import"./vue-Fz7C6K53.js";import{_ as G}from"./index-B6krP_Ey.js";import{f as H}from"./@vueuse-BwU3ZJgc.js";import{d as J,r as a,w as P,k as Q,c as l,o as n,a as t,O as z,L as U,P as r,K as c}from"./@vue-f6RsqvxV.js";import"./@amap-NgbnhGr0.js";import"./nprogress-BfkAby1e.js";import"./vue-router-ByGV_9Pg.js";import"./pinia-C_67xuHx.js";import"./vue-demi-BrIuzOLu.js";import"./axios-DHr9lByg.js";import"./lodash-DrdanVtZ.js";import"./element-plus-lDmxn7oP.js";import"./@element-plus-VXQHkdO3.js";import"./lodash-es-DEZIFt8k.js";import"./dayjs-BWOtif3e.js";import"./@popperjs-D_chPuIy.js";import"./async-validator-D3FX-SGt.js";import"./@ctrl-r5W6hzzQ.js";import"./normalize-wheel-es-BQoi3Ox2.js";import"./css-color-function-DEEjFEOq.js";import"./color-B0v57BL7.js";import"./clone-CuIhj1wH.js";import"./color-convert-BGgJB5UM.js";import"./color-name-BQ5IbGbl.js";import"./color-string-BhgG7-8u.js";import"./balanced-match-D4yLO45F.js";import"./ms-CzQ2E3wO.js";import"./vue-clipboard3-DHkbfTbJ.js";import"./clipboard-BHLzaSUg.js";import"./echarts-BiZprfkC.js";import"./tslib-BDyQ-Jie.js";import"./zrender-fZOP1A-7.js";import"./highlight.js-Dw-sv6H4.js";import"./@highlightjs-rkmszhNh.js";const W=J({__name:"CoreMetricsOverview",props:{filterData:{type:Object,default:()=>({})},coreMetricsData:{type:Object,default:null}},emits:["show-ai-analysis-popup"],setup(k,{expose:i,emit:g}){i();const e=k,m=g,s=a(!1),d=a({value:0,trend:0}),y=a({value:0,trend:0}),v=a({value:0,trend:0}),p=a({value:0,trend:0}),u=a({value:0,trend:0}),f=a({value:0,trend:0}),C=a({value:0,trend:0}),_=a({value:0,trend:0}),E=o=>new Intl.NumberFormat().format(o),K=o=>o>=90?"excellent":o>=75?"good":o>=60?"average":o>=40?"warning":"danger",q=H(async()=>{console.log("CoreMetricsOverview: 已禁用独立请求，需要父组件提供数据"),s.value=!0},300);P(()=>e.coreMetricsData,o=>{var M,T,b,O,D,N,x,A,h,I,j,w,B,L,F,S;console.log("CoreMetricsOverview: props.coreMetricsData 变更:",o),o?(console.log("CoreMetricsOverview: 接收到父组件提供的数据",o),s.value=!1,d.value={value:((M=o.totalTickets)==null?void 0:M.value)||0,trend:((T=o.totalTickets)==null?void 0:T.trend)||0},y.value={value:((b=o.companyCount)==null?void 0:b.value)||0,trend:((O=o.companyCount)==null?void 0:O.trend)||0},v.value={value:((D=o.completedTickets)==null?void 0:D.value)||0,trend:((N=o.completedTickets)==null?void 0:N.trend)||0},p.value={value:((x=o.completedRate)==null?void 0:x.value)||0,trend:((A=o.completedRate)==null?void 0:A.trend)||0},u.value={value:((h=o.timeoutTickets)==null?void 0:h.value)||0,trend:((I=o.timeoutTickets)==null?void 0:I.trend)||0},f.value={value:((j=o.timeoutRate)==null?void 0:j.value)||0,trend:((w=o.timeoutRate)==null?void 0:w.trend)||0},C.value={value:((B=o.complaintCount)==null?void 0:B.value)||0,trend:((L=o.complaintCount)==null?void 0:L.trend)||0},_.value={value:((F=o.complaintRate)==null?void 0:F.value)||0,trend:((S=o.complaintRate)==null?void 0:S.trend)||0}):(console.log("CoreMetricsOverview: 等待父组件提供数据"),s.value=!0)},{immediate:!0,deep:!0}),P(()=>e.filterData,()=>{console.log("CoreMetricsOverview: 过滤条件变化，但不再发起独立请求")},{deep:!0}),Q(()=>{console.log("CoreMetricsOverview: 组件挂载，仅使用父组件提供的数据"),e.coreMetricsData||(s.value=!0,console.log("CoreMetricsOverview: 父组件未提供数据，显示加载状态"))});const R={props:e,emit:m,isLoading:s,totalTickets:d,companyCount:y,completedTickets:v,completedRate:p,timeoutTickets:u,timeoutRate:f,complaintCount:C,complaintRate:_,formatNumber:E,getRateColorClass:K,debouncedFetchData:q,handleAIAnalysis:()=>{const o={coreMetrics:{totalTickets:d.value,completedTickets:v.value,timeoutTickets:u.value,complaintCount:C.value,completedRate:p.value,timeoutRate:f.value,complaintRate:_.value},filters:e.filterData};m("show-ai-analysis-popup",{title:"AI 核心指标分析",content:"",analysisType:"core_metrics",analysisData:o})}};return Object.defineProperty(R,"__isScriptSetup",{enumerable:!1,value:!0}),R}}),X={class:"core-metrics-overview"},Y={class:"metrics-title"},Z={key:0,class:"loading-overlay"},V={key:1,class:"metrics-grid"},$={class:"metric-card"},tt={class:"metric-data"},et={class:"metric-value"},ot={class:"metric-card"},it={class:"metric-data"},at={class:"metric-value"},rt={class:"metric-card"},st={class:"metric-data"},ct={class:"metric-value"},lt={class:"metric-card"},nt={class:"metric-data"},mt={class:"metric-value"};function dt(k,i,g,e,m,s){return n(),l("div",X,[t("h3",Y,[i[1]||(i[1]=z(" 核心指标总览 ")),e.isLoading?U("",!0):(n(),l("button",{key:0,class:"ai-analysis-btn",onClick:e.handleAIAnalysis},i[0]||(i[0]=[t("i",{class:"el-icon-magic-stick"},null,-1),z(" AI分析 ")])))]),e.isLoading?(n(),l("div",Z,i[2]||(i[2]=[t("div",{class:"loading-spinner"},[t("svg",{viewBox:"0 0 50 50",class:"spinner-icon"},[t("circle",{cx:"25",cy:"25",r:"20",fill:"none","stroke-width":"4","stroke-linecap":"round"})]),t("span",null,"加载中...")],-1)]))):(n(),l("div",V,[t("div",$,[i[4]||(i[4]=t("div",{class:"metric-icon total-icon"},[t("i",{class:"el-icon-tickets"})],-1)),t("div",tt,[t("div",et,r(e.formatNumber(e.totalTickets.value)),1),i[3]||(i[3]=t("div",{class:"metric-name"},"累计工单总量",-1)),t("div",{class:c(["metric-trend",{positive:e.companyCount.trend>0,negative:e.companyCount.trend<0}])},[t("i",{class:c(e.companyCount.trend>0?"el-icon-top":"el-icon-bottom")},null,2),t("span",null,"单位工单率 "+r(Math.abs(e.companyCount.trend))+"%",1)],2)])]),t("div",ot,[i[6]||(i[6]=t("div",{class:"metric-icon completed-icon"},[t("i",{class:"el-icon-check"})],-1)),t("div",it,[t("div",at,r(e.formatNumber(e.completedTickets.value)),1),i[5]||(i[5]=t("div",{class:"metric-name"},"累计完成工单",-1)),t("div",{class:c(["metric-rate",e.getRateColorClass(e.completedRate.value)])}," 完成率 "+r(e.completedRate.value)+"% ",3)])]),t("div",rt,[i[8]||(i[8]=t("div",{class:"metric-icon timeout-icon"},[t("i",{class:"el-icon-time"})],-1)),t("div",st,[t("div",ct,r(e.formatNumber(e.timeoutTickets.value)),1),i[7]||(i[7]=t("div",{class:"metric-name"},"累计超时工单",-1)),t("div",{class:c(["metric-rate",e.getRateColorClass(100-e.timeoutRate.value)])}," 超时率 "+r(e.timeoutRate.value)+"% ",3)])]),t("div",lt,[i[10]||(i[10]=t("div",{class:"metric-icon complaint-icon"},[t("i",{class:"el-icon-warning"})],-1)),t("div",nt,[t("div",mt,r(e.formatNumber(e.complaintCount.value)),1),i[9]||(i[9]=t("div",{class:"metric-name"},"累计投诉工单",-1)),t("div",{class:c(["metric-rate",e.getRateColorClass(100-e.complaintRate.value)])}," 投诉率 "+r(e.complaintRate.value)+"% ",3)])])]))])}const Ut=G(W,[["render",dt],["__scopeId","data-v-74063395"],["__file","D:/work/project/huifa-ops-saas/tenant/src/views/data_screen/components/CoreMetricsOverview.vue"]]);export{Ut as default};
