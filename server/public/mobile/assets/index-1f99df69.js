var e=Object.defineProperty,t=(t,n,o)=>(((t,n,o)=>{n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[n]=o})(t,"symbol"!=typeof n?n+"":n,o),o);!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const n={},o=function(e,t,o){if(!t||0===t.length)return e();const r=document.getElementsByTagName("link");return Promise.all(t.map((e=>{if((e=function(e){return"/mobile/"+e}(e))in n)return;n[e]=!0;const t=e.endsWith(".css"),i=t?'[rel="stylesheet"]':"";if(!!o)for(let n=r.length-1;n>=0;n--){const o=r[n];if(o.href===e&&(!t||"stylesheet"===o.rel))return}else if(document.querySelector(`link[href="${e}"]${i}`))return;const a=document.createElement("link");return a.rel=t?"stylesheet":"modulepreload",t||(a.as="script",a.crossOrigin=""),a.href=e,document.head.appendChild(a),t?new Promise(((t,n)=>{a.addEventListener("load",t),a.addEventListener("error",(()=>n(new Error(`Unable to preload CSS for ${e}`))))})):void 0}))).then((()=>e()))};function r(e,t){const n=Object.create(null),o=e.split(",");for(let r=0;r<o.length;r++)n[o[r]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}function i(e){if(C(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=I(o)?c(o):i(o);if(r)for(const e in r)t[e]=r[e]}return t}return I(e)||P(e)?e:void 0}const a=/;(?![^(]*\))/g,s=/:([^]+)/,l=/\/\*.*?\*\//gs;function c(e){const t={};return e.replace(l,"").split(a).forEach((e=>{if(e){const n=e.split(s);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function u(e){let t="";if(I(e))t=e;else if(C(e))for(let n=0;n<e.length;n++){const o=u(e[n]);o&&(t+=o+" ")}else if(P(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const d=r("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function f(e){return!!e||""===e}const h=e=>I(e)?e:null==e?"":C(e)||P(e)&&(e.toString===L||!M(e.toString))?JSON.stringify(e,p,2):String(e),p=(e,t)=>t&&t.__v_isRef?p(e,t.value):E(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n])=>(e[`${t} =>`]=n,e)),{})}:O(t)?{[`Set(${t.size})`]:[...t.values()]}:!P(t)||C(t)||j(t)?t:String(t),g={},m=[],v=()=>{},y=()=>!1,b=/^on[^a-z]/,_=e=>b.test(e),w=e=>e.startsWith("onUpdate:"),x=Object.assign,T=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},k=Object.prototype.hasOwnProperty,S=(e,t)=>k.call(e,t),C=Array.isArray,E=e=>"[object Map]"===R(e),O=e=>"[object Set]"===R(e),M=e=>"function"==typeof e,I=e=>"string"==typeof e,A=e=>"symbol"==typeof e,P=e=>null!==e&&"object"==typeof e,$=e=>P(e)&&M(e.then)&&M(e.catch),L=Object.prototype.toString,R=e=>L.call(e),j=e=>"[object Object]"===R(e),B=e=>I(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,D=r(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),N=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},F=/-(\w)/g,V=N((e=>e.replace(F,((e,t)=>t?t.toUpperCase():"")))),q=/\B([A-Z])/g,W=N((e=>e.replace(q,"-$1").toLowerCase())),H=N((e=>e.charAt(0).toUpperCase()+e.slice(1))),z=N((e=>e?`on${H(e)}`:"")),U=(e,t)=>!Object.is(e,t),Y=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},X=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},G=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let J;const K=["ad","ad-content-page","ad-draw","audio","button","camera","canvas","checkbox","checkbox-group","cover-image","cover-view","editor","form","functional-page-navigator","icon","image","input","label","live-player","live-pusher","map","movable-area","movable-view","navigator","official-account","open-data","picker","picker-view","picker-view-column","progress","radio","radio-group","rich-text","scroll-view","slider","swiper","swiper-item","switch","text","textarea","video","view","web-view"].map((e=>"uni-"+e));const Z="\n",Q=["%","%"],ee=/^([a-z-]+:)?\/\//i,te=/^data:.*,.*/,ne="onShow",oe="onHide",re="onLaunch",ie="onError",ae="onThemeChange",se="onPageNotFound",le="onUnhandledRejection",ce="onLoad",ue="onUnload",de="onInit",fe="onSaveExitState",he="onResize",pe="onBackPress",ge="onPageScroll",me="onTabItemTap",ve="onReachBottom",ye="onPullDownRefresh",be="onShareTimeline",_e="onAddToFavorites",we="onShareAppMessage",xe="onNavigationBarButtonTap",Te="onNavigationBarSearchInputClicked",ke="onNavigationBarSearchInputChanged",Se="onNavigationBarSearchInputConfirmed",Ce="onNavigationBarSearchInputFocusChanged",Ee="onAppEnterForeground",Oe="onAppEnterBackground",Me="onWebInvokeAppService";function Ie(e){return e&&(e.appContext?e.proxy:e)}function Ae(e){if(!e)return;let t=e.type.name;for(;t&&(n=W(t),-1!==K.indexOf("uni-"+n.replace("v-uni-","")));)t=(e=e.parent).type.name;var n;return e.proxy}function Pe(e){return 1===e.nodeType}function $e(e){return 0===e.indexOf("/")}function Le(e){return $e(e)?e:"/"+e}function Re(e){return $e(e)?e.slice(1):e}function je(e,t){for(const n in t)e.style[n]=t[n]}function Be(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}const De=e=>e>9?e:"0"+e;function Ne({date:e=new Date,mode:t="date"}){return"time"===t?De(e.getHours())+":"+De(e.getMinutes()):e.getFullYear()+"-"+De(e.getMonth()+1)+"-"+De(e.getDate())}function Fe(e){return V(e.substring(5))}const Ve=Be((()=>{const e=HTMLElement.prototype,t=e.setAttribute;e.setAttribute=function(e,n){if(e.startsWith("data-")&&this.tagName.startsWith("UNI-")){(this.__uniDataset||(this.__uniDataset={}))[Fe(e)]=n}t.call(this,e,n)};const n=e.removeAttribute;e.removeAttribute=function(e){this.__uniDataset&&e.startsWith("data-")&&this.tagName.startsWith("UNI-")&&delete this.__uniDataset[Fe(e)],n.call(this,e)}}));function qe(e){return x({},e.dataset,e.__uniDataset)}const We=new RegExp("\"[^\"]+\"|'[^']+'|url\\([^)]+\\)|(\\d*\\.?\\d+)[r|u]px","g");function He(e){return{passive:e}}function ze(e){const{id:t,offsetTop:n,offsetLeft:o}=e;return{id:t,dataset:qe(e),offsetTop:n,offsetLeft:o}}function Ue(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function Ye(e={}){const t={};return Object.keys(e).forEach((n=>{try{t[n]=Ue(e[n])}catch(zE){t[n]=e[n]}})),t}const Xe=/\+/g;function Ge(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(Xe," ");let r=e.indexOf("="),i=Ue(r<0?e:e.slice(0,r)),a=r<0?null:Ue(e.slice(r+1));if(i in t){let e=t[i];C(e)||(e=t[i]=[e]),e.push(a)}else t[i]=a}return t}function Je(e,t,{clearTimeout:n,setTimeout:o}){let r;const i=function(){n(r);r=o((()=>e.apply(this,arguments)),t)};return i.cancel=function(){n(r)},i}class Ke{constructor(e,t){this.id=e,this.listener={},this.emitCache=[],t&&Object.keys(t).forEach((e=>{this.on(e,t[e])}))}emit(e,...t){const n=this.listener[e];if(!n)return this.emitCache.push({eventName:e,args:t});n.forEach((e=>{e.fn.apply(e.fn,t)})),this.listener[e]=n.filter((e=>"once"!==e.type))}on(e,t){this._addListener(e,"on",t),this._clearCache(e)}once(e,t){this._addListener(e,"once",t),this._clearCache(e)}off(e,t){const n=this.listener[e];if(n)if(t)for(let o=0;o<n.length;)n[o].fn===t&&(n.splice(o,1),o--),o++;else delete this.listener[e]}_clearCache(e){for(let t=0;t<this.emitCache.length;t++){const n=this.emitCache[t],o=e?n.eventName===e?e:null:n.eventName;if(!o)continue;"number"!=typeof this.emit.apply(this,[o,...n.args])?(this.emitCache.splice(t,1),t--):this.emitCache.pop()}}_addListener(e,t,n){(this.listener[e]||(this.listener[e]=[])).push({fn:n,type:t})}}const Ze=[de,ce,ne,oe,ue,pe,ge,me,ve,ye,be,we,_e,fe,xe,Te,ke,Se,Ce],Qe=[ce,ne];const et=[ne,oe,re,ie,ae,se,le,de,ce,"onReady",ue,he,pe,ge,me,ve,ye,be,_e,we,fe,xe,Te,ke,Se,Ce];const tt=[];const nt=Be(((e,t)=>{if(M(e._component.onError))return t(e)})),ot=function(){};ot.prototype={on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n}),this},once:function(e,t,n){var o=this;function r(){o.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,r=n.length;o<r;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],r=[];if(o&&t)for(var i=0,a=o.length;i<a;i++)o[i].fn!==t&&o[i].fn._!==t&&r.push(o[i]);return r.length?n[e]=r:delete n[e],this}};var rt=ot;const it={black:"rgba(0,0,0,0.4)",white:"rgba(255,255,255,0.4)"};function at(e,t={},n="light"){const o=t[n],r={};return o?(Object.keys(e).forEach((i=>{let a=e[i];r[i]=(()=>{if(j(a))return at(a,t,n);if(C(a))return a.map((e=>j(e)?at(e,t,n):e));if(I(a)&&a.startsWith("@")){const t=a.replace("@","");let n=o[t]||a;switch(i){case"titleColor":n="black"===n?"#000000":"#ffffff";break;case"borderStyle":n=(e=n)&&e in it?it[e]:e}return n}var e;return a})()})),r):e}let st;class lt{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=st,!e&&st&&(this.index=(st.scopes||(st.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=st;try{return st=this,e()}finally{st=t}}}on(){st=this}off(){st=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function ct(e){return new lt(e)}const ut=e=>{const t=new Set(e);return t.w=0,t.n=0,t},dt=e=>(e.w&gt)>0,ft=e=>(e.n&gt)>0,ht=new WeakMap;let pt=0,gt=1;let mt;const vt=Symbol(""),yt=Symbol("");class bt{constructor(e,t=null,n){this.fn=e,this.scheduler=t,this.active=!0,this.deps=[],this.parent=void 0,function(e,t=st){t&&t.active&&t.effects.push(e)}(this,n)}run(){if(!this.active)return this.fn();let e=mt,t=wt;for(;e;){if(e===this)return;e=e.parent}try{return this.parent=mt,mt=this,wt=!0,gt=1<<++pt,pt<=30?(({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=gt})(this):_t(this),this.fn()}finally{pt<=30&&(e=>{const{deps:t}=e;if(t.length){let n=0;for(let o=0;o<t.length;o++){const r=t[o];dt(r)&&!ft(r)?r.delete(e):t[n++]=r,r.w&=~gt,r.n&=~gt}t.length=n}})(this),gt=1<<--pt,mt=this.parent,wt=t,this.parent=void 0,this.deferStop&&this.stop()}}stop(){mt===this?this.deferStop=!0:this.active&&(_t(this),this.onStop&&this.onStop(),this.active=!1)}}function _t(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let wt=!0;const xt=[];function Tt(){xt.push(wt),wt=!1}function kt(){const e=xt.pop();wt=void 0===e||e}function St(e,t,n){if(wt&&mt){let t=ht.get(e);t||ht.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=ut()),Ct(o)}}function Ct(e,t){let n=!1;pt<=30?ft(e)||(e.n|=gt,n=!dt(e)):n=!e.has(mt),n&&(e.add(mt),mt.deps.push(e))}function Et(e,t,n,o,r,i){const a=ht.get(e);if(!a)return;let s=[];if("clear"===t)s=[...a.values()];else if("length"===n&&C(e)){const e=Number(o);a.forEach(((t,n)=>{("length"===n||n>=e)&&s.push(t)}))}else switch(void 0!==n&&s.push(a.get(n)),t){case"add":C(e)?B(n)&&s.push(a.get("length")):(s.push(a.get(vt)),E(e)&&s.push(a.get(yt)));break;case"delete":C(e)||(s.push(a.get(vt)),E(e)&&s.push(a.get(yt)));break;case"set":E(e)&&s.push(a.get(vt))}if(1===s.length)s[0]&&Ot(s[0]);else{const e=[];for(const t of s)t&&e.push(...t);Ot(ut(e))}}function Ot(e,t){const n=C(e)?e:[...e];for(const o of n)o.computed&&Mt(o);for(const o of n)o.computed||Mt(o)}function Mt(e,t){(e!==mt||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}const It=r("__proto__,__v_isRef,__isVue"),At=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(A)),Pt=Dt(),$t=Dt(!1,!0),Lt=Dt(!0),Rt=jt();function jt(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=kn(this);for(let t=0,r=this.length;t<r;t++)St(n,0,t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(kn)):o}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){Tt();const n=kn(this)[t].apply(this,e);return kt(),n}})),e}function Bt(e){const t=kn(this);return St(t,0,e),t.hasOwnProperty(e)}function Dt(e=!1,t=!1){return function(n,o,r){if("__v_isReactive"===o)return!e;if("__v_isReadonly"===o)return e;if("__v_isShallow"===o)return t;if("__v_raw"===o&&r===(e?t?pn:hn:t?fn:dn).get(n))return n;const i=C(n);if(!e){if(i&&S(Rt,o))return Reflect.get(Rt,o,r);if("hasOwnProperty"===o)return Bt}const a=Reflect.get(n,o,r);return(A(o)?At.has(o):It(o))?a:(e||St(n,0,o),t?a:In(a)?i&&B(o)?a:a.value:P(a)?e?yn(a):mn(a):a)}}function Nt(e=!1){return function(t,n,o,r){let i=t[n];if(wn(i)&&In(i)&&!In(o))return!1;if(!e&&(xn(o)||wn(o)||(i=kn(i),o=kn(o)),!C(t)&&In(i)&&!In(o)))return i.value=o,!0;const a=C(t)&&B(n)?Number(n)<t.length:S(t,n),s=Reflect.set(t,n,o,r);return t===kn(r)&&(a?U(o,i)&&Et(t,"set",n,o):Et(t,"add",n,o)),s}}const Ft={get:Pt,set:Nt(),deleteProperty:function(e,t){const n=S(e,t);e[t];const o=Reflect.deleteProperty(e,t);return o&&n&&Et(e,"delete",t,void 0),o},has:function(e,t){const n=Reflect.has(e,t);return A(t)&&At.has(t)||St(e,0,t),n},ownKeys:function(e){return St(e,0,C(e)?"length":vt),Reflect.ownKeys(e)}},Vt={get:Lt,set:(e,t)=>!0,deleteProperty:(e,t)=>!0},qt=x({},Ft,{get:$t,set:Nt(!0)}),Wt=e=>e,Ht=e=>Reflect.getPrototypeOf(e);function zt(e,t,n=!1,o=!1){const r=kn(e=e.__v_raw),i=kn(t);n||(t!==i&&St(r,0,t),St(r,0,i));const{has:a}=Ht(r),s=o?Wt:n?En:Cn;return a.call(r,t)?s(e.get(t)):a.call(r,i)?s(e.get(i)):void(e!==r&&e.get(t))}function Ut(e,t=!1){const n=this.__v_raw,o=kn(n),r=kn(e);return t||(e!==r&&St(o,0,e),St(o,0,r)),e===r?n.has(e):n.has(e)||n.has(r)}function Yt(e,t=!1){return e=e.__v_raw,!t&&St(kn(e),0,vt),Reflect.get(e,"size",e)}function Xt(e){e=kn(e);const t=kn(this);return Ht(t).has.call(t,e)||(t.add(e),Et(t,"add",e,e)),this}function Gt(e,t){t=kn(t);const n=kn(this),{has:o,get:r}=Ht(n);let i=o.call(n,e);i||(e=kn(e),i=o.call(n,e));const a=r.call(n,e);return n.set(e,t),i?U(t,a)&&Et(n,"set",e,t):Et(n,"add",e,t),this}function Jt(e){const t=kn(this),{has:n,get:o}=Ht(t);let r=n.call(t,e);r||(e=kn(e),r=n.call(t,e)),o&&o.call(t,e);const i=t.delete(e);return r&&Et(t,"delete",e,void 0),i}function Kt(){const e=kn(this),t=0!==e.size,n=e.clear();return t&&Et(e,"clear",void 0,void 0),n}function Zt(e,t){return function(n,o){const r=this,i=r.__v_raw,a=kn(i),s=t?Wt:e?En:Cn;return!e&&St(a,0,vt),i.forEach(((e,t)=>n.call(o,s(e),s(t),r)))}}function Qt(e,t,n){return function(...o){const r=this.__v_raw,i=kn(r),a=E(i),s="entries"===e||e===Symbol.iterator&&a,l="keys"===e&&a,c=r[e](...o),u=n?Wt:t?En:Cn;return!t&&St(i,0,l?yt:vt),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:s?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function en(e){return function(...t){return"delete"!==e&&this}}function tn(){const e={get(e){return zt(this,e)},get size(){return Yt(this)},has:Ut,add:Xt,set:Gt,delete:Jt,clear:Kt,forEach:Zt(!1,!1)},t={get(e){return zt(this,e,!1,!0)},get size(){return Yt(this)},has:Ut,add:Xt,set:Gt,delete:Jt,clear:Kt,forEach:Zt(!1,!0)},n={get(e){return zt(this,e,!0)},get size(){return Yt(this,!0)},has(e){return Ut.call(this,e,!0)},add:en("add"),set:en("set"),delete:en("delete"),clear:en("clear"),forEach:Zt(!0,!1)},o={get(e){return zt(this,e,!0,!0)},get size(){return Yt(this,!0)},has(e){return Ut.call(this,e,!0)},add:en("add"),set:en("set"),delete:en("delete"),clear:en("clear"),forEach:Zt(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((r=>{e[r]=Qt(r,!1,!1),n[r]=Qt(r,!0,!1),t[r]=Qt(r,!1,!0),o[r]=Qt(r,!0,!0)})),[e,n,t,o]}const[nn,on,rn,an]=tn();function sn(e,t){const n=t?e?an:rn:e?on:nn;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(S(n,o)&&o in t?n:t,o,r)}const ln={get:sn(!1,!1)},cn={get:sn(!1,!0)},un={get:sn(!0,!1)},dn=new WeakMap,fn=new WeakMap,hn=new WeakMap,pn=new WeakMap;function gn(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>R(e).slice(8,-1))(e))}function mn(e){return wn(e)?e:bn(e,!1,Ft,ln,dn)}function vn(e){return bn(e,!1,qt,cn,fn)}function yn(e){return bn(e,!0,Vt,un,hn)}function bn(e,t,n,o,r){if(!P(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const a=gn(e);if(0===a)return e;const s=new Proxy(e,2===a?o:n);return r.set(e,s),s}function _n(e){return wn(e)?_n(e.__v_raw):!(!e||!e.__v_isReactive)}function wn(e){return!(!e||!e.__v_isReadonly)}function xn(e){return!(!e||!e.__v_isShallow)}function Tn(e){return _n(e)||wn(e)}function kn(e){const t=e&&e.__v_raw;return t?kn(t):e}function Sn(e){return X(e,"__v_skip",!0),e}const Cn=e=>P(e)?mn(e):e,En=e=>P(e)?yn(e):e;function On(e){wt&&mt&&Ct((e=kn(e)).dep||(e.dep=ut()))}function Mn(e,t){const n=(e=kn(e)).dep;n&&Ot(n)}function In(e){return!(!e||!0!==e.__v_isRef)}function An(e){return $n(e,!1)}function Pn(e){return $n(e,!0)}function $n(e,t){return In(e)?e:new Ln(e,t)}class Ln{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:kn(e),this._value=t?e:Cn(e)}get value(){return On(this),this._value}set value(e){const t=this.__v_isShallow||xn(e)||wn(e);e=t?e:kn(e),U(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:Cn(e),Mn(this))}}function Rn(e){return In(e)?e.value:e}const jn={get:(e,t,n)=>Rn(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return In(r)&&!In(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function Bn(e){return _n(e)?e:new Proxy(e,jn)}class Dn{constructor(e,t,n){this._object=e,this._key=t,this._defaultValue=n,this.__v_isRef=!0}get value(){const e=this._object[this._key];return void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return function(e,t){var n;return null===(n=ht.get(e))||void 0===n?void 0:n.get(t)}(kn(this._object),this._key)}}function Nn(e,t,n){const o=e[t];return In(o)?o:new Dn(e,t,n)}var Fn;class Vn{constructor(e,t,n,o){this._setter=t,this.dep=void 0,this.__v_isRef=!0,this[Fn]=!1,this._dirty=!0,this.effect=new bt(e,(()=>{this._dirty||(this._dirty=!0,Mn(this))})),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=kn(this);return On(e),!e._dirty&&e._cacheable||(e._dirty=!1,e._value=e.effect.run()),e._value}set value(e){this._setter(e)}}function qn(e,t,n,o){let r;try{r=o?e(...o):e()}catch(i){Hn(i,t,n)}return r}function Wn(e,t,n,o){if(M(e)){const r=qn(e,t,n,o);return r&&$(r)&&r.catch((e=>{Hn(e,t,n)})),r}const r=[];for(let i=0;i<e.length;i++)r.push(Wn(e[i],t,n,o));return r}function Hn(e,t,n,o=!0){t&&t.vnode;if(t){let o=t.parent;const r=t.proxy,i=n;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;o=o.parent}const a=t.appContext.config.errorHandler;if(a)return void qn(a,null,10,[e,r,i])}!function(e){console.error(e)}(e,0,0,o)}Fn="__v_isReadonly";let zn=!1,Un=!1;const Yn=[];let Xn=0;const Gn=[];let Jn=null,Kn=0;const Zn=Promise.resolve();let Qn=null;function eo(e){const t=Qn||Zn;return e?t.then(this?e.bind(this):e):t}function to(e){Yn.length&&Yn.includes(e,zn&&e.allowRecurse?Xn+1:Xn)||(null==e.id?Yn.push(e):Yn.splice(function(e){let t=Xn+1,n=Yn.length;for(;t<n;){const o=t+n>>>1;io(Yn[o])<e?t=o+1:n=o}return t}(e.id),0,e),no())}function no(){zn||Un||(Un=!0,Qn=Zn.then(so))}function oo(e,t=(zn?Xn+1:0)){for(;t<Yn.length;t++){const e=Yn[t];e&&e.pre&&(Yn.splice(t,1),t--,e())}}function ro(e){if(Gn.length){const e=[...new Set(Gn)];if(Gn.length=0,Jn)return void Jn.push(...e);for(Jn=e,Jn.sort(((e,t)=>io(e)-io(t))),Kn=0;Kn<Jn.length;Kn++)Jn[Kn]();Jn=null,Kn=0}}const io=e=>null==e.id?1/0:e.id,ao=(e,t)=>{const n=io(e)-io(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function so(e){Un=!1,zn=!0,Yn.sort(ao);try{for(Xn=0;Xn<Yn.length;Xn++){const e=Yn[Xn];e&&!1!==e.active&&qn(e,null,14)}}finally{Xn=0,Yn.length=0,ro(),zn=!1,Qn=null,(Yn.length||Gn.length)&&so()}}function lo(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||g;let r=n;const i=t.startsWith("update:"),a=i&&t.slice(7);if(a&&a in o){const e=`${"modelValue"===a?"model":a}Modifiers`,{number:t,trim:i}=o[e]||g;i&&(r=n.map((e=>I(e)?e.trim():e))),t&&(r=n.map(G))}let s,l=o[s=z(t)]||o[s=z(V(t))];!l&&i&&(l=o[s=z(W(t))]),l&&Wn(l,e,6,co(e,l,r));const c=o[s+"Once"];if(c){if(e.emitted){if(e.emitted[s])return}else e.emitted={};e.emitted[s]=!0,Wn(c,e,6,co(e,c,r))}}function co(e,t,n){if(1!==n.length)return n;if(M(t)){if(t.length<2)return n}else if(!t.find((e=>e.length>=2)))return n;const o=n[0];if(o&&S(o,"type")&&S(o,"timeStamp")&&S(o,"target")&&S(o,"currentTarget")&&S(o,"detail")){const t=e.proxy,o=t.$gcd(t,!0);o&&n.push(o)}return n}function uo(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const i=e.emits;let a={},s=!1;if(!M(e)){const o=e=>{const n=uo(e,t,!0);n&&(s=!0,x(a,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return i||s?(C(i)?i.forEach((e=>a[e]=null)):x(a,i),P(e)&&o.set(e,a),a):(P(e)&&o.set(e,null),null)}function fo(e,t){return!(!e||!_(t))&&(t=t.slice(2).replace(/Once$/,""),S(e,t[0].toLowerCase()+t.slice(1))||S(e,W(t))||S(e,t))}let ho=null,po=null;function go(e){const t=ho;return ho=e,po=e&&e.type.__scopeId||null,t}function mo(e,t=ho,n){if(!t)return e;if(e._n)return e;const o=(...n)=>{o._d&&vi(-1);const r=go(t);let i;try{i=e(...n)}finally{go(r),o._d&&vi(1)}return i};return o._n=!0,o._c=!0,o._d=!0,o}function vo(e){const{type:t,vnode:n,proxy:o,withProxy:r,props:i,propsOptions:[a],slots:s,attrs:l,emit:c,render:u,renderCache:d,data:f,setupState:h,ctx:p,inheritAttrs:g}=e;let m,v;const y=go(e);try{if(4&n.shapeFlag){const e=r||o;m=Ai(u.call(e,e,d,i,h,f,p)),v=l}else{const e=t;0,m=Ai(e.length>1?e(i,{attrs:l,slots:s,emit:c}):e(i,null)),v=t.props?l:yo(l)}}catch(_){hi.length=0,Hn(_,e,1),m=Ei(di)}let b=m;if(v&&!1!==g){const e=Object.keys(v),{shapeFlag:t}=b;e.length&&7&t&&(a&&e.some(w)&&(v=bo(v,a)),b=Oi(b,v))}return n.dirs&&(b=Oi(b),b.dirs=b.dirs?b.dirs.concat(n.dirs):n.dirs),n.transition&&(b.transition=n.transition),m=b,go(y),m}const yo=e=>{let t;for(const n in e)("class"===n||"style"===n||_(n))&&((t||(t={}))[n]=e[n]);return t},bo=(e,t)=>{const n={};for(const o in e)w(o)&&o.slice(9)in t||(n[o]=e[o]);return n};function _o(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const i=o[r];if(t[i]!==e[i]&&!fo(n,i))return!0}return!1}const wo=e=>e.__isSuspense;function xo(e,t){if(Di){let n=Di.provides;const o=Di.parent&&Di.parent.provides;o===n&&(n=Di.provides=Object.create(o)),n[e]=t,"app"===Di.type.mpType&&Di.appContext.app.provide(e,t)}else;}function To(e,t,n=!1){const o=Di||ho;if(o){const r=null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&M(t)?t.call(o.proxy):t}}function ko(e,t){return Eo(e,null,t)}const So={};function Co(e,t,n){return Eo(e,t,n)}function Eo(e,t,{immediate:n,deep:o,flush:r,onTrack:i,onTrigger:a}=g){const s=st===(null==Di?void 0:Di.scope)?Di:null;let l,c,u=!1,d=!1;if(In(e)?(l=()=>e.value,u=xn(e)):_n(e)?(l=()=>e,o=!0):C(e)?(d=!0,u=e.some((e=>_n(e)||xn(e))),l=()=>e.map((e=>In(e)?e.value:_n(e)?Io(e):M(e)?qn(e,s,2):void 0))):l=M(e)?t?()=>qn(e,s,2):()=>{if(!s||!s.isUnmounted)return c&&c(),Wn(e,s,3,[h])}:v,t&&o){const e=l;l=()=>Io(e())}let f,h=e=>{c=b.onStop=()=>{qn(e,s,4)}};if(Hi){if(h=v,t?n&&Wn(t,s,3,[l(),d?[]:void 0,h]):l(),"sync"!==r)return v;{const e=Zi();f=e.__watcherHandles||(e.__watcherHandles=[])}}let p=d?new Array(e.length).fill(So):So;const m=()=>{if(b.active)if(t){const e=b.run();(o||u||(d?e.some(((e,t)=>U(e,p[t]))):U(e,p)))&&(c&&c(),Wn(t,s,3,[e,p===So?void 0:d&&p[0]===So?[]:p,h]),p=e)}else b.run()};let y;m.allowRecurse=!!t,"sync"===r?y=m:"post"===r?y=()=>ii(m,s&&s.suspense):(m.pre=!0,s&&(m.id=s.uid),y=()=>to(m));const b=new bt(l,y);t?n?m():p=b.run():"post"===r?ii(b.run.bind(b),s&&s.suspense):b.run();const _=()=>{b.stop(),s&&s.scope&&T(s.scope.effects,b)};return f&&f.push(_),_}function Oo(e,t,n){const o=this.proxy,r=I(e)?e.includes(".")?Mo(o,e):()=>o[e]:e.bind(o,o);let i;M(t)?i=t:(i=t.handler,n=t);const a=Di;Fi(this);const s=Eo(r,i.bind(o),n);return a?Fi(a):Vi(),s}function Mo(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function Io(e,t){if(!P(e)||e.__v_skip)return e;if((t=t||new Set).has(e))return e;if(t.add(e),In(e))Io(e.value,t);else if(C(e))for(let n=0;n<e.length;n++)Io(e[n],t);else if(O(e)||E(e))e.forEach((e=>{Io(e,t)}));else if(j(e))for(const n in e)Io(e[n],t);return e}const Ao=[Function,Array],Po={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Ao,onEnter:Ao,onAfterEnter:Ao,onEnterCancelled:Ao,onBeforeLeave:Ao,onLeave:Ao,onAfterLeave:Ao,onLeaveCancelled:Ao,onBeforeAppear:Ao,onAppear:Ao,onAfterAppear:Ao,onAppearCancelled:Ao},$o={name:"BaseTransition",props:Po,setup(e,{slots:t}){const n=Ni(),o=function(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return lr((()=>{e.isMounted=!0})),dr((()=>{e.isUnmounting=!0})),e}();let r;return()=>{const i=t.default&&Fo(t.default(),!0);if(!i||!i.length)return;let a=i[0];if(i.length>1)for(const e of i)if(e.type!==di){a=e;break}const s=kn(e),{mode:l}=s;if(o.isLeaving)return Bo(a);const c=Do(a);if(!c)return Bo(a);const u=jo(c,s,o,n);No(c,u);const d=n.subTree,f=d&&Do(d);let h=!1;const{getTransitionKey:p}=c.type;if(p){const e=p();void 0===r?r=e:e!==r&&(r=e,h=!0)}if(f&&f.type!==di&&(!xi(c,f)||h)){const e=jo(f,s,o,n);if(No(f,e),"out-in"===l)return o.isLeaving=!0,e.afterLeave=()=>{o.isLeaving=!1,!1!==n.update.active&&n.update()},Bo(a);"in-out"===l&&c.type!==di&&(e.delayLeave=(e,t,n)=>{Ro(o,f)[String(f.key)]=f,e._leaveCb=()=>{t(),e._leaveCb=void 0,delete u.delayedLeave},u.delayedLeave=n})}return a}}},Lo=$o;function Ro(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function jo(e,t,n,o){const{appear:r,mode:i,persisted:a=!1,onBeforeEnter:s,onEnter:l,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:d,onLeave:f,onAfterLeave:h,onLeaveCancelled:p,onBeforeAppear:g,onAppear:m,onAfterAppear:v,onAppearCancelled:y}=t,b=String(e.key),_=Ro(n,e),w=(e,t)=>{e&&Wn(e,o,9,t)},x=(e,t)=>{const n=t[1];w(e,t),C(e)?e.every((e=>e.length<=1))&&n():e.length<=1&&n()},T={mode:i,persisted:a,beforeEnter(t){let o=s;if(!n.isMounted){if(!r)return;o=g||s}t._leaveCb&&t._leaveCb(!0);const i=_[b];i&&xi(e,i)&&i.el._leaveCb&&i.el._leaveCb(),w(o,[t])},enter(e){let t=l,o=c,i=u;if(!n.isMounted){if(!r)return;t=m||l,o=v||c,i=y||u}let a=!1;const s=e._enterCb=t=>{a||(a=!0,w(t?i:o,[e]),T.delayedLeave&&T.delayedLeave(),e._enterCb=void 0)};t?x(t,[e,s]):s()},leave(t,o){const r=String(e.key);if(t._enterCb&&t._enterCb(!0),n.isUnmounting)return o();w(d,[t]);let i=!1;const a=t._leaveCb=n=>{i||(i=!0,o(),w(n?p:h,[t]),t._leaveCb=void 0,_[r]===e&&delete _[r])};_[r]=e,f?x(f,[t,a]):a()},clone:e=>jo(e,t,n,o)};return T}function Bo(e){if(zo(e))return(e=Oi(e)).children=null,e}function Do(e){return zo(e)?e.children?e.children[0]:void 0:e}function No(e,t){6&e.shapeFlag&&e.component?No(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Fo(e,t=!1,n){let o=[],r=0;for(let i=0;i<e.length;i++){let a=e[i];const s=null==n?a.key:String(n)+String(null!=a.key?a.key:i);a.type===ci?(128&a.patchFlag&&r++,o=o.concat(Fo(a.children,t,s))):(t||a.type!==di)&&o.push(null!=s?Oi(a,{key:s}):a)}if(r>1)for(let i=0;i<o.length;i++)o[i].patchFlag=-2;return o}function Vo(e){return M(e)?{setup:e,name:e.name}:e}const qo=e=>!!e.type.__asyncLoader;function Wo(e){M(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:o,delay:r=200,timeout:i,suspensible:a=!0,onError:s}=e;let l,c=null,u=0;const d=()=>{let e;return c||(e=c=t().catch((e=>{if(e=e instanceof Error?e:new Error(String(e)),s)return new Promise(((t,n)=>{s(e,(()=>t((u++,c=null,d()))),(()=>n(e)),u+1)}));throw e})).then((t=>e!==c&&c?c:(t&&(t.__esModule||"Module"===t[Symbol.toStringTag])&&(t=t.default),l=t,t))))};return Vo({name:"AsyncComponentWrapper",__asyncLoader:d,get __asyncResolved(){return l},setup(){const e=Di;if(l)return()=>Ho(l,e);const t=t=>{c=null,Hn(t,e,13,!o)};if(a&&e.suspense||Hi)return d().then((t=>()=>Ho(t,e))).catch((e=>(t(e),()=>o?Ei(o,{error:e}):null)));const s=An(!1),u=An(),f=An(!!r);return r&&setTimeout((()=>{f.value=!1}),r),null!=i&&setTimeout((()=>{if(!s.value&&!u.value){const e=new Error(`Async component timed out after ${i}ms.`);t(e),u.value=e}}),i),d().then((()=>{s.value=!0,e.parent&&zo(e.parent.vnode)&&to(e.parent.update)})).catch((e=>{t(e),u.value=e})),()=>s.value&&l?Ho(l,e):u.value&&o?Ei(o,{error:u.value}):n&&!f.value?Ei(n):void 0}})}function Ho(e,t){const{ref:n,props:o,children:r,ce:i}=t.vnode,a=Ei(e,o,r);return a.ref=n,a.ce=i,delete t.vnode.ce,a}const zo=e=>e.type.__isKeepAlive;class Uo{constructor(e){this.max=e,this._cache=new Map,this._keys=new Set,this._max=parseInt(e,10)}get(e){const{_cache:t,_keys:n,_max:o}=this,r=t.get(e);if(r)n.delete(e),n.add(e);else if(n.add(e),o&&n.size>o){const e=n.values().next().value;this.pruneCacheEntry(t.get(e)),this.delete(e)}return r}set(e,t){this._cache.set(e,t)}delete(e){this._cache.delete(e),this._keys.delete(e)}forEach(e,t){this._cache.forEach(e.bind(t))}}const Yo={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number],matchBy:{type:String,default:"name"},cache:Object},setup(e,{slots:t}){const n=Ni(),o=n.ctx;if(!o.renderer)return()=>{const e=t.default&&t.default();return e&&1===e.length?e[0]:e};const r=e.cache||new Uo(e.max);r.pruneCacheEntry=a;let i=null;function a(t){var o;!i||!xi(t,i)||"key"===e.matchBy&&t.key!==i.key?(er(o=t),u(o,n,s,!0)):i&&er(i)}const s=n.suspense,{renderer:{p:l,m:c,um:u,o:{createElement:d}}}=o,f=d("div");function h(t){r.forEach(((n,o)=>{const i=nr(n,e.matchBy);!i||t&&t(i)||(r.delete(o),a(n))}))}o.activate=(e,t,n,o,r)=>{const i=e.component;if(i.ba){const e=i.isDeactivated;i.isDeactivated=!1,Y(i.ba),i.isDeactivated=e}c(e,t,n,0,s),l(i.vnode,e,t,n,i,s,o,e.slotScopeIds,r),ii((()=>{i.isDeactivated=!1,i.a&&Y(i.a);const t=e.props&&e.props.onVnodeMounted;t&&Ri(t,i.parent,e)}),s)},o.deactivate=e=>{const t=e.component;t.bda&&or(t.bda),c(e,f,null,1,s),ii((()=>{t.bda&&rr(t.bda),t.da&&Y(t.da);const n=e.props&&e.props.onVnodeUnmounted;n&&Ri(n,t.parent,e),t.isDeactivated=!0}),s)},Co((()=>[e.include,e.exclude,e.matchBy]),(([e,t])=>{e&&h((t=>Go(e,t))),t&&h((e=>!Go(t,e)))}),{flush:"post",deep:!0});let p=null;const g=()=>{null!=p&&r.set(p,tr(n.subTree))};return lr(g),ur(g),dr((()=>{r.forEach(((t,o)=>{r.delete(o),a(t);const{subTree:i,suspense:s}=n,l=tr(i);if(t.type!==l.type||"key"===e.matchBy&&t.key!==l.key);else{l.component.bda&&Y(l.component.bda),er(l);const e=l.component.da;e&&ii(e,s)}}))})),()=>{if(p=null,!t.default)return null;const n=t.default(),o=n[0];if(n.length>1)return i=null,n;if(!wi(o)||!(4&o.shapeFlag)&&!wo(o.type))return i=null,o;let a=tr(o);const s=a.type,l=nr(a,e.matchBy),{include:c,exclude:u}=e;if(c&&(!l||!Go(c,l))||u&&l&&Go(u,l))return i=a,o;const d=null==a.key?s:a.key,f=r.get(d);return a.el&&(a=Oi(a),wo(o.type)&&(o.ssContent=a)),p=d,f&&(a.el=f.el,a.component=f.component,a.transition&&No(a,a.transition),a.shapeFlag|=512),a.shapeFlag|=256,i=a,wo(o.type)?o:a}}},Xo=Yo;function Go(e,t){return C(e)?e.some((e=>Go(e,t))):I(e)?e.split(",").includes(t):!!e.test&&e.test(t)}function Jo(e,t){Zo(e,"a",t)}function Ko(e,t){Zo(e,"da",t)}function Zo(e,t,n=Di){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(o.__called=!1,ir(t,o,n),n){let e=n.parent;for(;e&&e.parent;)zo(e.parent.vnode)&&Qo(o,t,n,e),e=e.parent}}function Qo(e,t,n,o){const r=ir(t,e,o,!0);fr((()=>{T(o[t],r)}),n)}function er(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function tr(e){return wo(e.type)?e.ssContent:e}function nr(e,t){if("name"===t){const t=e.type;return Xi(qo(e)?t.__asyncResolved||{}:t)}return String(e.key)}function or(e){for(let t=0;t<e.length;t++){const n=e[t];n.__called||(n(),n.__called=!0)}}function rr(e){e.forEach((e=>e.__called=!1))}function ir(e,t,n=Di,o=!1){if(n){if(function(e){return Ze.indexOf(e)>-1}(e)&&n.$pageInstance){if(n.type.__reserved)return;if(n!==n.$pageInstance&&(n=n.$pageInstance,function(e){return Qe.indexOf(e)>-1}(e))){const o=n.proxy;Wn(t.bind(o),n,e,ce===e?[o.$page.options]:[])}}const r=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;Tt(),Fi(n);const r=Wn(t,n,e,o);return Vi(),kt(),r});return o?r.unshift(i):r.push(i),i}}const ar=e=>(t,n=Di)=>(!Hi||"sp"===e)&&ir(e,((...e)=>t(...e)),n),sr=ar("bm"),lr=ar("m"),cr=ar("bu"),ur=ar("u"),dr=ar("bum"),fr=ar("um"),hr=ar("sp"),pr=ar("rtg"),gr=ar("rtc");function mr(e,t=Di){ir("ec",e,t)}function vr(e,t){const n=ho;if(null===n)return e;const o=Yi(n)||n.proxy,r=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[e,n,a,s=g]=t[i];e&&(M(e)&&(e={mounted:e,updated:e}),e.deep&&Io(n),r.push({dir:e,instance:o,value:n,oldValue:void 0,arg:a,modifiers:s}))}return e}function yr(e,t,n,o){const r=e.dirs,i=t&&t.dirs;for(let a=0;a<r.length;a++){const s=r[a];i&&(s.oldValue=i[a].value);let l=s.dir[o];l&&(Tt(),Wn(l,n,8,[e.el,s,e,t]),kt())}}const br="components";function _r(e,t){return Tr(br,e,!0,t)||e}const wr=Symbol();function xr(e){return I(e)?Tr(br,e,!1)||e:e||wr}function Tr(e,t,n=!0,o=!1){const r=ho||Di;if(r){const n=r.type;if(e===br){const e=Xi(n,!1);if(e&&(e===t||e===V(t)||e===H(V(t))))return n}const i=kr(r[e]||n[e],t)||kr(r.appContext[e],t);return!i&&o?n:i}}function kr(e,t){return e&&(e[t]||e[V(t)]||e[H(V(t))])}function Sr(e,t,n,o){let r;const i=n&&n[o];if(C(e)||I(e)){r=new Array(e.length);for(let n=0,o=e.length;n<o;n++)r[n]=t(e[n],n,void 0,i&&i[n])}else if("number"==typeof e){r=new Array(e);for(let n=0;n<e;n++)r[n]=t(n+1,n,void 0,i&&i[n])}else if(P(e))if(e[Symbol.iterator])r=Array.from(e,((e,n)=>t(e,n,void 0,i&&i[n])));else{const n=Object.keys(e);r=new Array(n.length);for(let o=0,a=n.length;o<a;o++){const a=n[o];r[o]=t(e[a],a,o,i&&i[o])}}else r=[];return n&&(n[o]=r),r}function Cr(e,t,n={},o,r){if(ho.isCE||ho.parent&&qo(ho.parent)&&ho.parent.isCE)return"default"!==t&&(n.name=t),Ei("slot",n,o&&o());let i=e[t];i&&i._c&&(i._d=!1),gi();const a=i&&Er(i(n)),s=_i(ci,{key:n.key||a&&a.key||`_${t}`},a||(o?o():[]),a&&1===e._?64:-2);return!r&&s.scopeId&&(s.slotScopeIds=[s.scopeId+"-s"]),i&&i._c&&(i._d=!0),s}function Er(e){return e.some((e=>!wi(e)||e.type!==di&&!(e.type===ci&&!Er(e.children))))?e:null}const Or=e=>e?qi(e)?Yi(e)||e.proxy:Or(e.parent):null,Mr=x(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Or(e.parent),$root:e=>Or(e.root),$emit:e=>e.emit,$options:e=>jr(e),$forceUpdate:e=>e.f||(e.f=()=>to(e.update)),$nextTick:e=>e.n||(e.n=eo.bind(e.proxy)),$watch:e=>Oo.bind(e)}),Ir=(e,t)=>e!==g&&!e.__isScriptSetup&&S(e,t),Ar={get({_:e},t){const{ctx:n,setupState:o,data:r,props:i,accessCache:a,type:s,appContext:l}=e;let c;if("$"!==t[0]){const s=a[t];if(void 0!==s)switch(s){case 1:return o[t];case 2:return r[t];case 4:return n[t];case 3:return i[t]}else{if(Ir(o,t))return a[t]=1,o[t];if(r!==g&&S(r,t))return a[t]=2,r[t];if((c=e.propsOptions[0])&&S(c,t))return a[t]=3,i[t];if(n!==g&&S(n,t))return a[t]=4,n[t];Pr&&(a[t]=0)}}const u=Mr[t];let d,f;return u?("$attrs"===t&&St(e,0,t),u(e)):(d=s.__cssModules)&&(d=d[t])?d:n!==g&&S(n,t)?(a[t]=4,n[t]):(f=l.config.globalProperties,S(f,t)?f[t]:void 0)},set({_:e},t,n){const{data:o,setupState:r,ctx:i}=e;return Ir(r,t)?(r[t]=n,!0):o!==g&&S(o,t)?(o[t]=n,!0):!S(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(i[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:r,propsOptions:i}},a){let s;return!!n[a]||e!==g&&S(e,a)||Ir(t,a)||(s=i[0])&&S(s,a)||S(o,a)||S(Mr,a)||S(r.config.globalProperties,a)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:S(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};let Pr=!0;function $r(e){const t=jr(e),n=e.proxy,o=e.ctx;Pr=!1,t.beforeCreate&&Lr(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:a,watch:s,provide:l,inject:c,created:u,beforeMount:d,mounted:f,beforeUpdate:h,updated:p,activated:g,deactivated:m,beforeDestroy:y,beforeUnmount:b,destroyed:_,unmounted:w,render:x,renderTracked:T,renderTriggered:k,errorCaptured:S,serverPrefetch:E,expose:O,inheritAttrs:I,components:A,directives:$,filters:L}=t;if(c&&function(e,t,n=v,o=!1){C(e)&&(e=Fr(e));for(const r in e){const n=e[r];let i;i=P(n)?"default"in n?To(n.from||r,n.default,!0):To(n.from||r):To(n),In(i)&&o?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:e=>i.value=e}):t[r]=i}}(c,o,null,e.appContext.config.unwrapInjectedRef),a)for(const v in a){const e=a[v];M(e)&&(o[v]=e.bind(n))}if(r){const t=r.call(n,n);P(t)&&(e.data=mn(t))}if(Pr=!0,i)for(const C in i){const e=i[C],t=M(e)?e.bind(n,n):M(e.get)?e.get.bind(n,n):v,r=!M(e)&&M(e.set)?e.set.bind(n):v,a=Gi({get:t,set:r});Object.defineProperty(o,C,{enumerable:!0,configurable:!0,get:()=>a.value,set:e=>a.value=e})}if(s)for(const v in s)Rr(s[v],o,n,v);if(l){const e=M(l)?l.call(n):l;Reflect.ownKeys(e).forEach((t=>{xo(t,e[t])}))}function R(e,t){C(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(u&&Lr(u,e,"c"),R(sr,d),R(lr,f),R(cr,h),R(ur,p),R(Jo,g),R(Ko,m),R(mr,S),R(gr,T),R(pr,k),R(dr,b),R(fr,w),R(hr,E),C(O))if(O.length){const t=e.exposed||(e.exposed={});O.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});x&&e.render===v&&(e.render=x),null!=I&&(e.inheritAttrs=I),A&&(e.components=A),$&&(e.directives=$);const j=e.appContext.config.globalProperties.$applyOptions;j&&j(t,e,n)}function Lr(e,t,n){Wn(C(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function Rr(e,t,n,o){const r=o.includes(".")?Mo(n,o):()=>n[o];if(I(e)){const n=t[e];M(n)&&Co(r,n)}else if(M(e))Co(r,e.bind(n));else if(P(e))if(C(e))e.forEach((e=>Rr(e,t,n,o)));else{const o=M(e.handler)?e.handler.bind(n):t[e.handler];M(o)&&Co(r,o,e)}}function jr(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:a}}=e.appContext,s=i.get(t);let l;return s?l=s:r.length||n||o?(l={},r.length&&r.forEach((e=>Br(l,e,a,!0))),Br(l,t,a)):l=t,P(t)&&i.set(t,l),l}function Br(e,t,n,o=!1){const{mixins:r,extends:i}=t;i&&Br(e,i,n,!0),r&&r.forEach((t=>Br(e,t,n,!0)));for(const a in t)if(o&&"expose"===a);else{const o=Dr[a]||n&&n[a];e[a]=o?o(e[a],t[a]):t[a]}return e}const Dr={data:Nr,props:qr,emits:qr,methods:qr,computed:qr,beforeCreate:Vr,created:Vr,beforeMount:Vr,mounted:Vr,beforeUpdate:Vr,updated:Vr,beforeDestroy:Vr,beforeUnmount:Vr,destroyed:Vr,unmounted:Vr,activated:Vr,deactivated:Vr,errorCaptured:Vr,serverPrefetch:Vr,components:qr,directives:qr,watch:function(e,t){if(!e)return t;if(!t)return e;const n=x(Object.create(null),e);for(const o in t)n[o]=Vr(e[o],t[o]);return n},provide:Nr,inject:function(e,t){return qr(Fr(e),Fr(t))}};function Nr(e,t){return t?e?function(){return x(M(e)?e.call(this,this):e,M(t)?t.call(this,this):t)}:t:e}function Fr(e){if(C(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Vr(e,t){return e?[...new Set([].concat(e,t))]:t}function qr(e,t){return e?x(x(Object.create(null),e),t):t}function Wr(e,t,n,o){const[r,i]=e.propsOptions;let a,s=!1;if(t)for(let l in t){if(D(l))continue;const c=t[l];let u;r&&S(r,u=V(l))?i&&i.includes(u)?(a||(a={}))[u]=c:n[u]=c:fo(e.emitsOptions,l)||l in o&&c===o[l]||(o[l]=c,s=!0)}if(i){const t=kn(n),o=a||g;for(let a=0;a<i.length;a++){const s=i[a];n[s]=Hr(r,t,s,o[s],e,!S(o,s))}}return s}function Hr(e,t,n,o,r,i){const a=e[n];if(null!=a){const e=S(a,"default");if(e&&void 0===o){const e=a.default;if(a.type!==Function&&M(e)){const{propsDefaults:i}=r;n in i?o=i[n]:(Fi(r),o=i[n]=e.call(null,t),Vi())}else o=e}a[0]&&(i&&!e?o=!1:!a[1]||""!==o&&o!==W(n)||(o=!0))}return o}function zr(e,t,n=!1){const o=t.propsCache,r=o.get(e);if(r)return r;const i=e.props,a={},s=[];let l=!1;if(!M(e)){const o=e=>{l=!0;const[n,o]=zr(e,t,!0);x(a,n),o&&s.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!i&&!l)return P(e)&&o.set(e,m),m;if(C(i))for(let u=0;u<i.length;u++){const e=V(i[u]);Ur(e)&&(a[e]=g)}else if(i)for(const u in i){const e=V(u);if(Ur(e)){const t=i[u],n=a[e]=C(t)||M(t)?{type:t}:Object.assign({},t);if(n){const t=Gr(Boolean,n.type),o=Gr(String,n.type);n[0]=t>-1,n[1]=o<0||t<o,(t>-1||S(n,"default"))&&s.push(e)}}}const c=[a,s];return P(e)&&o.set(e,c),c}function Ur(e){return"$"!==e[0]}function Yr(e){const t=e&&e.toString().match(/^\s*(function|class) (\w+)/);return t?t[2]:null===e?"null":""}function Xr(e,t){return Yr(e)===Yr(t)}function Gr(e,t){return C(t)?t.findIndex((t=>Xr(t,e))):M(t)&&Xr(t,e)?0:-1}const Jr=e=>"_"===e[0]||"$stable"===e,Kr=e=>C(e)?e.map(Ai):[Ai(e)],Zr=(e,t,n)=>{if(t._n)return t;const o=mo(((...e)=>Kr(t(...e))),n);return o._c=!1,o},Qr=(e,t,n)=>{const o=e._ctx;for(const r in e){if(Jr(r))continue;const n=e[r];if(M(n))t[r]=Zr(0,n,o);else if(null!=n){const e=Kr(n);t[r]=()=>e}}},ei=(e,t)=>{const n=Kr(t);e.slots.default=()=>n};function ti(){return{app:null,config:{isNativeTag:y,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ni=0;function oi(e,t){return function(n,o=null){M(n)||(n=Object.assign({},n)),null==o||P(o)||(o=null);const r=ti(),i=new Set;let a=!1;const s=r.app={_uid:ni++,_component:n,_props:o,_container:null,_context:r,_instance:null,version:Qi,get config(){return r.config},set config(e){},use:(e,...t)=>(i.has(e)||(e&&M(e.install)?(i.add(e),e.install(s,...t)):M(e)&&(i.add(e),e(s,...t))),s),mixin:e=>(r.mixins.includes(e)||r.mixins.push(e),s),component:(e,t)=>t?(r.components[e]=t,s):r.components[e],directive:(e,t)=>t?(r.directives[e]=t,s):r.directives[e],mount(i,l,c){if(!a){const u=Ei(n,o);return u.appContext=r,l&&t?t(u,i):e(u,i,c),a=!0,s._container=i,i.__vue_app__=s,s._instance=u.component,Yi(u.component)||u.component.proxy}},unmount(){a&&(e(null,s._container),delete s._container.__vue_app__)},provide:(e,t)=>(r.provides[e]=t,s)};return s}}function ri(e,t,n,o,r=!1){if(C(e))return void e.forEach(((e,i)=>ri(e,t&&(C(t)?t[i]:t),n,o,r)));if(qo(o)&&!r)return;const i=4&o.shapeFlag?Yi(o.component)||o.component.proxy:o.el,a=r?null:i,{i:s,r:l}=e,c=t&&t.r,u=s.refs===g?s.refs={}:s.refs,d=s.setupState;if(null!=c&&c!==l&&(I(c)?(u[c]=null,S(d,c)&&(d[c]=null)):In(c)&&(c.value=null)),M(l))qn(l,s,12,[a,u]);else{const t=I(l),o=In(l);if(t||o){const s=()=>{if(e.f){const n=t?S(d,l)?d[l]:u[l]:l.value;r?C(n)&&T(n,i):C(n)?n.includes(i)||n.push(i):t?(u[l]=[i],S(d,l)&&(d[l]=u[l])):(l.value=[i],e.k&&(u[e.k]=l.value))}else t?(u[l]=a,S(d,l)&&(d[l]=a)):o&&(l.value=a,e.k&&(u[e.k]=a))};a?(s.id=-1,ii(s,n)):s()}}}const ii=function(e,t){var n;t&&t.pendingBranch?C(e)?t.effects.push(...e):t.effects.push(e):(C(n=e)?Gn.push(...n):Jn&&Jn.includes(n,n.allowRecurse?Kn+1:Kn)||Gn.push(n),no())};function ai(e){return function(e,t){(J||(J="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})).__VUE__=!0;const{insert:n,remove:o,patchProp:r,forcePatchProp:i,createElement:a,createText:s,createComment:l,setText:c,setElementText:u,parentNode:d,nextSibling:f,setScopeId:h=v,insertStaticContent:p}=e,y=(e,t,n,o=null,r=null,i=null,a=!1,s=null,l=!!t.dynamicChildren)=>{if(e===t)return;e&&!xi(e,t)&&(o=te(e),G(e,r,i,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:d}=t;switch(c){case ui:b(e,t,n,o);break;case di:_(e,t,n,o);break;case fi:null==e&&w(t,n,o,a);break;case ci:L(e,t,n,o,r,i,a,s,l);break;default:1&d?C(e,t,n,o,r,i,a,s,l):6&d?R(e,t,n,o,r,i,a,s,l):(64&d||128&d)&&c.process(e,t,n,o,r,i,a,s,l,oe)}null!=u&&r&&ri(u,e&&e.ref,i,t||e,!t)},b=(e,t,o,r)=>{if(null==e)n(t.el=s(t.children),o,r);else{const n=t.el=e.el;t.children!==e.children&&c(n,t.children)}},_=(e,t,o,r)=>{null==e?n(t.el=l(t.children||""),o,r):t.el=e.el},w=(e,t,n,o)=>{[e.el,e.anchor]=p(e.children,t,n,o,e.el,e.anchor)},T=({el:e,anchor:t},o,r)=>{let i;for(;e&&e!==t;)i=f(e),n(e,o,r),e=i;n(t,o,r)},k=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=f(e),o(e),e=n;o(t)},C=(e,t,n,o,r,i,a,s,l)=>{a=a||"svg"===t.type,null==e?E(t,n,o,r,i,a,s,l):I(e,t,r,i,a,s,l)},E=(e,t,o,i,s,l,c,d)=>{let f,h;const{type:p,props:g,shapeFlag:m,transition:v,dirs:y}=e;if(f=e.el=a(e.type,l,g&&g.is,g),8&m?u(f,e.children):16&m&&M(e.children,f,null,i,s,l&&"foreignObject"!==p,c,d),y&&yr(e,null,i,"created"),O(f,e,e.scopeId,c,i),g){for(const t in g)"value"===t||D(t)||r(f,t,null,g[t],l,e.children,i,s,ee);"value"in g&&r(f,"value",null,g.value),(h=g.onVnodeBeforeMount)&&Ri(h,i,e)}Object.defineProperty(f,"__vueParentComponent",{value:i,enumerable:!1}),y&&yr(e,null,i,"beforeMount");const b=(!s||s&&!s.pendingBranch)&&v&&!v.persisted;b&&v.beforeEnter(f),n(f,t,o),((h=g&&g.onVnodeMounted)||b||y)&&ii((()=>{h&&Ri(h,i,e),b&&v.enter(f),y&&yr(e,null,i,"mounted")}),s)},O=(e,t,n,o,r)=>{if(n&&h(e,n),o)for(let i=0;i<o.length;i++)h(e,o[i]);if(r){if(t===r.subTree){const t=r.vnode;O(e,t,t.scopeId,t.slotScopeIds,r.parent)}}},M=(e,t,n,o,r,i,a,s,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=s?Pi(e[c]):Ai(e[c]);y(null,l,t,n,o,r,i,a,s)}},I=(e,t,n,o,a,s,l)=>{const c=t.el=e.el;let{patchFlag:d,dynamicChildren:f,dirs:h}=t;d|=16&e.patchFlag;const p=e.props||g,m=t.props||g;let v;n&&si(n,!1),(v=m.onVnodeBeforeUpdate)&&Ri(v,n,t,e),h&&yr(t,e,n,"beforeUpdate"),n&&si(n,!0);const y=a&&"foreignObject"!==t.type;if(f?A(e.dynamicChildren,f,c,n,o,y,s):l||q(e,t,c,null,n,o,y,s,!1),d>0){if(16&d)P(c,t,p,m,n,o,a);else if(2&d&&p.class!==m.class&&r(c,"class",null,m.class,a),4&d&&r(c,"style",p.style,m.style,a),8&d){const s=t.dynamicProps;for(let t=0;t<s.length;t++){const l=s[t],u=p[l],d=m[l];(d!==u||"value"===l||i&&i(c,l))&&r(c,l,u,d,a,e.children,n,o,ee)}}1&d&&e.children!==t.children&&u(c,t.children)}else l||null!=f||P(c,t,p,m,n,o,a);((v=m.onVnodeUpdated)||h)&&ii((()=>{v&&Ri(v,n,t,e),h&&yr(t,e,n,"updated")}),o)},A=(e,t,n,o,r,i,a)=>{for(let s=0;s<t.length;s++){const l=e[s],c=t[s],u=l.el&&(l.type===ci||!xi(l,c)||70&l.shapeFlag)?d(l.el):n;y(l,c,u,null,o,r,i,a,!0)}},P=(e,t,n,o,a,s,l)=>{if(n!==o){if(n!==g)for(const i in n)D(i)||i in o||r(e,i,n[i],null,l,t.children,a,s,ee);for(const c in o){if(D(c))continue;const u=o[c],d=n[c];(u!==d&&"value"!==c||i&&i(e,c))&&r(e,c,d,u,l,t.children,a,s,ee)}"value"in o&&r(e,"value",n.value,o.value)}},L=(e,t,o,r,i,a,l,c,u)=>{const d=t.el=e?e.el:s(""),f=t.anchor=e?e.anchor:s("");let{patchFlag:h,dynamicChildren:p,slotScopeIds:g}=t;g&&(c=c?c.concat(g):g),null==e?(n(d,o,r),n(f,o,r),M(t.children,o,f,i,a,l,c,u)):h>0&&64&h&&p&&e.dynamicChildren?(A(e.dynamicChildren,p,o,i,a,l,c),(null!=t.key||i&&t===i.subTree)&&li(e,t,!0)):q(e,t,o,f,i,a,l,c,u)},R=(e,t,n,o,r,i,a,s,l)=>{t.slotScopeIds=s,null==e?512&t.shapeFlag?r.ctx.activate(t,n,o,a,l):j(t,n,o,r,i,a,l):B(e,t,l)},j=(e,t,n,o,r,i,a)=>{const s=e.component=function(e,t,n){const o=e.type,r=(t?t.appContext:e.appContext)||ji,i={uid:Bi++,vnode:e,type:o,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,scope:new lt(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:zr(o,r),emitsOptions:uo(o,r),emit:null,emitted:null,propsDefaults:g,inheritAttrs:o.inheritAttrs,ctx:g,data:g,props:g,attrs:g,slots:g,refs:g,setupState:g,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,bda:null,da:null,ba:null,a:null,rtg:null,rtc:null,ec:null,sp:null};i.ctx={_:i},i.root=t?t.root:i,i.emit=lo.bind(null,i),i.$pageInstance=t&&t.$pageInstance,e.ce&&e.ce(i);return i}(e,o,r);if(zo(e)&&(s.ctx.renderer=oe),function(e,t=!1){Hi=t;const{props:n,children:o}=e.vnode,r=qi(e);(function(e,t,n,o=!1){const r={},i={};X(i,Ti,1),e.propsDefaults=Object.create(null),Wr(e,t,r,i);for(const a in e.propsOptions[0])a in r||(r[a]=void 0);n?e.props=o?r:vn(r):e.type.props?e.props=r:e.props=i,e.attrs=i})(e,n,r,t),((e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=kn(t),X(t,"_",n)):Qr(t,e.slots={})}else e.slots={},t&&ei(e,t);X(e.slots,Ti,1)})(e,o);const i=r?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=Sn(new Proxy(e.ctx,Ar));const{setup:o}=n;if(o){const n=e.setupContext=o.length>1?function(e){const t=t=>{e.exposed=t||{}};let n;return{get attrs(){return n||(n=function(e){return new Proxy(e.attrs,{get:(t,n)=>(St(e,0,"$attrs"),t[n])})}(e))},slots:e.slots,emit:e.emit,expose:t}}(e):null;Fi(e),Tt();const r=qn(o,e,0,[e.props,n]);if(kt(),Vi(),$(r)){if(r.then(Vi,Vi),t)return r.then((n=>{zi(e,n,t)})).catch((t=>{Hn(t,e,0)}));e.asyncDep=r}else zi(e,r,t)}else Ui(e,t)}(e,t):void 0;Hi=!1}(s),s.asyncDep){if(r&&r.registerDep(s,N),!e.el){const e=s.subTree=Ei(di);_(null,e,t,n)}}else N(s,e,t,n,r,i,a)},B=(e,t,n)=>{const o=t.component=e.component;if(function(e,t,n){const{props:o,children:r,component:i}=e,{props:a,children:s,patchFlag:l}=t,c=i.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!r&&!s||s&&s.$stable)||o!==a&&(o?!a||_o(o,a,c):!!a);if(1024&l)return!0;if(16&l)return o?_o(o,a,c):!!a;if(8&l){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(a[n]!==o[n]&&!fo(c,n))return!0}}return!1}(e,t,n)){if(o.asyncDep&&!o.asyncResolved)return void F(o,t,n);o.next=t,function(e){const t=Yn.indexOf(e);t>Xn&&Yn.splice(t,1)}(o.update),o.update()}else t.el=e.el,o.vnode=t},N=(e,t,n,o,r,i,a)=>{const s=()=>{if(e.isMounted){let t,{next:n,bu:o,u:s,parent:l,vnode:c}=e,u=n;si(e,!1),n?(n.el=c.el,F(e,n,a)):n=c,o&&Y(o),(t=n.props&&n.props.onVnodeBeforeUpdate)&&Ri(t,l,n,c),si(e,!0);const f=vo(e),h=e.subTree;e.subTree=f,y(h,f,d(h.el),te(h),e,r,i),n.el=f.el,null===u&&function({vnode:e,parent:t},n){for(;t&&t.subTree===e;)(e=t.vnode).el=n,t=t.parent}(e,f.el),s&&ii(s,r),(t=n.props&&n.props.onVnodeUpdated)&&ii((()=>Ri(t,l,n,c)),r)}else{let a;const{el:s,props:l}=t,{bm:c,m:u,parent:d}=e,f=qo(t);if(si(e,!1),c&&Y(c),!f&&(a=l&&l.onVnodeBeforeMount)&&Ri(a,d,t),si(e,!0),s&&ie){const n=()=>{e.subTree=vo(e),ie(s,e.subTree,e,r,null)};f?t.type.__asyncLoader().then((()=>!e.isUnmounted&&n())):n()}else{const a=e.subTree=vo(e);y(null,a,n,o,e,r,i),t.el=a.el}if(u&&ii(u,r),!f&&(a=l&&l.onVnodeMounted)){const e=t;ii((()=>Ri(a,d,e)),r)}const{ba:h,a:p}=e;(256&t.shapeFlag||d&&qo(d.vnode)&&256&d.vnode.shapeFlag)&&(h&&or(h),p&&ii(p,r),h&&ii((()=>rr(h)),r)),e.isMounted=!0,t=n=o=null}},l=e.effect=new bt(s,(()=>to(c)),e.scope),c=e.update=()=>l.run();c.id=e.uid,si(e,!0),c()},F=(e,t,n)=>{t.component=e;const o=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,o){const{props:r,attrs:i,vnode:{patchFlag:a}}=e,s=kn(r),[l]=e.propsOptions;let c=!1;if(!(o||a>0)||16&a){let o;Wr(e,t,r,i)&&(c=!0);for(const i in s)t&&(S(t,i)||(o=W(i))!==i&&S(t,o))||(l?!n||void 0===n[i]&&void 0===n[o]||(r[i]=Hr(l,s,i,void 0,e,!0)):delete r[i]);if(i!==s)for(const e in i)t&&S(t,e)||(delete i[e],c=!0)}else if(8&a){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let a=n[o];if(fo(e.emitsOptions,a))continue;const u=t[a];if(l)if(S(i,a))u!==i[a]&&(i[a]=u,c=!0);else{const t=V(a);r[t]=Hr(l,s,t,u,e,!1)}else u!==i[a]&&(i[a]=u,c=!0)}}c&&Et(e,"set","$attrs")}(e,t.props,o,n),((e,t,n)=>{const{vnode:o,slots:r}=e;let i=!0,a=g;if(32&o.shapeFlag){const e=t._;e?n&&1===e?i=!1:(x(r,t),n||1!==e||delete r._):(i=!t.$stable,Qr(t,r)),a=t}else t&&(ei(e,t),a={default:1});if(i)for(const s in r)Jr(s)||s in a||delete r[s]})(e,t.children,n),Tt(),oo(),kt()},q=(e,t,n,o,r,i,a,s,l=!1)=>{const c=e&&e.children,d=e?e.shapeFlag:0,f=t.children,{patchFlag:h,shapeFlag:p}=t;if(h>0){if(128&h)return void z(c,f,n,o,r,i,a,s,l);if(256&h)return void H(c,f,n,o,r,i,a,s,l)}8&p?(16&d&&ee(c,r,i),f!==c&&u(n,f)):16&d?16&p?z(c,f,n,o,r,i,a,s,l):ee(c,r,i,!0):(8&d&&u(n,""),16&p&&M(f,n,o,r,i,a,s,l))},H=(e,t,n,o,r,i,a,s,l)=>{t=t||m;const c=(e=e||m).length,u=t.length,d=Math.min(c,u);let f;for(f=0;f<d;f++){const o=t[f]=l?Pi(t[f]):Ai(t[f]);y(e[f],o,n,null,r,i,a,s,l)}c>u?ee(e,r,i,!0,!1,d):M(t,n,o,r,i,a,s,l,d)},z=(e,t,n,o,r,i,a,s,l)=>{let c=0;const u=t.length;let d=e.length-1,f=u-1;for(;c<=d&&c<=f;){const o=e[c],u=t[c]=l?Pi(t[c]):Ai(t[c]);if(!xi(o,u))break;y(o,u,n,null,r,i,a,s,l),c++}for(;c<=d&&c<=f;){const o=e[d],c=t[f]=l?Pi(t[f]):Ai(t[f]);if(!xi(o,c))break;y(o,c,n,null,r,i,a,s,l),d--,f--}if(c>d){if(c<=f){const e=f+1,d=e<u?t[e].el:o;for(;c<=f;)y(null,t[c]=l?Pi(t[c]):Ai(t[c]),n,d,r,i,a,s,l),c++}}else if(c>f)for(;c<=d;)G(e[c],r,i,!0),c++;else{const h=c,p=c,g=new Map;for(c=p;c<=f;c++){const e=t[c]=l?Pi(t[c]):Ai(t[c]);null!=e.key&&g.set(e.key,c)}let v,b=0;const _=f-p+1;let w=!1,x=0;const T=new Array(_);for(c=0;c<_;c++)T[c]=0;for(c=h;c<=d;c++){const o=e[c];if(b>=_){G(o,r,i,!0);continue}let u;if(null!=o.key)u=g.get(o.key);else for(v=p;v<=f;v++)if(0===T[v-p]&&xi(o,t[v])){u=v;break}void 0===u?G(o,r,i,!0):(T[u-p]=c+1,u>=x?x=u:w=!0,y(o,t[u],n,null,r,i,a,s,l),b++)}const k=w?function(e){const t=e.slice(),n=[0];let o,r,i,a,s;const l=e.length;for(o=0;o<l;o++){const l=e[o];if(0!==l){if(r=n[n.length-1],e[r]<l){t[o]=r,n.push(o);continue}for(i=0,a=n.length-1;i<a;)s=i+a>>1,e[n[s]]<l?i=s+1:a=s;l<e[n[i]]&&(i>0&&(t[o]=n[i-1]),n[i]=o)}}i=n.length,a=n[i-1];for(;i-- >0;)n[i]=a,a=t[a];return n}(T):m;for(v=k.length-1,c=_-1;c>=0;c--){const e=p+c,d=t[e],f=e+1<u?t[e+1].el:o;0===T[c]?y(null,d,n,f,r,i,a,s,l):w&&(v<0||c!==k[v]?U(d,n,f,2):v--)}}},U=(e,t,o,r,i=null)=>{const{el:a,type:s,transition:l,children:c,shapeFlag:u}=e;if(6&u)return void U(e.component.subTree,t,o,r);if(128&u)return void e.suspense.move(t,o,r);if(64&u)return void s.move(e,t,o,oe);if(s===ci){n(a,t,o);for(let e=0;e<c.length;e++)U(c[e],t,o,r);return void n(e.anchor,t,o)}if(s===fi)return void T(e,t,o);if(2!==r&&1&u&&l)if(0===r)l.beforeEnter(a),n(a,t,o),ii((()=>l.enter(a)),i);else{const{leave:e,delayLeave:r,afterLeave:i}=l,s=()=>n(a,t,o),c=()=>{e(a,(()=>{s(),i&&i()}))};r?r(a,s,c):c()}else n(a,t,o)},G=(e,t,n,o=!1,r=!1)=>{const{type:i,props:a,ref:s,children:l,dynamicChildren:c,shapeFlag:u,patchFlag:d,dirs:f}=e;if(null!=s&&ri(s,null,n,e,!0),256&u)return void t.ctx.deactivate(e);const h=1&u&&f,p=!qo(e);let g;if(p&&(g=a&&a.onVnodeBeforeUnmount)&&Ri(g,t,e),6&u)Q(e.component,n,o);else{if(128&u)return void e.suspense.unmount(n,o);h&&yr(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,r,oe,o):c&&(i!==ci||d>0&&64&d)?ee(c,t,n,!1,!0):(i===ci&&384&d||!r&&16&u)&&ee(l,t,n),o&&K(e)}(p&&(g=a&&a.onVnodeUnmounted)||h)&&ii((()=>{g&&Ri(g,t,e),h&&yr(e,null,t,"unmounted")}),n)},K=e=>{const{type:t,el:n,anchor:r,transition:i}=e;if(t===ci)return void Z(n,r);if(t===fi)return void k(e);const a=()=>{o(n),i&&!i.persisted&&i.afterLeave&&i.afterLeave()};if(1&e.shapeFlag&&i&&!i.persisted){const{leave:t,delayLeave:o}=i,r=()=>t(n,a);o?o(e.el,a,r):r()}else a()},Z=(e,t)=>{let n;for(;e!==t;)n=f(e),o(e),e=n;o(t)},Q=(e,t,n)=>{const{bum:o,scope:r,update:i,subTree:a,um:s}=e;o&&Y(o),r.stop(),i&&(i.active=!1,G(a,e,t,n)),s&&ii(s,t),ii((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},ee=(e,t,n,o=!1,r=!1,i=0)=>{for(let a=i;a<e.length;a++)G(e[a],t,n,o,r)},te=e=>6&e.shapeFlag?te(e.component.subTree):128&e.shapeFlag?e.suspense.next():f(e.anchor||e.el),ne=(e,t,n)=>{null==e?t._vnode&&G(t._vnode,null,null,!0):y(t._vnode||null,e,t,null,null,null,n),oo(),ro(),t._vnode=e},oe={p:y,um:G,m:U,r:K,mt:j,mc:M,pc:q,pbc:A,n:te,o:e};let re,ie;t&&([re,ie]=t(oe));return{render:ne,hydrate:re,createApp:oi(ne,re)}}(e)}function si({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function li(e,t,n=!1){const o=e.children,r=t.children;if(C(o)&&C(r))for(let i=0;i<o.length;i++){const e=o[i];let t=r[i];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=r[i]=Pi(r[i]),t.el=e.el),n||li(e,t)),t.type===ui&&(t.el=e.el)}}const ci=Symbol(void 0),ui=Symbol(void 0),di=Symbol(void 0),fi=Symbol(void 0),hi=[];let pi=null;function gi(e=!1){hi.push(pi=e?null:[])}let mi=1;function vi(e){mi+=e}function yi(e){return e.dynamicChildren=mi>0?pi||m:null,hi.pop(),pi=hi[hi.length-1]||null,mi>0&&pi&&pi.push(e),e}function bi(e,t,n,o,r,i){return yi(Ci(e,t,n,o,r,i,!0))}function _i(e,t,n,o,r){return yi(Ei(e,t,n,o,r,!0))}function wi(e){return!!e&&!0===e.__v_isVNode}function xi(e,t){return e.type===t.type&&e.key===t.key}const Ti="__vInternal",ki=({key:e})=>null!=e?e:null,Si=({ref:e,ref_key:t,ref_for:n})=>null!=e?I(e)||In(e)||M(e)?{i:ho,r:e,k:t,f:!!n}:e:null;function Ci(e,t=null,n=null,o=0,r=null,i=(e===ci?0:1),a=!1,s=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&ki(t),ref:t&&Si(t),scopeId:po,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:ho};return s?($i(l,n),128&i&&e.normalize(l)):n&&(l.shapeFlag|=I(n)?8:16),mi>0&&!a&&pi&&(l.patchFlag>0||6&i)&&32!==l.patchFlag&&pi.push(l),l}const Ei=function(e,t=null,n=null,o=0,r=null,a=!1){e&&e!==wr||(e=di);if(wi(e)){const o=Oi(e,t,!0);return n&&$i(o,n),mi>0&&!a&&pi&&(6&o.shapeFlag?pi[pi.indexOf(e)]=o:pi.push(o)),o.patchFlag|=-2,o}s=e,M(s)&&"__vccOpts"in s&&(e=e.__vccOpts);var s;if(t){t=function(e){return e?Tn(e)||Ti in e?x({},e):e:null}(t);let{class:e,style:n}=t;e&&!I(e)&&(t.class=u(e)),P(n)&&(Tn(n)&&!C(n)&&(n=x({},n)),t.style=i(n))}const l=I(e)?1:wo(e)?128:(e=>e.__isTeleport)(e)?64:P(e)?4:M(e)?2:0;return Ci(e,t,n,o,r,l,a,!0)};function Oi(e,t,n=!1){const{props:o,ref:r,patchFlag:i,children:a}=e,s=t?Li(o||{},t):o;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:s,key:s&&ki(s),ref:t&&t.ref?n&&r?C(r)?r.concat(Si(t)):[r,Si(t)]:Si(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ci?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Oi(e.ssContent),ssFallback:e.ssFallback&&Oi(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function Mi(e=" ",t=0){return Ei(ui,null,e,t)}function Ii(e="",t=!1){return t?(gi(),_i(di,null,e)):Ei(di,null,e)}function Ai(e){return null==e||"boolean"==typeof e?Ei(di):C(e)?Ei(ci,null,e.slice()):"object"==typeof e?Pi(e):Ei(ui,null,String(e))}function Pi(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:Oi(e)}function $i(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if(C(t))n=16;else if("object"==typeof t){if(65&o){const n=t.default;return void(n&&(n._c&&(n._d=!1),$i(e,n()),n._c&&(n._d=!0)))}{n=32;const o=t._;o||Ti in t?3===o&&ho&&(1===ho.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=ho}}else M(t)?(t={default:t,_ctx:ho},n=32):(t=String(t),64&o?(n=16,t=[Mi(t)]):n=8);e.children=t,e.shapeFlag|=n}function Li(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const e in o)if("class"===e)t.class!==o.class&&(t.class=u([t.class,o.class]));else if("style"===e)t.style=i([t.style,o.style]);else if(_(e)){const n=t[e],r=o[e];!r||n===r||C(n)&&n.includes(r)||(t[e]=n?[].concat(n,r):r)}else""!==e&&(t[e]=o[e])}return t}function Ri(e,t,n,o=null){Wn(e,t,7,[n,o])}const ji=ti();let Bi=0;let Di=null;const Ni=()=>Di||ho,Fi=e=>{Di=e,e.scope.on()},Vi=()=>{Di&&Di.scope.off(),Di=null};function qi(e){return 4&e.vnode.shapeFlag}let Wi,Hi=!1;function zi(e,t,n){M(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:P(t)&&(e.setupState=Bn(t)),Ui(e,n)}function Ui(e,t,n){const o=e.type;if(!e.render){if(!t&&Wi&&!o.render){const t=o.template||jr(e).template;if(t){const{isCustomElement:n,compilerOptions:r}=e.appContext.config,{delimiters:i,compilerOptions:a}=o,s=x(x({isCustomElement:n,delimiters:i},r),a);o.render=Wi(t,s)}}e.render=o.render||v}Fi(e),Tt(),$r(e),kt(),Vi()}function Yi(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(Bn(Sn(e.exposed)),{get:(t,n)=>n in t?t[n]:n in Mr?Mr[n](e):void 0,has:(e,t)=>t in e||t in Mr}))}function Xi(e,t=!0){return M(e)?e.displayName||e.name:e.name||t&&e.__name}const Gi=(e,t)=>function(e,t,n=!1){let o,r;const i=M(e);return i?(o=e,r=v):(o=e.get,r=e.set),new Vn(o,r,i||!r,n)}(e,0,Hi);function Ji(e,t,n){const o=arguments.length;return 2===o?P(t)&&!C(t)?wi(t)?Ei(e,null,[t]):Ei(e,t):Ei(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&wi(n)&&(n=[n]),Ei(e,t,n))}const Ki=Symbol(""),Zi=()=>To(Ki),Qi="3.2.47",ea="undefined"!=typeof document?document:null,ta=ea&&ea.createElement("template"),na={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const r=t?ea.createElementNS("http://www.w3.org/2000/svg",e):ea.createElement(e,n?{is:n}:void 0);return"select"===e&&o&&null!=o.multiple&&r.setAttribute("multiple",o.multiple),r},createText:e=>ea.createTextNode(e),createComment:e=>ea.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>ea.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,r,i){const a=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),r!==i&&(r=r.nextSibling););else{ta.innerHTML=o?`<svg>${e}</svg>`:e;const r=ta.content;if(o){const e=r.firstChild;for(;e.firstChild;)r.appendChild(e.firstChild);r.removeChild(e)}t.insertBefore(r,n)}return[a?a.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}};const oa=/\s*!important$/;function ra(e,t,n){if(C(n))n.forEach((n=>ra(e,t,n)));else if(null==n&&(n=""),n=pa(n),t.startsWith("--"))e.setProperty(t,n);else{const o=function(e,t){const n=aa[t];if(n)return n;let o=V(t);if("filter"!==o&&o in e)return aa[t]=o;o=H(o);for(let r=0;r<ia.length;r++){const n=ia[r]+o;if(n in e)return aa[t]=n}return t}(e,t);oa.test(n)?e.setProperty(W(o),n.replace(oa,""),"important"):e[o]=n}}const ia=["Webkit","Moz","ms"],aa={};const{unit:sa,unitRatio:la,unitPrecision:ca}={unit:"rem",unitRatio:10/320,unitPrecision:5},ua=(da=sa,fa=la,ha=ca,e=>e.replace(We,((e,t)=>{if(!t)return e;if(1===fa)return`${t}${da}`;const n=function(e,t){const n=Math.pow(10,t+1),o=Math.floor(e*n);return 10*Math.round(o/10)/n}(parseFloat(t)*fa,ha);return 0===n?"0":`${n}${da}`})));var da,fa,ha;const pa=e=>I(e)?ua(e):e,ga="http://www.w3.org/1999/xlink";function ma(e,t,n,o,r=null){const i=e._vei||(e._vei={}),a=i[t];if(o&&a)a.value=o;else{const[n,s]=function(e){let t;if(va.test(e)){let n;for(t={};n=e.match(va);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}const n=":"===e[2]?e.slice(3):W(e.slice(2));return[n,t]}(t);if(o){const a=i[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();const o=t&&t.proxy,r=o&&o.$nne,{value:i}=n;if(r&&C(i)){const n=wa(e,i);for(let o=0;o<n.length;o++){const i=n[o];Wn(i,t,5,i.__wwe?[e]:r(e))}}else Wn(wa(e,i),t,5,r&&!i.__wwe?r(e,i,t):[e])};return n.value=e,n.attached=_a(),n}(o,r);!function(e,t,n,o){e.addEventListener(t,n,o)}(e,n,a,s)}else a&&(!function(e,t,n,o){e.removeEventListener(t,n,o)}(e,n,a,s),i[t]=void 0)}}const va=/(?:Once|Passive|Capture)$/;let ya=0;const ba=Promise.resolve(),_a=()=>ya||(ba.then((()=>ya=0)),ya=Date.now());function wa(e,t){if(C(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>{const t=t=>!t._stopped&&e&&e(t);return t.__wwe=e.__wwe,t}))}return t}const xa=/^on[a-z]/;function Ta(e){const t=Ni();if(!t)return;const n=t.ut=(n=e(t.proxy))=>{Array.from(document.querySelectorAll(`[data-v-owner="${t.uid}"]`)).forEach((e=>Sa(e,n)))},o=()=>{const o=e(t.proxy);ka(t.subTree,o),n(o)};Eo(o,null,{flush:"post"}),lr((()=>{const e=new MutationObserver(o);e.observe(t.subTree.el.parentNode,{childList:!0}),fr((()=>e.disconnect()))}))}function ka(e,t){if(128&e.shapeFlag){const n=e.suspense;e=n.activeBranch,n.pendingBranch&&!n.isHydrating&&n.effects.push((()=>{ka(n.activeBranch,t)}))}for(;e.component;)e=e.component.subTree;if(1&e.shapeFlag&&e.el)Sa(e.el,t);else if(e.type===ci)e.children.forEach((e=>ka(e,t)));else if(e.type===fi){let{el:n,anchor:o}=e;for(;n&&(Sa(n,t),n!==o);)n=n.nextSibling}}function Sa(e,t){if(1===e.nodeType){const n=e.style;for(const e in t)n.setProperty(`--${e}`,pa(t[e]))}}const Ca="transition",Ea="animation",Oa=(e,{slots:t})=>Ji(Lo,function(e){const t={};for(const x in e)x in Ma||(t[x]=e[x]);if(!1===e.css)return t;const{name:n="v",type:o,duration:r,enterFromClass:i=`${n}-enter-from`,enterActiveClass:a=`${n}-enter-active`,enterToClass:s=`${n}-enter-to`,appearFromClass:l=i,appearActiveClass:c=a,appearToClass:u=s,leaveFromClass:d=`${n}-leave-from`,leaveActiveClass:f=`${n}-leave-active`,leaveToClass:h=`${n}-leave-to`}=e,p=function(e){if(null==e)return null;if(P(e))return[Pa(e.enter),Pa(e.leave)];{const t=Pa(e);return[t,t]}}(r),g=p&&p[0],m=p&&p[1],{onBeforeEnter:v,onEnter:y,onEnterCancelled:b,onLeave:_,onLeaveCancelled:w,onBeforeAppear:T=v,onAppear:k=y,onAppearCancelled:S=b}=t,C=(e,t,n)=>{La(e,t?u:s),La(e,t?c:a),n&&n()},E=(e,t)=>{e._isLeaving=!1,La(e,d),La(e,h),La(e,f),t&&t()},O=e=>(t,n)=>{const r=e?k:y,a=()=>C(t,e,n);Ia(r,[t,a]),Ra((()=>{La(t,e?l:i),$a(t,e?u:s),Aa(r)||Ba(t,o,g,a)}))};return x(t,{onBeforeEnter(e){Ia(v,[e]),$a(e,i),$a(e,a)},onBeforeAppear(e){Ia(T,[e]),$a(e,l),$a(e,c)},onEnter:O(!1),onAppear:O(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>E(e,t);$a(e,d),document.body.offsetHeight,$a(e,f),Ra((()=>{e._isLeaving&&(La(e,d),$a(e,h),Aa(_)||Ba(e,o,m,n))})),Ia(_,[e,n])},onEnterCancelled(e){C(e,!1),Ia(b,[e])},onAppearCancelled(e){C(e,!0),Ia(S,[e])},onLeaveCancelled(e){E(e),Ia(w,[e])}})}(e),t);Oa.displayName="Transition";const Ma={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};Oa.props=x({},Po,Ma);const Ia=(e,t=[])=>{C(e)?e.forEach((e=>e(...t))):e&&e(...t)},Aa=e=>!!e&&(C(e)?e.some((e=>e.length>1)):e.length>1);function Pa(e){const t=(e=>{const t=I(e)?Number(e):NaN;return isNaN(t)?e:t})(e);return t}function $a(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e._vtc||(e._vtc=new Set)).add(t)}function La(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const{_vtc:n}=e;n&&(n.delete(t),n.size||(e._vtc=void 0))}function Ra(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let ja=0;function Ba(e,t,n,o){const r=e._endId=++ja,i=()=>{r===e._endId&&o()};if(n)return setTimeout(i,n);const{type:a,timeout:s,propCount:l}=function(e,t){const n=window.getComputedStyle(e),o=e=>(n[e]||"").split(", "),r=o(`${Ca}Delay`),i=o(`${Ca}Duration`),a=Da(r,i),s=o(`${Ea}Delay`),l=o(`${Ea}Duration`),c=Da(s,l);let u=null,d=0,f=0;t===Ca?a>0&&(u=Ca,d=a,f=i.length):t===Ea?c>0&&(u=Ea,d=c,f=l.length):(d=Math.max(a,c),u=d>0?a>c?Ca:Ea:null,f=u?u===Ca?i.length:l.length:0);const h=u===Ca&&/\b(transform|all)(,|$)/.test(o(`${Ca}Property`).toString());return{type:u,timeout:d,propCount:f,hasTransform:h}}(e,t);if(!a)return o();const c=a+"end";let u=0;const d=()=>{e.removeEventListener(c,f),i()},f=t=>{t.target===e&&++u>=l&&d()};setTimeout((()=>{u<l&&d()}),s+1),e.addEventListener(c,f)}function Da(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map(((t,n)=>Na(t)+Na(e[n]))))}function Na(e){return 1e3*Number(e.slice(0,-1).replace(",","."))}const Fa=["ctrl","shift","alt","meta"],Va={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>Fa.some((n=>e[`${n}Key`]&&!t.includes(n)))},qa=(e,t)=>(n,...o)=>{for(let e=0;e<t.length;e++){const o=Va[t[e]];if(o&&o(n,t))return}return e(n,...o)},Wa={beforeMount(e,{value:t},{transition:n}){e._vod="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):Ha(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),Ha(e,!0),o.enter(e)):o.leave(e,(()=>{Ha(e,!1)})):Ha(e,t))},beforeUnmount(e,{value:t}){Ha(e,t)}};function Ha(e,t){e.style.display=t?e._vod:"none"}const za=x({patchProp:(e,t,n,o,r=!1,i,a,s,l)=>{if(0===t.indexOf("change:"))return function(e,t,n,o=null){if(!n||!o)return;const r=t.replace("change:",""),{attrs:i}=o,a=i[r],s=(e.__wxsProps||(e.__wxsProps={}))[r];if(s===a)return;e.__wxsProps[r]=a;const l=o.proxy;eo((()=>{n(a,s,l.$gcd(l,!0),l.$gcd(l,!1))}))}(e,t,o,a);"class"===t?function(e,t,n){const{__wxsAddClass:o,__wxsRemoveClass:r}=e;r&&r.length&&(t=(t||"").split(/\s+/).filter((e=>-1===r.indexOf(e))).join(" "),r.length=0),o&&o.length&&(t=(t||"")+" "+o.join(" "));const i=e._vtc;i&&(t=(t?[t,...i]:[...i]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,o,r):"style"===t?function(e,t,n){const o=e.style,r=I(n);if(n&&!r){if(t&&!I(t))for(const e in t)null==n[e]&&ra(o,e,"");for(const e in n)ra(o,e,n[e])}else{const i=o.display;r?t!==n&&(o.cssText=n):t&&e.removeAttribute("style"),"_vod"in e&&(o.display=i)}const{__wxsStyle:i}=e;if(i)for(const a in i)ra(o,a,i[a])}(e,n,o):_(t)?w(t)||ma(e,t,0,o,a):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,o){if(o)return"innerHTML"===t||"textContent"===t||!!(t in e&&xa.test(t)&&M(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if(xa.test(t)&&I(n))return!1;return t in e}(e,t,o,r))?function(e,t,n,o,r,i,a){if("innerHTML"===t||"textContent"===t)return o&&a(o,r,i),void(e[t]=null==n?"":n);if("value"===t&&"PROGRESS"!==e.tagName&&!e.tagName.includes("-")){e._value=n;const o=null==n?"":n;return e.value===o&&"OPTION"!==e.tagName||(e.value=o),void(null==n&&e.removeAttribute(t))}let s=!1;if(""===n||null==n){const o=typeof e[t];"boolean"===o?n=f(n):null==n&&"string"===o?(n="",s=!0):"number"===o&&(n=0,s=!0)}try{e[t]=n}catch(zE){}s&&e.removeAttribute(t)}(e,t,o,i,a,s,l):("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),function(e,t,n,o){if(o&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(ga,t.slice(6,t.length)):e.setAttributeNS(ga,t,n);else{const o=d(t);null==n||o&&!f(n)?e.removeAttribute(t):e.setAttribute(t,o?"":n)}}(e,t,o,r))},forcePatchProp:(e,t)=>0===t.indexOf("change:")||("class"===t&&e.__wxsClassChanged?(e.__wxsClassChanged=!1,!0):!("style"!==t||!e.__wxsStyleChanged)&&(e.__wxsStyleChanged=!1,!0))},na);let Ua;const Ya=(...e)=>{const t=(Ua||(Ua=ai(za))).createApp(...e),{mount:n}=t;return t.mount=e=>{const o=function(e){if(I(e)){return document.querySelector(e)}return e}(e);if(!o)return;const r=t._component;M(r)||r.render||r.template||(r.template=o.innerHTML),o.innerHTML="";const i=n(o,!1,o instanceof SVGElement);return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};const Xa=["{","}"];const Ga=/^(?:\d)+/,Ja=/^(?:\w)+/;const Ka="zh-Hans",Za="zh-Hant",Qa="en",es="fr",ts="es",ns=Object.prototype.hasOwnProperty,os=(e,t)=>ns.call(e,t),rs=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t,n=Xa){if(!t)return[e];let o=this._caches[e];return o||(o=function(e,[t,n]){const o=[];let r=0,i="";for(;r<e.length;){let a=e[r++];if(a===t){i&&o.push({type:"text",value:i}),i="";let t="";for(a=e[r++];void 0!==a&&a!==n;)t+=a,a=e[r++];const s=a===n,l=Ga.test(t)?"list":s&&Ja.test(t)?"named":"unknown";o.push({value:t,type:l})}else i+=a}return i&&o.push({type:"text",value:i}),o}(e,n),this._caches[e]=o),function(e,t){const n=[];let o=0;const r=Array.isArray(t)?"list":(i=t,null!==i&&"object"==typeof i?"named":"unknown");var i;if("unknown"===r)return n;for(;o<e.length;){const i=e[o];switch(i.type){case"text":n.push(i.value);break;case"list":n.push(t[parseInt(i.value,10)]);break;case"named":"named"===r&&n.push(t[i.value])}o++}return n}(o,t)}};function is(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return Ka;if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?Ka:e.indexOf("-hant")>-1?Za:(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?Za:Ka);var n;const o=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,[Qa,es,ts]);return o||void 0}class as{constructor({locale:e,fallbackLocale:t,messages:n,watcher:o,formater:r}){this.locale=Qa,this.fallbackLocale=Qa,this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=r||rs,this.messages=n||{},this.setLocale(e||Qa),o&&this.watchLocale(o)}setLocale(e){const t=this.locale;this.locale=is(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){const t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t,n=!0){const o=this.messages[e];o?n?Object.assign(o,t):Object.keys(t).forEach((e=>{os(o,e)||(o[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){let o=this.message;return"string"==typeof t?(t=is(t,this.messages))&&(o=this.messages[t]):n=t,os(o,e)?this.formater.interpolate(o[e],n).join(""):(console.warn(`Cannot translate the value of keypath ${e}. Use the value of keypath as default.`),e)}}function ss(e,t={},n,o){"string"!=typeof e&&([e,t]=[t,e]),"string"!=typeof e&&(e="undefined"!=typeof uni&&Qf?Qf():"undefined"!=typeof global&&global.getLocale?global.getLocale():Qa),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||Qa);const r=new as({locale:e,fallbackLocale:n,messages:t,watcher:o});let i=(e,t)=>{{let e=!1;i=function(t,n){const o=ov().$vm;return o&&(o.$locale,e||(e=!0,function(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}(o,r))),r.t(t,n)}}return i(e,t)};return{i18n:r,f:(e,t,n)=>r.f(e,t,n),t:(e,t)=>i(e,t),add:(e,t,n=!0)=>r.add(e,t,n),watch:e=>r.watchLocale(e),getLocale:()=>r.getLocale(),setLocale:e=>r.setLocale(e)}}function ls(e,t){return e.indexOf(t[0])>-1}
/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const cs="undefined"!=typeof document;function us(e){return"object"==typeof e||"displayName"in e||"props"in e||"__vccOpts"in e}const ds=Object.assign;function fs(e,t){const n={};for(const o in t){const r=t[o];n[o]=ps(r)?r.map(e):e(r)}return n}const hs=()=>{},ps=Array.isArray,gs=/#/g,ms=/&/g,vs=/\//g,ys=/=/g,bs=/\?/g,_s=/\+/g,ws=/%5B/g,xs=/%5D/g,Ts=/%5E/g,ks=/%60/g,Ss=/%7B/g,Cs=/%7C/g,Es=/%7D/g,Os=/%20/g;function Ms(e){return encodeURI(""+e).replace(Cs,"|").replace(ws,"[").replace(xs,"]")}function Is(e){return Ms(e).replace(_s,"%2B").replace(Os,"+").replace(gs,"%23").replace(ms,"%26").replace(ks,"`").replace(Ss,"{").replace(Es,"}").replace(Ts,"^")}function As(e){return null==e?"":function(e){return Ms(e).replace(gs,"%23").replace(bs,"%3F")}(e).replace(vs,"%2F")}function Ps(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const $s=/\/$/;function Ls(e,t,n="/"){let o,r={},i="",a="";const s=t.indexOf("#");let l=t.indexOf("?");return s<l&&s>=0&&(l=-1),l>-1&&(o=t.slice(0,l),i=t.slice(l+1,s>-1?s:t.length),r=e(i)),s>-1&&(o=o||t.slice(0,s),a=t.slice(s,t.length)),o=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/"),r=o[o.length-1];".."!==r&&"."!==r||o.push("");let i,a,s=n.length-1;for(i=0;i<o.length;i++)if(a=o[i],"."!==a){if(".."!==a)break;s>1&&s--}return n.slice(0,s).join("/")+"/"+o.slice(i).join("/")}(null!=o?o:t,n),{fullPath:o+(i&&"?")+i+a,path:o,query:r,hash:Ps(a)}}function Rs(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function js(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Bs(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Ds(e[n],t[n]))return!1;return!0}function Ds(e,t){return ps(e)?Ns(e,t):ps(t)?Ns(t,e):e===t}function Ns(e,t){return ps(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}const Fs={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Vs,qs,Ws,Hs;function zs(e){if(!e)if(cs){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace($s,"")}(qs=Vs||(Vs={})).pop="pop",qs.push="push",(Hs=Ws||(Ws={})).back="back",Hs.forward="forward",Hs.unknown="";const Us=/^[^#]+#/;function Ys(e,t){return e.replace(Us,"#")+t}const Xs=()=>({left:window.scrollX,top:window.scrollY});function Gs(e){let t;if("el"in e){const n=e.el,o="string"==typeof n&&n.startsWith("#"),r="string"==typeof n?o?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function Js(e,t){return(history.state?history.state.position-t:-1)+e}const Ks=new Map;function Zs(e,t){const{pathname:n,search:o,hash:r}=t,i=e.indexOf("#");if(i>-1){let t=r.includes(e.slice(i))?e.slice(i).length:1,n=r.slice(t);return"/"!==n[0]&&(n="/"+n),Rs(n,"")}return Rs(n,e)+o+r}function Qs(e,t,n,o=!1,r=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:r?Xs():null}}function el(e){const{history:t,location:n}=window,o={value:Zs(e,n)},r={value:t.state};function i(o,i,a){const s=e.indexOf("#"),l=s>-1?(n.host&&document.querySelector("base")?e:e.slice(s))+o:location.protocol+"//"+location.host+e+o;try{t[a?"replaceState":"pushState"](i,"",l),r.value=i}catch(c){console.error(c),n[a?"replace":"assign"](l)}}return r.value||i(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:o,state:r,push:function(e,n){const a=ds({},r.value,t.state,{forward:e,scroll:Xs()});i(a.current,a,!0),i(e,ds({},Qs(o.value,e,null),{position:a.position+1},n),!1),o.value=e},replace:function(e,n){i(e,ds({},t.state,Qs(r.value.back,e,r.value.forward,!0),n,{position:r.value.position}),!0),o.value=e}}}function tl(e){const t=el(e=zs(e)),n=function(e,t,n,o){let r=[],i=[],a=null;const s=({state:i})=>{const s=Zs(e,location),l=n.value,c=t.value;let u=0;if(i){if(n.value=s,t.value=i,a&&a===l)return void(a=null);u=c?i.position-c.position:0}else o(s);r.forEach((e=>{e(n.value,l,{delta:u,type:Vs.pop,direction:u?u>0?Ws.forward:Ws.back:Ws.unknown})}))};function l(){const{history:e}=window;e.state&&e.replaceState(ds({},e.state,{scroll:Xs()}),"")}return window.addEventListener("popstate",s),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:function(){a=n.value},listen:function(e){r.push(e);const t=()=>{const t=r.indexOf(e);t>-1&&r.splice(t,1)};return i.push(t),t},destroy:function(){for(const e of i)e();i=[],window.removeEventListener("popstate",s),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace);const o=ds({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:Ys.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function nl(e){return"string"==typeof e||"symbol"==typeof e}const ol=Symbol("");var rl,il;function al(e,t){return ds(new Error,{type:e,[ol]:!0},t)}function sl(e,t){return e instanceof Error&&ol in e&&(null==t||!!(e.type&t))}(il=rl||(rl={}))[il.aborted=4]="aborted",il[il.cancelled=8]="cancelled",il[il.duplicated=16]="duplicated";const ll="[^/]+?",cl={sensitive:!1,strict:!1,start:!0,end:!0},ul=/[.+*?^${}()[\]/\\]/g;function dl(e,t){let n=0;for(;n<e.length&&n<t.length;){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function fl(e,t){let n=0;const o=e.score,r=t.score;for(;n<o.length&&n<r.length;){const e=dl(o[n],r[n]);if(e)return e;n++}if(1===Math.abs(r.length-o.length)){if(hl(o))return 1;if(hl(r))return-1}return r.length-o.length}function hl(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const pl={type:0,value:""},gl=/[a-zA-Z0-9_]/;function ml(e,t,n){const o=function(e,t){const n=ds({},cl,t),o=[];let r=n.start?"^":"";const i=[];for(const l of e){const e=l.length?[]:[90];n.strict&&!l.length&&(r+="/");for(let t=0;t<l.length;t++){const o=l[t];let a=40+(n.sensitive?.25:0);if(0===o.type)t||(r+="/"),r+=o.value.replace(ul,"\\$&"),a+=40;else if(1===o.type){const{value:e,repeatable:n,optional:c,regexp:u}=o;i.push({name:e,repeatable:n,optional:c});const d=u||ll;if(d!==ll){a+=10;try{new RegExp(`(${d})`)}catch(s){throw new Error(`Invalid custom RegExp for param "${e}" (${d}): `+s.message)}}let f=n?`((?:${d})(?:/(?:${d}))*)`:`(${d})`;t||(f=c&&l.length<2?`(?:/${f})`:"/"+f),c&&(f+="?"),r+=f,a+=20,c&&(a+=-8),n&&(a+=-20),".*"===d&&(a+=-50)}e.push(a)}o.push(e)}if(n.strict&&n.end){const e=o.length-1;o[e][o[e].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const a=new RegExp(r,n.sensitive?"":"i");return{re:a,score:o,keys:i,parse:function(e){const t=e.match(a),n={};if(!t)return null;for(let o=1;o<t.length;o++){const e=t[o]||"",r=i[o-1];n[r.name]=e&&r.repeatable?e.split("/"):e}return n},stringify:function(t){let n="",o=!1;for(const r of e){o&&n.endsWith("/")||(n+="/"),o=!1;for(const e of r)if(0===e.type)n+=e.value;else if(1===e.type){const{value:i,repeatable:a,optional:s}=e,l=i in t?t[i]:"";if(ps(l)&&!a)throw new Error(`Provided param "${i}" is an array but it is not repeatable (* or + modifiers)`);const c=ps(l)?l.join("/"):l;if(!c){if(!s)throw new Error(`Missing required param "${i}"`);r.length<2&&(n.endsWith("/")?n=n.slice(0,-1):o=!0)}n+=c}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[pl]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${c}": ${e}`)}let n=0,o=n;const r=[];let i;function a(){i&&r.push(i),i=[]}let s,l=0,c="",u="";function d(){c&&(0===n?i.push({type:0,value:c}):1===n||2===n||3===n?(i.length>1&&("*"===s||"+"===s)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:c,regexp:u,repeatable:"*"===s||"+"===s,optional:"*"===s||"?"===s})):t("Invalid state to consume buffer"),c="")}function f(){c+=s}for(;l<e.length;)if(s=e[l++],"\\"!==s||2===n)switch(n){case 0:"/"===s?(c&&d(),a()):":"===s?(d(),n=1):f();break;case 4:f(),n=o;break;case 1:"("===s?n=2:gl.test(s)?f():(d(),n=0,"*"!==s&&"?"!==s&&"+"!==s&&l--);break;case 2:")"===s?"\\"==u[u.length-1]?u=u.slice(0,-1)+s:n=3:u+=s;break;case 3:d(),n=0,"*"!==s&&"?"!==s&&"+"!==s&&l--,u="";break;default:t("Unknown state")}else o=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${c}"`),d(),a(),r}(e.path),n),r=ds(o,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function vl(e,t){const n=[],o=new Map;function r(e,n,o){const s=!o,l=bl(e);l.aliasOf=o&&o.record;const c=Tl(t,e),u=[l];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(bl(ds({},l,{components:o?o.record.components:l.components,path:e,aliasOf:o?o.record:l})))}let d,f;for(const t of u){const{path:u}=t;if(n&&"/"!==u[0]){const e=n.record.path,o="/"===e[e.length-1]?"":"/";t.path=n.record.path+(u&&o+u)}if(d=ml(t,n,c),o?o.alias.push(d):(f=f||d,f!==d&&f.alias.push(d),s&&e.name&&!wl(d)&&i(e.name)),kl(d)&&a(d),l.children){const e=l.children;for(let t=0;t<e.length;t++)r(e[t],d,o&&o.children[t])}o=o||d}return f?()=>{i(f)}:hs}function i(e){if(nl(e)){const t=o.get(e);t&&(o.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(i),t.alias.forEach(i))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&o.delete(e.record.name),e.children.forEach(i),e.alias.forEach(i))}}function a(e){const t=function(e,t){let n=0,o=t.length;for(;n!==o;){const r=n+o>>1;fl(e,t[r])<0?o=r:n=r+1}const r=function(e){let t=e;for(;t=t.parent;)if(kl(t)&&0===fl(e,t))return t;return}(e);r&&(o=t.lastIndexOf(r,o-1));return o}(e,n);n.splice(t,0,e),e.record.name&&!wl(e)&&o.set(e.record.name,e)}return t=Tl({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>r(e))),{addRoute:r,resolve:function(e,t){let r,i,a,s={};if("name"in e&&e.name){if(r=o.get(e.name),!r)throw al(1,{location:e});a=r.record.name,s=ds(yl(t.params,r.keys.filter((e=>!e.optional)).concat(r.parent?r.parent.keys.filter((e=>e.optional)):[]).map((e=>e.name))),e.params&&yl(e.params,r.keys.map((e=>e.name)))),i=r.stringify(s)}else if(null!=e.path)i=e.path,r=n.find((e=>e.re.test(i))),r&&(s=r.parse(i),a=r.record.name);else{if(r=t.name?o.get(t.name):n.find((e=>e.re.test(t.path))),!r)throw al(1,{location:e,currentLocation:t});a=r.record.name,s=ds({},t.params,e.params),i=r.stringify(s)}const l=[];let c=r;for(;c;)l.unshift(c.record),c=c.parent;return{name:a,path:i,params:s,matched:l,meta:xl(l)}},removeRoute:i,clearRoutes:function(){n.length=0,o.clear()},getRoutes:function(){return n},getRecordMatcher:function(e){return o.get(e)}}}function yl(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function bl(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:_l(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function _l(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]="object"==typeof n?n[o]:n;return t}function wl(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function xl(e){return e.reduce(((e,t)=>ds(e,t.meta)),{})}function Tl(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function kl({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Sl(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(_s," "),r=e.indexOf("="),i=Ps(r<0?e:e.slice(0,r)),a=r<0?null:Ps(e.slice(r+1));if(i in t){let e=t[i];ps(e)||(e=t[i]=[e]),e.push(a)}else t[i]=a}return t}function Cl(e){let t="";for(let n in e){const o=e[n];if(n=Is(n).replace(ys,"%3D"),null==o){void 0!==o&&(t+=(t.length?"&":"")+n);continue}(ps(o)?o.map((e=>e&&Is(e))):[o&&Is(o)]).forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function El(e){const t={};for(const n in e){const o=e[n];void 0!==o&&(t[n]=ps(o)?o.map((e=>null==e?null:""+e)):null==o?o:""+o)}return t}const Ol=Symbol(""),Ml=Symbol(""),Il=Symbol(""),Al=Symbol(""),Pl=Symbol("");function $l(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function Ll(e,t,n,o,r,i=e=>e()){const a=o&&(o.enterCallbacks[r]=o.enterCallbacks[r]||[]);return()=>new Promise(((s,l)=>{const c=e=>{var i;!1===e?l(al(4,{from:n,to:t})):e instanceof Error?l(e):"string"==typeof(i=e)||i&&"object"==typeof i?l(al(2,{from:t,to:e})):(a&&o.enterCallbacks[r]===a&&"function"==typeof e&&a.push(e),s())},u=i((()=>e.call(o&&o.instances[r],t,n,c)));let d=Promise.resolve(u);e.length<3&&(d=d.then(c)),d.catch((e=>l(e)))}))}function Rl(e,t,n,o,r=e=>e()){const i=[];for(const a of e)for(const e in a.components){let s=a.components[e];if("beforeRouteEnter"===t||a.instances[e])if(us(s)){const l=(s.__vccOpts||s)[t];l&&i.push(Ll(l,n,o,a,e,r))}else{let l=s();i.push((()=>l.then((i=>{if(!i)throw new Error(`Couldn't resolve component "${e}" at "${a.path}"`);const s=(l=i).__esModule||"Module"===l[Symbol.toStringTag]||l.default&&us(l.default)?i.default:i;var l;a.mods[e]=i,a.components[e]=s;const c=(s.__vccOpts||s)[t];return c&&Ll(c,n,o,a,e,r)()}))))}}return i}function jl(e){const t=To(Il),n=To(Al),o=Gi((()=>{const n=Rn(e.to);return t.resolve(n)})),r=Gi((()=>{const{matched:e}=o.value,{length:t}=e,r=e[t-1],i=n.matched;if(!r||!i.length)return-1;const a=i.findIndex(js.bind(null,r));if(a>-1)return a;const s=Dl(e[t-2]);return t>1&&Dl(r)===s&&i[i.length-1].path!==s?i.findIndex(js.bind(null,e[t-2])):a})),i=Gi((()=>r.value>-1&&function(e,t){for(const n in t){const o=t[n],r=e[n];if("string"==typeof o){if(o!==r)return!1}else if(!ps(r)||r.length!==o.length||o.some(((e,t)=>e!==r[t])))return!1}return!0}(n.params,o.value.params))),a=Gi((()=>r.value>-1&&r.value===n.matched.length-1&&Bs(n.params,o.value.params)));return{route:o,href:Gi((()=>o.value.href)),isActive:i,isExactActive:a,navigate:function(n={}){if(function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(n)){const n=t[Rn(e.replace)?"replace":"push"](Rn(e.to)).catch(hs);return e.viewTransition&&"undefined"!=typeof document&&"startViewTransition"in document&&document.startViewTransition((()=>n)),n}return Promise.resolve()}}}const Bl=Vo({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:jl,setup(e,{slots:t}){const n=mn(jl(e)),{options:o}=To(Il),r=Gi((()=>({[Nl(e.activeClass,o.linkActiveClass,"router-link-active")]:n.isActive,[Nl(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const o=t.default&&(1===(i=t.default(n)).length?i[0]:i);var i;return e.custom?o:Ji("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}});function Dl(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Nl=(e,t,n)=>null!=e?e:null!=t?t:n,Fl=Vo({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const o=To(Pl),r=Gi((()=>e.route||o.value)),i=To(Ml,0),a=Gi((()=>{let e=Rn(i);const{matched:t}=r.value;let n;for(;(n=t[e])&&!n.components;)e++;return e})),s=Gi((()=>r.value.matched[a.value]));xo(Ml,Gi((()=>a.value+1))),xo(Ol,s),xo(Pl,r);const l=An();return Co((()=>[l.value,s.value,e.name]),(([e,t,n],[o,r,i])=>{t&&(t.instances[n]=e,r&&r!==t&&e&&e===o&&(t.leaveGuards.size||(t.leaveGuards=r.leaveGuards),t.updateGuards.size||(t.updateGuards=r.updateGuards))),!e||!t||r&&js(t,r)&&o||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const o=r.value,i=e.name,a=s.value,c=a&&a.components[i];if(!c)return Vl(n.default,{Component:c,route:o});const u=a.props[i],d=u?!0===u?o.params:"function"==typeof u?u(o):u:null,f=Ji(c,ds({},d,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(a.instances[i]=null)},ref:l}));return Vl(n.default,{Component:f,route:o})||f}}});function Vl(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const ql=Fl;function Wl(e){const t=vl(e.routes,e),n=e.parseQuery||Sl,o=e.stringifyQuery||Cl,r=e.history,i=$l(),a=$l(),s=$l(),l=Pn(Fs);let c=Fs;cs&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=fs.bind(null,(e=>""+e)),d=fs.bind(null,As),f=fs.bind(null,Ps);function h(e,i){if(i=ds({},i||l.value),"string"==typeof e){const o=Ls(n,e,i.path),a=t.resolve({path:o.path},i),s=r.createHref(o.fullPath);return ds(o,a,{params:f(a.params),hash:Ps(o.hash),redirectedFrom:void 0,href:s})}let a;if(null!=e.path)a=ds({},e,{path:Ls(n,e.path,i.path).path});else{const t=ds({},e.params);for(const e in t)null==t[e]&&delete t[e];a=ds({},e,{params:d(t)}),i.params=d(i.params)}const s=t.resolve(a,i),c=e.hash||"";s.params=u(f(s.params));const h=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(o,ds({},e,{hash:(p=c,Ms(p).replace(Ss,"{").replace(Es,"}").replace(Ts,"^")),path:s.path}));var p;const g=r.createHref(h);return ds({fullPath:h,hash:c,query:o===Cl?El(e.query):e.query||{}},s,{redirectedFrom:void 0,href:g})}function p(e){return"string"==typeof e?Ls(n,e,l.value.path):ds({},e)}function g(e,t){if(c!==e)return al(8,{from:t,to:e})}function m(e){return y(e)}function v(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let o="function"==typeof n?n(e):n;return"string"==typeof o&&(o=o.includes("?")||o.includes("#")?o=p(o):{path:o},o.params={}),ds({query:e.query,hash:e.hash,params:null!=o.path?{}:e.params},o)}}function y(e,t){const n=c=h(e),r=l.value,i=e.state,a=e.force,s=!0===e.replace,u=v(n);if(u)return y(ds(p(u),{state:"object"==typeof u?ds({},i,u.state):i,force:a,replace:s}),t||n);const d=n;let f;return d.redirectedFrom=t,!a&&function(e,t,n){const o=t.matched.length-1,r=n.matched.length-1;return o>-1&&o===r&&js(t.matched[o],n.matched[r])&&Bs(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(o,r,n)&&(f=al(16,{to:d,from:r}),A(r,r,!0,!1)),(f?Promise.resolve(f):w(d,r)).catch((e=>sl(e)?sl(e,2)?e:I(e):M(e,d,r))).then((e=>{if(e){if(sl(e,2))return y(ds({replace:s},p(e.to),{state:"object"==typeof e.to?ds({},i,e.to.state):i,force:a}),t||d)}else e=T(d,r,!0,s,i);return x(d,r,e),e}))}function b(e,t){const n=g(e,t);return n?Promise.reject(n):Promise.resolve()}function _(e){const t=L.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function w(e,t){let n;const[o,r,s]=function(e,t){const n=[],o=[],r=[],i=Math.max(t.matched.length,e.matched.length);for(let a=0;a<i;a++){const i=t.matched[a];i&&(e.matched.find((e=>js(e,i)))?o.push(i):n.push(i));const s=e.matched[a];s&&(t.matched.find((e=>js(e,s)))||r.push(s))}return[n,o,r]}(e,t);n=Rl(o.reverse(),"beforeRouteLeave",e,t);for(const i of o)i.leaveGuards.forEach((o=>{n.push(Ll(o,e,t))}));const l=b.bind(null,e,t);return n.push(l),j(n).then((()=>{n=[];for(const o of i.list())n.push(Ll(o,e,t));return n.push(l),j(n)})).then((()=>{n=Rl(r,"beforeRouteUpdate",e,t);for(const o of r)o.updateGuards.forEach((o=>{n.push(Ll(o,e,t))}));return n.push(l),j(n)})).then((()=>{n=[];for(const o of s)if(o.beforeEnter)if(ps(o.beforeEnter))for(const r of o.beforeEnter)n.push(Ll(r,e,t));else n.push(Ll(o.beforeEnter,e,t));return n.push(l),j(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=Rl(s,"beforeRouteEnter",e,t,_),n.push(l),j(n)))).then((()=>{n=[];for(const o of a.list())n.push(Ll(o,e,t));return n.push(l),j(n)})).catch((e=>sl(e,8)?e:Promise.reject(e)))}function x(e,t,n){s.list().forEach((o=>_((()=>o(e,t,n)))))}function T(e,t,n,o,i){const a=g(e,t);if(a)return a;const s=t===Fs,c=cs?history.state:{};n&&(o||s?r.replace(e.fullPath,ds({scroll:s&&c&&c.scroll},i)):r.push(e.fullPath,i)),l.value=e,A(e,t,n,s),I()}let k;function S(){k||(k=r.listen(((e,t,n)=>{if(!R.listening)return;const o=h(e),i=v(o);if(i)return void y(ds(i,{replace:!0,force:!0}),o).catch(hs);c=o;const a=l.value;cs&&function(e,t){Ks.set(e,t)}(Js(a.fullPath,n.delta),Xs()),w(o,a).catch((e=>sl(e,12)?e:sl(e,2)?(y(ds(p(e.to),{force:!0}),o).then((e=>{sl(e,20)&&!n.delta&&n.type===Vs.pop&&r.go(-1,!1)})).catch(hs),Promise.reject()):(n.delta&&r.go(-n.delta,!1),M(e,o,a)))).then((e=>{(e=e||T(o,a,!1))&&(n.delta&&!sl(e,8)?r.go(-n.delta,!1):n.type===Vs.pop&&sl(e,20)&&r.go(-1,!1)),x(o,a,e)})).catch(hs)})))}let C,E=$l(),O=$l();function M(e,t,n){I(e);const o=O.list();return o.length?o.forEach((o=>o(e,t,n))):console.error(e),Promise.reject(e)}function I(e){return C||(C=!e,S(),E.list().forEach((([t,n])=>e?n(e):t())),E.reset()),e}function A(t,n,o,r){const{scrollBehavior:i}=e;if(!cs||!i)return Promise.resolve();const a=!o&&function(e){const t=Ks.get(e);return Ks.delete(e),t}(Js(t.fullPath,0))||(r||!o)&&history.state&&history.state.scroll||null;return eo().then((()=>i(t,n,a))).then((e=>e&&Gs(e))).catch((e=>M(e,t,n)))}const P=e=>r.go(e);let $;const L=new Set,R={currentRoute:l,listening:!0,addRoute:function(e,n){let o,r;return nl(e)?(o=t.getRecordMatcher(e),r=n):r=e,t.addRoute(r,o)},removeRoute:function(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)},clearRoutes:t.clearRoutes,hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map((e=>e.record))},resolve:h,options:e,push:m,replace:function(e){return m(ds(p(e),{replace:!0}))},go:P,back:()=>P(-1),forward:()=>P(1),beforeEach:i.add,beforeResolve:a.add,afterEach:s.add,onError:O.add,isReady:function(){return C&&l.value!==Fs?Promise.resolve():new Promise(((e,t)=>{E.add([e,t])}))},install(e){e.component("RouterLink",Bl),e.component("RouterView",ql),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>Rn(l)}),cs&&!$&&l.value===Fs&&($=!0,m(r.location).catch((e=>{})));const t={};for(const o in Fs)Object.defineProperty(t,o,{get:()=>l.value[o],enumerable:!0});e.provide(Il,this),e.provide(Al,vn(t)),e.provide(Pl,l);const n=e.unmount;L.add(e),e.unmount=function(){L.delete(e),L.size<1&&(c=Fs,k&&k(),k=null,l.value=Fs,$=!1,C=!1),n()}}};function j(e){return e.reduce(((e,t)=>e.then((()=>_(t)))),Promise.resolve())}return R}function Hl(e){return To(Al)}const zl=Be((()=>"undefined"!=typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length));let Ul;function Yl(e){return ls(e,Q)?Jl().f(e,function(){const e=Qf(),t=__uniConfig.locales;return t[e]||t[__uniConfig.fallbackLocale]||t.en||{}}(),Q):e}function Xl(e,t){if(1===t.length){if(e){const n=e=>I(e)&&ls(e,Q),o=t[0];let r=[];if(C(e)&&(r=e.filter((e=>n(e[o])))).length)return r;const i=e[t[0]];if(n(i))return e}return}const n=t.shift();return Xl(e&&e[n],t)}function Gl(e,t){const n=Xl(e,t);if(!n)return!1;const o=t[t.length-1];if(C(n))n.forEach((e=>Gl(e,[o])));else{let e=n[o];Object.defineProperty(n,o,{get:()=>Yl(e),set(t){e=t}})}return!0}function Jl(){if(!Ul){let e;if(e=window.localStorage&&localStorage.UNI_LOCALE||__uniConfig.locale||navigator.language,Ul=ss(e),zl()){const t=Object.keys(__uniConfig.locales||{});t.length&&t.forEach((e=>Ul.add(e,__uniConfig.locales[e]))),Ul.setLocale(e)}}return Ul}function Kl(e,t,n){return t.reduce(((t,o,r)=>(t[e+o]=n[r],t)),{})}const Zl=Be((()=>{const e="uni.async.",t=["error"];Jl().add(Qa,Kl(e,t,["The connection timed out, click the screen to try again."]),!1),Jl().add(ts,Kl(e,t,["Se agotó el tiempo de conexión, haga clic en la pantalla para volver a intentarlo."]),!1),Jl().add(es,Kl(e,t,["La connexion a expiré, cliquez sur l'écran pour réessayer."]),!1),Jl().add(Ka,Kl(e,t,["连接服务器超时，点击屏幕重试"]),!1),Jl().add(Za,Kl(e,t,["連接服務器超時，點擊屏幕重試"]),!1)})),Ql=Be((()=>{const e="uni.showActionSheet.",t=["cancel"];Jl().add(Qa,Kl(e,t,["Cancel"]),!1),Jl().add(ts,Kl(e,t,["Cancelar"]),!1),Jl().add(es,Kl(e,t,["Annuler"]),!1),Jl().add(Ka,Kl(e,t,["取消"]),!1),Jl().add(Za,Kl(e,t,["取消"]),!1)})),ec=Be((()=>{const e="uni.showToast.",t=["unpaired"];Jl().add(Qa,Kl(e,t,["Please note showToast must be paired with hideToast"]),!1),Jl().add(ts,Kl(e,t,["Tenga en cuenta que showToast debe estar emparejado con hideToast"]),!1),Jl().add(es,Kl(e,t,["Veuillez noter que showToast doit être associé à hideToast"]),!1),Jl().add(Ka,Kl(e,t,["请注意 showToast 与 hideToast 必须配对使用"]),!1),Jl().add(Za,Kl(e,t,["請注意 showToast 與 hideToast 必須配對使用"]),!1)})),tc=Be((()=>{const e="uni.showLoading.",t=["unpaired"];Jl().add(Qa,Kl(e,t,["Please note showLoading must be paired with hideLoading"]),!1),Jl().add(ts,Kl(e,t,["Tenga en cuenta que showLoading debe estar emparejado con hideLoading"]),!1),Jl().add(es,Kl(e,t,["Veuillez noter que showLoading doit être associé à hideLoading"]),!1),Jl().add(Ka,Kl(e,t,["请注意 showLoading 与 hideLoading 必须配对使用"]),!1),Jl().add(Za,Kl(e,t,["請注意 showLoading 與 hideLoading 必須配對使用"]),!1)})),nc=Be((()=>{const e="uni.showModal.",t=["cancel","confirm"];Jl().add(Qa,Kl(e,t,["Cancel","OK"]),!1),Jl().add(ts,Kl(e,t,["Cancelar","OK"]),!1),Jl().add(es,Kl(e,t,["Annuler","OK"]),!1),Jl().add(Ka,Kl(e,t,["取消","确定"]),!1),Jl().add(Za,Kl(e,t,["取消","確定"]),!1)})),oc=Be((()=>{const e="uni.chooseFile.",t=["notUserActivation"];Jl().add(Qa,Kl(e,t,["File chooser dialog can only be shown with a user activation"]),!1),Jl().add(ts,Kl(e,t,["El cuadro de diálogo del selector de archivos solo se puede mostrar con la activación del usuario"]),!1),Jl().add(es,Kl(e,t,["La boîte de dialogue du sélecteur de fichier ne peut être affichée qu'avec une activation par l'utilisateur"]),!1),Jl().add(Ka,Kl(e,t,["文件选择器对话框只能在用户激活时显示"]),!1),Jl().add(Za,Kl(e,t,["文件選擇器對話框只能在用戶激活時顯示"]),!1)})),rc=Be((()=>{const e="uni.setClipboardData.",t=["success","fail"];Jl().add(Qa,Kl(e,t,["Content copied","Copy failed, please copy manually"]),!1),Jl().add(ts,Kl(e,t,["Contenido copiado","Error al copiar, copie manualmente"]),!1),Jl().add(es,Kl(e,t,["Contenu copié","Échec de la copie, copiez manuellement"]),!1),Jl().add(Ka,Kl(e,t,["内容已复制","复制失败，请手动复制"]),!1),Jl().add(Za,Kl(e,t,["內容已復制","復制失敗，請手動復製"]),!1)})),ic=Be((()=>{const e="uni.picker.",t=["done","cancel"];Jl().add(Qa,Kl(e,t,["Done","Cancel"]),!1),Jl().add(ts,Kl(e,t,["OK","Cancelar"]),!1),Jl().add(es,Kl(e,t,["OK","Annuler"]),!1),Jl().add(Ka,Kl(e,t,["完成","取消"]),!1),Jl().add(Za,Kl(e,t,["完成","取消"]),!1)}));function ac(e){const t=new rt;return{on:(e,n)=>t.on(e,n),once:(e,n)=>t.once(e,n),off:(e,n)=>t.off(e,n),emit:(e,...n)=>t.emit(e,...n),subscribe(n,o,r=!1){t[r?"once":"on"](`${e}.${n}`,o)},unsubscribe(n,o){t.off(`${e}.${n}`,o)},subscribeHandler(n,o,r){t.emit(`${e}.${n}`,o,r)}}}const sc="invokeViewApi",lc="invokeServiceApi";let cc=1;const uc=Object.create(null);function dc(e,t){return e+"."+t}function fc(e,t,n){t=dc(e,t),uc[t]||(uc[t]=n)}function hc({id:e,name:t,args:n},o){t=dc(o,t);const r=t=>{e&&Wb.publishHandler(sc+"."+e,t)},i=uc[t];i?i(n,r):r({})}const pc=x(ac("service"),{invokeServiceMethod:(e,t,n)=>{const{subscribe:o,publishHandler:r}=Wb,i=n?cc++:0;n&&o(lc+"."+i,n,!0),r(lc,{id:i,name:e,args:t})}}),gc=He(!0);let mc;function vc(){mc&&(clearTimeout(mc),mc=null)}let yc=0,bc=0;function _c(e){if(vc(),1!==e.touches.length)return;const{pageX:t,pageY:n}=e.touches[0];yc=t,bc=n,mc=setTimeout((function(){const t=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget});t.touches=e.touches,t.changedTouches=e.changedTouches,e.target.dispatchEvent(t)}),350)}function wc(e){if(!mc)return;if(1!==e.touches.length)return vc();const{pageX:t,pageY:n}=e.touches[0];return Math.abs(t-yc)>10||Math.abs(n-bc)>10?vc():void 0}function xc(e,t){const n=Number(e);return isNaN(n)?t:n}function Tc(){const e=__uniConfig.globalStyle||{},t=xc(e.rpxCalcMaxDeviceWidth,960),n=xc(e.rpxCalcBaseDeviceWidth,375);function o(){let e=function(){const e=/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation,t=e&&90===Math.abs(window.orientation);var n=e?Math[t?"max":"min"](screen.width,screen.height):screen.width;return Math.min(window.innerWidth,document.documentElement.clientWidth,n)||n}();e=e<=t?e:n,document.documentElement.style.fontSize=e/23.4375+"px"}o(),document.addEventListener("DOMContentLoaded",o),window.addEventListener("load",o),window.addEventListener("resize",o)}function kc(){Tc(),Ve(),window.addEventListener("touchstart",_c,gc),window.addEventListener("touchmove",wc,gc),window.addEventListener("touchend",vc,gc),window.addEventListener("touchcancel",vc,gc)}var Sc,Cc,Ec=["top","left","right","bottom"],Oc={};function Mc(){return Cc="CSS"in window&&"function"==typeof CSS.supports?CSS.supports("top: env(safe-area-inset-top)")?"env":CSS.supports("top: constant(safe-area-inset-top)")?"constant":"":""}function Ic(){if(Cc="string"==typeof Cc?Cc:Mc()){var e=[],t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t={passive:!0}}});window.addEventListener("test",null,n)}catch(zE){}var o=document.createElement("div");r(o,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),Ec.forEach((function(e){a(o,e)})),document.body.appendChild(o),i(),Sc=!0}else Ec.forEach((function(e){Oc[e]=0}));function r(e,t){var n=e.style;Object.keys(t).forEach((function(e){var o=t[e];n[e]=o}))}function i(t){t?e.push(t):e.forEach((function(e){e()}))}function a(e,n){var o=document.createElement("div"),a=document.createElement("div"),s=document.createElement("div"),l=document.createElement("div"),c={position:"absolute",width:"100px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:Cc+"(safe-area-inset-"+n+")"};r(o,c),r(a,c),r(s,{transition:"0s",animation:"none",width:"400px",height:"400px"}),r(l,{transition:"0s",animation:"none",width:"250%",height:"250%"}),o.appendChild(s),a.appendChild(l),e.appendChild(o),e.appendChild(a),i((function(){o.scrollTop=a.scrollTop=1e4;var e=o.scrollTop,r=a.scrollTop;function i(){this.scrollTop!==(this===o?e:r)&&(o.scrollTop=a.scrollTop=1e4,e=o.scrollTop,r=a.scrollTop,function(e){Pc.length||setTimeout((function(){var e={};Pc.forEach((function(t){e[t]=Oc[t]})),Pc.length=0,$c.forEach((function(t){t(e)}))}),0);Pc.push(e)}(n))}o.addEventListener("scroll",i,t),a.addEventListener("scroll",i,t)}));var u=getComputedStyle(o);Object.defineProperty(Oc,n,{configurable:!0,get:function(){return parseFloat(u.paddingBottom)}})}}function Ac(e){return Sc||Ic(),Oc[e]}var Pc=[];var $c=[];var Lc={get support(){return 0!=("string"==typeof Cc?Cc:Mc()).length},get top(){return Ac("top")},get left(){return Ac("left")},get right(){return Ac("right")},get bottom(){return Ac("bottom")},onChange:function(e){Mc()&&(Sc||Ic(),"function"==typeof e&&$c.push(e))},offChange:function(e){var t=$c.indexOf(e);t>=0&&$c.splice(t,1)}};const Rc=qa((()=>{}),["prevent"]),jc=qa((()=>{}),["stop"]);function Bc(e,t){return parseInt((e.getPropertyValue(t).match(/\d+/)||["0"])[0])}function Dc(){const e=Bc(document.documentElement.style,"--window-top");return e?e+Lc.top:0}function Nc(){const e=document.documentElement.style,t=Dc(),n=Bc(e,"--window-bottom"),o=Bc(e,"--window-left"),r=Bc(e,"--window-right"),i=Bc(e,"--top-window-height");return{top:t,bottom:n?n+Lc.bottom:0,left:o?o+Lc.left:0,right:r?r+Lc.right:0,topWindowHeight:i||0}}function Fc(e){const t=document.documentElement.style;Object.keys(e).forEach((n=>{t.setProperty(n,e[n])}))}function Vc(e){return Fc(e)}function qc(e){return Symbol(e)}function Wc(e){return-1!==(e+="").indexOf("rpx")||-1!==e.indexOf("upx")}function Hc(e,t=!1){if(t)return function(e){if(!Wc(e))return e;return e.replace(/(\d+(\.\d+)?)[ru]px/g,((e,t)=>of(parseFloat(t))+"px"))}(e);if(I(e)){const t=parseInt(e)||0;return Wc(e)?of(t):t}return e}const zc="M1.952 18.080q-0.32-0.352-0.416-0.88t0.128-0.976l0.16-0.352q0.224-0.416 0.64-0.528t0.8 0.176l6.496 4.704q0.384 0.288 0.912 0.272t0.88-0.336l17.312-14.272q0.352-0.288 0.848-0.256t0.848 0.352l-0.416-0.416q0.32 0.352 0.32 0.816t-0.32 0.816l-18.656 18.912q-0.32 0.352-0.8 0.352t-0.8-0.32l-7.936-8.064z",Uc="M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM15.136 8.672h1.728q0.128 0 0.224 0.096t0.096 0.256l-0.384 10.24q0 0.064-0.048 0.112t-0.112 0.048h-1.248q-0.096 0-0.144-0.048t-0.048-0.112l-0.384-10.24q0-0.16 0.096-0.256t0.224-0.096zM16 23.328q-0.48 0-0.832-0.352t-0.352-0.848 0.352-0.848 0.832-0.352 0.832 0.352 0.352 0.848-0.352 0.848-0.832 0.352z";function Yc(e,t="#000",n=27){return Ei("svg",{width:n,height:n,viewBox:"0 0 32 32"},[Ei("path",{d:e,fill:t},null,8,["d","fill"])],8,["width","height"])}function Xc(){{const{$pageInstance:e}=Ni();return e&&e.proxy.$page.id}}function Gc(e){const t=Ie(e);if(t.$page)return t.$page.id;if(t.$){const{$pageInstance:e}=t.$;return e&&e.proxy.$page.id}}function Jc(){const e=$m(),t=e.length;if(t)return e[t-1]}function Kc(){const e=Jc();if(e)return e.$page.meta}function Zc(){const e=Kc();return e?e.id:-1}function Qc(){const e=Jc();if(e)return e.$vm}const eu=["navigationBar","pullToRefresh"];function tu(e,t){const n=JSON.parse(JSON.stringify(__uniConfig.globalStyle||{})),o=x({id:t},n,e);eu.forEach((t=>{o[t]=x({},n[t],e[t])}));const{navigationBar:r}=o;return r.titleText&&r.titleImage&&(r.titleText=""),o}function nu(e,t,n){if(I(e))n=t,t=e,e=Qc();else if("number"==typeof e){const t=$m().find((t=>t.$page.id===e));e=t?t.$vm:Qc()}if(!e)return;const o=e.$[t];return o&&((e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n})(o,n)}function ou(e){e.preventDefault()}let ru,iu=0;function au({onPageScroll:e,onReachBottom:t,onReachBottomDistance:n}){let o=!1,r=!1,i=!0;const a=()=>{function a(){if((()=>{const{scrollHeight:e}=document.documentElement,t=window.innerHeight,o=window.scrollY,i=o>0&&e>t&&o+t+n>=e,a=Math.abs(e-iu)>n;return!i||r&&!a?(!i&&r&&(r=!1),!1):(iu=e,r=!0,!0)})())return t&&t(),i=!1,setTimeout((function(){i=!0}),350),!0}e&&e(window.pageYOffset),t&&i&&(a()||(ru=setTimeout(a,300))),o=!1};return function(){clearTimeout(ru),o||requestAnimationFrame(a),o=!0}}function su(e,t){if(0===t.indexOf("/"))return t;if(0===t.indexOf("./"))return su(e,t.slice(2));const n=t.split("/"),o=n.length;let r=0;for(;r<o&&".."===n[r];r++);n.splice(0,r),t=n.join("/");const i=e.length>0?e.split("/"):[];return i.splice(i.length-r-1,r+1),Le(i.concat(n).join("/"))}function lu(e,t=!1){return t?__uniRoutes.find((t=>t.path===e||t.alias===e)):__uniRoutes.find((t=>t.path===e))}class cu{constructor(e){this.$bindClass=!1,this.$bindStyle=!1,this.$vm=e,this.$el=function(e,t=!1){const{vnode:n}=e;if(Pe(n.el))return t?n.el?[n.el]:[]:n.el;const{subTree:o}=e;if(16&o.shapeFlag){const e=o.children.filter((e=>e.el&&Pe(e.el)));if(e.length>0)return t?e.map((e=>e.el)):e[0].el}return t?n.el?[n.el]:[]:n.el}(e.$),this.$el.getAttribute&&(this.$bindClass=!!this.$el.getAttribute("class"),this.$bindStyle=!!this.$el.getAttribute("style"))}selectComponent(e){if(!this.$el||!e)return;const t=hu(this.$el.querySelector(e));return t?uu(t,!1):void 0}selectAllComponents(e){if(!this.$el||!e)return[];const t=[],n=this.$el.querySelectorAll(e);for(let o=0;o<n.length;o++){const e=hu(n[o]);e&&t.push(uu(e,!1))}return t}forceUpdate(e){"class"===e?this.$bindClass?(this.$el.__wxsClassChanged=!0,this.$vm.$forceUpdate()):this.updateWxsClass():"style"===e&&(this.$bindStyle?(this.$el.__wxsStyleChanged=!0,this.$vm.$forceUpdate()):this.updateWxsStyle())}updateWxsClass(){const{__wxsAddClass:e}=this.$el;e.length&&(this.$el.className=e.join(" "))}updateWxsStyle(){const{__wxsStyle:e}=this.$el;e&&this.$el.setAttribute("style",function(e){let t="";if(!e||I(e))return t;for(const n in e){const o=e[n],r=n.startsWith("--")?n:W(n);(I(o)||"number"==typeof o)&&(t+=`${r}:${o};`)}return t}(e))}setStyle(e){return this.$el&&e?(I(e)&&(e=c(e)),j(e)&&(this.$el.__wxsStyle=e,this.forceUpdate("style")),this):this}addClass(e){if(!this.$el||!e)return this;const t=this.$el.__wxsAddClass||(this.$el.__wxsAddClass=[]);return-1===t.indexOf(e)&&(t.push(e),this.forceUpdate("class")),this}removeClass(e){if(!this.$el||!e)return this;const{__wxsAddClass:t}=this.$el;if(t){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const n=this.$el.__wxsRemoveClass||(this.$el.__wxsRemoveClass=[]);return-1===n.indexOf(e)&&(n.push(e),this.forceUpdate("class")),this}hasClass(e){return this.$el&&this.$el.classList.contains(e)}getDataset(){return this.$el&&this.$el.dataset}callMethod(e,t={}){const n=this.$vm[e];M(n)?n(JSON.parse(JSON.stringify(t))):this.$vm.ownerId&&Wb.publishHandler("onWxsInvokeCallMethod",{nodeId:this.$el.__id,ownerId:this.$vm.ownerId,method:e,args:t})}requestAnimationFrame(e){return window.requestAnimationFrame(e)}getState(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}triggerEvent(e,t={}){return this.$vm.$emit(e,t),this}getComputedStyle(e){if(this.$el){const t=window.getComputedStyle(this.$el);return e&&e.length?e.reduce(((e,n)=>(e[n]=t[n],e)),{}):t}return{}}setTimeout(e,t){return window.setTimeout(e,t)}clearTimeout(e){return window.clearTimeout(e)}getBoundingClientRect(){return this.$el.getBoundingClientRect()}}function uu(e,t=!0){if(t&&e&&(e=Ae(e.$)),e&&e.$el)return e.$el.__wxsComponentDescriptor||(e.$el.__wxsComponentDescriptor=new cu(e)),e.$el.__wxsComponentDescriptor}function du(e,t){return uu(e,t)}function fu(e,t,n,o=!0){if(t){e.__instance||(e.__instance=!0,Object.defineProperty(e,"instance",{get:()=>du(n.proxy,!1)}));const r=function(e,t,n=!0){if(!t)return!1;if(n&&e.length<2)return!1;const o=Ae(t);if(!o)return!1;const r=o.$.type;return!(!r.$wxs&&!r.$renderjs)&&o}(t,n,o);if(r)return[e,du(r,!1)]}}function hu(e){if(e)return e.__vueParentComponent&&e.__vueParentComponent.proxy}function pu(e){for(;e&&0!==e.tagName.indexOf("UNI-");)e=e.parentElement;return e}function gu(e,t=!1){const{type:n,timeStamp:o,target:r,currentTarget:i}=e,a={type:n,timeStamp:o,target:ze(t?r:pu(r)),detail:{},currentTarget:ze(i)};return e._stopped&&(a._stopped=!0),e.type.startsWith("touch")&&(a.touches=e.touches,a.changedTouches=e.changedTouches),function(e,t){x(e,{preventDefault:()=>t.preventDefault(),stopPropagation:()=>t.stopPropagation()})}(a,e),a}function mu(e,t){return{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY-t,pageX:e.pageX,pageY:e.pageY-t}}function vu(e,t){const n=[];for(let o=0;o<e.length;o++){const{identifier:r,pageX:i,pageY:a,clientX:s,clientY:l,force:c}=e[o];n.push({identifier:r,pageX:i,pageY:a-t,clientX:s,clientY:l-t,force:c||0})}return n}const yu=Object.defineProperty({__proto__:null,$nne:function(e,t,n){const{currentTarget:o}=e;if(!(e instanceof Event&&o instanceof HTMLElement))return[e];const r=0!==o.tagName.indexOf("UNI-");if(r)return fu(e,t,n,!1)||[e];const i=gu(e,r);if("click"===e.type)!function(e,t){const{x:n,y:o}=t,r=Dc();e.detail={x:n,y:o-r},e.touches=e.changedTouches=[mu(t,r)]}(i,e);else if((e=>0===e.type.indexOf("mouse")||["contextmenu"].includes(e.type))(e))!function(e,t){const n=Dc();e.pageX=t.pageX,e.pageY=t.pageY-n,e.clientX=t.clientX,e.clientY=t.clientY-n,e.touches=e.changedTouches=[mu(t,n)]}(i,e);else if((e=>"undefined"!=typeof TouchEvent&&e instanceof TouchEvent||0===e.type.indexOf("touch"))(e)){const t=Dc();i.touches=vu(e.touches,t),i.changedTouches=vu(e.changedTouches,t)}return fu(i,t,n)||[i]},createNativeEvent:gu},Symbol.toStringTag,{value:"Module"});function bu(e){!function(e){const t=e.globalProperties;x(t,yu),t.$gcd=du}(e._context.config)}let _u=1;function wu(){return Zc()+"."+sc}const xu=x(ac("view"),{invokeOnCallback:(e,t)=>Hb.emit("api."+e,t),invokeViewMethod:(e,t,n,o)=>{const{subscribe:r,publishHandler:i}=Hb,a=o?_u++:0;o&&r(sc+"."+a,o,!0),i(wu(),{id:a,name:e,args:t},n)},invokeViewMethodKeepAlive:(e,t,n,o)=>{const{subscribe:r,unsubscribe:i,publishHandler:a}=Hb,s=_u++,l=sc+"."+s;return r(l,n),a(wu(),{id:s,name:e,args:t},o),()=>{i(l)}}});function Tu(e){nu(Jc(),he,e),Hb.invokeOnCallback("onWindowResize",e)}function ku(e){const t=Jc();nu(ov(),ne,e),nu(t,ne)}function Su(){nu(ov(),oe),nu(Jc(),oe)}const Cu=[ge,ve];function Eu(){Cu.forEach((e=>Hb.subscribe(e,function(e){return(t,n)=>{nu(parseInt(n),e,t)}}(e))))}function Ou(){!function(){const{on:e}=Hb;e(he,Tu),e(Ee,ku),e(Oe,Su)}(),Eu()}function Mu(){if(this.$route){const e=this.$route.meta;return e.eventChannel||(e.eventChannel=new Ke(this.$page.id)),e.eventChannel}}function Iu(e){e._context.config.globalProperties.getOpenerEventChannel=Mu}function Au(){return{path:"",query:{},scene:1001,referrerInfo:{appId:"",extraData:{}}}}function Pu(e){return/^-?\d+[ur]px$/i.test(e)?e.replace(/(^-?\d+)[ur]px$/i,((e,t)=>`${of(parseFloat(t))}px`)):/^-?[\d\.]+$/.test(e)?`${e}px`:e||""}function $u(e){const t=e.animation;if(!t||!t.actions||!t.actions.length)return;let n=0;const o=t.actions,r=t.actions.length;function i(){const t=o[n],a=t.option.transition,s=function(e){const t=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],o=["opacity","background-color"],r=["width","height","left","right","top","bottom"],i=e.animates,a=e.option,s=a.transition,l={},c=[];return i.forEach((e=>{let i=e.type,a=[...e.args];if(t.concat(n).includes(i))i.startsWith("rotate")||i.startsWith("skew")?a=a.map((e=>parseFloat(e)+"deg")):i.startsWith("translate")&&(a=a.map(Pu)),n.indexOf(i)>=0&&(a.length=1),c.push(`${i}(${a.join(",")})`);else if(o.concat(r).includes(a[0])){i=a[0];const e=a[1];l[i]=r.includes(i)?Pu(e):e}})),l.transform=l.webkitTransform=c.join(" "),l.transition=l.webkitTransition=Object.keys(l).map((e=>`${function(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`)).replace("webkit","-webkit")}(e)} ${s.duration}ms ${s.timingFunction} ${s.delay}ms`)).join(","),l.transformOrigin=l.webkitTransformOrigin=a.transformOrigin,l}(t);Object.keys(s).forEach((t=>{e.$el.style[t]=s[t]})),n+=1,n<r&&setTimeout(i,a.duration+a.delay)}setTimeout((()=>{i()}),0)}const Lu={props:["animation"],watch:{animation:{deep:!0,handler(){$u(this)}}},mounted(){$u(this)}},Ru=e=>{e.__reserved=!0;const{props:t,mixins:n}=e;return t&&t.animation||(n||(e.mixins=[])).push(Lu),ju(e)},ju=e=>(e.__reserved=!0,e.compatConfig={MODE:3},Vo(e)),Bu={hoverClass:{type:String,default:"none"},hoverStopPropagation:{type:Boolean,default:!1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:400}};function Du(e){const t=An(!1);let n,o,r=!1;function i(){requestAnimationFrame((()=>{clearTimeout(o),o=setTimeout((()=>{t.value=!1}),parseInt(e.hoverStayTime))}))}function a(o){o._hoverPropagationStopped||e.hoverClass&&"none"!==e.hoverClass&&!e.disabled&&(e.hoverStopPropagation&&(o._hoverPropagationStopped=!0),r=!0,n=setTimeout((()=>{t.value=!0,r||i()}),parseInt(e.hoverStartTime)))}function s(){r=!1,t.value&&i()}function l(){s(),window.removeEventListener("mouseup",l)}return{hovering:t,binding:{onTouchstartPassive:function(e){e.touches.length>1||a(e)},onMousedown:function(e){r||(a(e),window.addEventListener("mouseup",l))},onTouchend:function(){s()},onMouseup:function(){r&&l()},onTouchcancel:function(){r=!1,t.value=!1,clearTimeout(n)}}}}function Nu(e,t){return I(t)&&(t=[t]),t.reduce(((t,n)=>(e[n]&&(t[n]=!0),t)),Object.create(null))}function Fu(e){return e.__wwe=!0,e}function Vu(e,t){return(n,o,r)=>{e.value&&t(n,function(e,t,n,o){const r=ze(n);return{type:o.type||e,timeStamp:t.timeStamp||0,target:r,currentTarget:r,detail:o}}(n,o,e.value,r||{}))}}const qu=qc("uf"),Wu=Ru({name:"Form",emits:["submit","reset"],setup(e,{slots:t,emit:n}){const o=An(null);return function(e){const t=[];xo(qu,{addField(e){t.push(e)},removeField(e){t.splice(t.indexOf(e),1)},submit(n){e("submit",n,{value:t.reduce(((e,t)=>{if(t.submit){const[n,o]=t.submit();n&&(e[n]=o)}return e}),Object.create(null))})},reset(n){t.forEach((e=>e.reset&&e.reset())),e("reset",n)}})}(Vu(o,n)),()=>Ei("uni-form",{ref:o},[Ei("span",null,[t.default&&t.default()])],512)}});const Hu={for:{type:String,default:""}},zu=qc("ul");const Uu=Ru({name:"Label",props:Hu,setup(e,{slots:t}){const n=Xc(),o=function(){const e=[];return xo(zu,{addHandler(t){e.push(t)},removeHandler(t){e.splice(e.indexOf(t),1)}}),e}(),r=Gi((()=>e.for||t.default&&t.default.length)),i=Fu((t=>{const r=t.target;let i=/^uni-(checkbox|radio|switch)-/.test(r.className);i||(i=/^uni-(checkbox|radio|switch|button)$|^(svg|path)$/i.test(r.tagName)),i||(e.for?Wb.emit("uni-label-click-"+n+"-"+e.for,t,!0):o.length&&o[0](t,!0))}));return()=>Ei("uni-label",{class:{"uni-label-pointer":r},onClick:i},[t.default&&t.default()],10,["onClick"])}});function Yu(e,t){Xu(e.id,t),Co((()=>e.id),((e,n)=>{Gu(n,t,!0),Xu(e,t,!0)})),fr((()=>{Gu(e.id,t)}))}function Xu(e,t,n){const o=Xc();n&&!e||j(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&Wb.on(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?Wb.on(r,t[r]):e&&Wb.on(`uni-${r}-${o}-${e}`,t[r])}))}function Gu(e,t,n){const o=Xc();n&&!e||j(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&Wb.off(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?Wb.off(r,t[r]):e&&Wb.off(`uni-${r}-${o}-${e}`,t[r])}))}const Ju=Ru({name:"Button",props:{id:{type:String,default:""},hoverClass:{type:String,default:"button-hover"},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:70},hoverStopPropagation:{type:Boolean,default:!1},disabled:{type:[Boolean,String],default:!1},formType:{type:String,default:""},openType:{type:String,default:""},loading:{type:[Boolean,String],default:!1},plain:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=An(null),o=To(qu,!1),{hovering:r,binding:i}=Du(e);Jl();const a=Fu(((t,r)=>{if(e.disabled)return t.stopImmediatePropagation();r&&n.value.click();const i=e.formType;if(i){if(!o)return;"submit"===i?o.submit(t):"reset"===i&&o.reset(t)}else;})),s=To(zu,!1);return s&&(s.addHandler(a),dr((()=>{s.removeHandler(a)}))),Yu(e,{"label-click":a}),()=>{const o=e.hoverClass,s=Nu(e,"disabled"),l=Nu(e,"loading"),c=Nu(e,"plain"),u=o&&"none"!==o;return Ei("uni-button",Li({ref:n,onClick:a,class:u&&r.value?o:""},u&&i,s,l,c),[t.default&&t.default()],16,["onClick"])}}});function Ku(e){return e.$el}function Zu(e){const{base:t}=__uniConfig.router;return 0===Le(e).indexOf(t)?Le(e):t+e}function Qu(e){const{base:t,assets:n}=__uniConfig.router;if("./"===t&&(0===e.indexOf("./static/")||n&&0===e.indexOf("./"+n+"/"))&&(e=e.slice(1)),0===e.indexOf("/")){if(0!==e.indexOf("//"))return Zu(e.slice(1));e="https:"+e}if(ee.test(e)||te.test(e)||0===e.indexOf("blob:"))return e;const o=$m();return o.length?Zu(su(o[o.length-1].$page.route,e).slice(1)):e}const ed=navigator.userAgent,td=/android/i.test(ed),nd=/iphone|ipad|ipod/i.test(ed),od=ed.match(/Windows NT ([\d|\d.\d]*)/i),rd=/Macintosh|Mac/i.test(ed),id=/Linux|X11/i.test(ed),ad=rd&&navigator.maxTouchPoints>0;function sd(){return/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation}function ld(e){return e&&90===Math.abs(window.orientation)}function cd(e,t){return e?Math[t?"max":"min"](screen.width,screen.height):screen.width}function ud(e){return Math.min(window.innerWidth,document.documentElement.clientWidth,e)||e}function dd(e,t,n,o){Hb.invokeViewMethod("video."+e,{videoId:e,type:n,data:o},t)}function fd(e,t){const n={},{top:o,topWindowHeight:r}=Nc();if(t.id&&(n.id=e.id),t.dataset&&(n.dataset=qe(e)),t.rect||t.size){const i=e.getBoundingClientRect();t.rect&&(n.left=i.left,n.right=i.right,n.top=i.top-o-r,n.bottom=i.bottom-o-r),t.size&&(n.width=i.width,n.height=i.height)}if(C(t.properties)&&t.properties.forEach((e=>{e=e.replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()}))})),t.scrollOffset)if("UNI-SCROLL-VIEW"===e.tagName){const t=e.children[0].children[0];n.scrollLeft=t.scrollLeft,n.scrollTop=t.scrollTop,n.scrollHeight=t.scrollHeight,n.scrollWidth=t.scrollWidth}else n.scrollLeft=0,n.scrollTop=0,n.scrollHeight=0,n.scrollWidth=0;if(C(t.computedStyle)){const o=getComputedStyle(e);t.computedStyle.forEach((e=>{n[e]=o[e]}))}return t.context&&(n.contextInfo=function(e){return e.__uniContextInfo}(e)),n}function hd(e,t){return(e.matches||e.matchesSelector||e.mozMatchesSelector||e.msMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector||function(e){const t=this.parentElement.querySelectorAll(e);let n=t.length;for(;--n>=0&&t.item(n)!==this;);return n>-1}).call(e,t)}function pd(e,t,n){const o=[];t.forEach((({component:t,selector:n,single:r,fields:i})=>{null===t?o.push(function(e){const t={};if(e.id&&(t.id=""),e.dataset&&(t.dataset={}),e.rect&&(t.left=0,t.right=0,t.top=0,t.bottom=0),e.size&&(t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight),e.scrollOffset){const e=document.documentElement,n=document.body;t.scrollLeft=e.scrollLeft||n.scrollLeft||0,t.scrollTop=e.scrollTop||n.scrollTop||0,t.scrollHeight=e.scrollHeight||n.scrollHeight||0,t.scrollWidth=e.scrollWidth||n.scrollWidth||0}return t}(i)):o.push(function(e,t,n,o,r){const i=function(e,t){return e?e.$el:t.$el}(t,e),a=i.parentElement;if(!a)return o?null:[];const{nodeType:s}=i,l=3===s||8===s;if(o){const e=l?a.querySelector(n):hd(i,n)?i:i.querySelector(n);return e?fd(e,r):null}{let e=[];const t=(l?a:i).querySelectorAll(n);return t&&t.length&&[].forEach.call(t,(t=>{e.push(fd(t,r))})),!l&&hd(i,n)&&e.unshift(fd(i,r)),e}}(e,t,n,r,i))})),n(o)}const gd=["original","compressed"],md=["album","camera"],vd=["GET","OPTIONS","HEAD","POST","PUT","DELETE","TRACE","CONNECT","PATCH"];function yd(e,t){return e&&-1!==t.indexOf(e)?e:t[0]}function bd(e,t){return!C(e)||0===e.length||e.find((e=>-1===t.indexOf(e)))?t:e}function _d(e){return function(){try{return e.apply(e,arguments)}catch(zE){console.error(zE)}}}let wd=1;const xd={};function Td(e,t,n,o=!1){return xd[e]={name:t,keepAlive:o,callback:n},e}function kd(e,t,n){if("number"==typeof e){const o=xd[e];if(o)return o.keepAlive||delete xd[e],o.callback(t,n)}return t}const Sd="success",Cd="fail",Ed="complete";function Od(e,t={},{beforeAll:n,beforeSuccess:o}={}){j(t)||(t={});const{success:r,fail:i,complete:a}=function(e){const t={};for(const n in e){const o=e[n];M(o)&&(t[n]=_d(o),delete e[n])}return t}(t),s=M(r),l=M(i),c=M(a),u=wd++;return Td(u,e,(u=>{(u=u||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(u.errMsg,e),M(n)&&n(u),u.errMsg===e+":ok"?(M(o)&&o(u,t),s&&r(u)):l&&i(u),c&&a(u)})),u}const Md="success",Id="fail",Ad="complete",Pd={},$d={};function Ld(e,t){return function(n){return e(n,t)||n}}function Rd(e,t,n){let o=!1;for(let r=0;r<e.length;r++){const i=e[r];if(o)o=Promise.resolve(Ld(i,n));else{const e=i(t,n);if($(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function jd(e,t={}){return[Md,Id,Ad].forEach((n=>{const o=e[n];if(!C(o))return;const r=t[n];t[n]=function(e){Rd(o,e,t).then((e=>M(r)&&r(e)||e))}})),t}function Bd(e,t){const n=[];C(Pd.returnValue)&&n.push(...Pd.returnValue);const o=$d[e];return o&&C(o.returnValue)&&n.push(...o.returnValue),n.forEach((e=>{t=e(t)||t})),t}function Dd(e){const t=Object.create(null);Object.keys(Pd).forEach((e=>{"returnValue"!==e&&(t[e]=Pd[e].slice())}));const n=$d[e];return n&&Object.keys(n).forEach((e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function Nd(e,t,n,o){const r=Dd(e);if(r&&Object.keys(r).length){if(C(r.invoke)){return Rd(r.invoke,n).then((n=>t(jd(Dd(e),n),...o)))}return t(jd(r,n),...o)}return t(n,...o)}function Fd(e,t){return(n={},...o)=>function(e){return!(!j(e)||![Sd,Cd,Ed].find((t=>M(e[t]))))}(n)?Bd(e,Nd(e,t,n,o)):Bd(e,new Promise(((r,i)=>{Nd(e,t,x(n,{success:r,fail:i}),o)})))}function Vd(e,t,n,o){return kd(e,x({errMsg:t+":fail"+(n?" "+n:"")},o))}function qd(e,t,n,o){if(o&&o.beforeInvoke){const e=o.beforeInvoke(t);if(I(e))return e}const r=function(e,t){const n=e[0];if(!t||!j(t.formatArgs)&&j(n))return;const o=t.formatArgs,r=Object.keys(o);for(let i=0;i<r.length;i++){const t=r[i],a=o[t];if(M(a)){const o=a(e[0][t],n);if(I(o))return o}else S(n,t)||(n[t]=a)}}(t,o);if(r)return r}function Wd(e,t,n){return o=>{!function(e){if(!M(e))throw new Error('Invalid args: type check failed for args "callback". Expected Function')}(o);const r=qd(0,[o],0,n);if(r)throw new Error(r);const i=!function(e){for(const t in xd)if(xd[t].name===e)return!0;return!1}(e);!function(e,t){Td(wd++,e,t,!0)}(e,o),i&&(!function(e){Hb.on("api."+e,(t=>{for(const n in xd){const o=xd[n];o.name===e&&o.callback(t)}}))}(e),t())}}function Hd(e,t,n,o){return n=>{const r=Od(e,n,o),i=qd(0,[n],0,o);return i?Vd(r,e,i):t(n,{resolve:t=>function(e,t,n){return kd(e,x(n||{},{errMsg:t+":ok"}))}(r,e,t),reject:(t,n)=>Vd(r,e,function(e){return!e||I(e)?e:e.stack?(console.error(e.message+Z+e.stack),e.message):e}(t),n)})}}function zd(e,t,n){return Wd(e,t,n)}function Ud(e,t,n,o){return Fd(e,Hd(e,t,0,o))}function Yd(e,t,n,o){return function(e,t,n,o){return(...e)=>{const n=qd(0,e,0,o);if(n)throw new Error(n);return t.apply(null,e)}}(0,t,0,o)}function Xd(e,t,n,o){return Fd(e,function(e,t,n,o){return Hd(e,t,0,o)}(e,t,0,o))}let Gd=!1,Jd=0,Kd=0,Zd=960,Qd=375,ef=750;function tf(){const{platform:e,pixelRatio:t,windowWidth:n}=function(){const e=sd(),t=ud(cd(e,ld(e)));return{platform:nd?"ios":"other",pixelRatio:window.devicePixelRatio,windowWidth:t}}();Jd=n,Kd=t,Gd="ios"===e}function nf(e,t){const n=Number(e);return isNaN(n)?t:n}const of=Yd(0,((e,t)=>{if(0===Jd&&(tf(),function(){const e=__uniConfig.globalStyle||{};Zd=nf(e.rpxCalcMaxDeviceWidth,960),Qd=nf(e.rpxCalcBaseDeviceWidth,375),ef=nf(e.rpxCalcBaseDeviceWidth,750)}()),0===(e=Number(e)))return 0;let n=t||Jd;n=e===ef||n<=Zd?n:Qd;let o=e/750*n;return o<0&&(o=-o),o=Math.floor(o+1e-4),0===o&&(o=1!==Kd&&Gd?.5:1),e<0?-o:o}));function rf(e,t){Object.keys(t).forEach((n=>{M(t[n])&&(e[n]=function(e,t){const n=t?e?e.concat(t):C(t)?t:[t]:e;return n?function(e){const t=[];for(let n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(n):n}(e[n],t[n]))}))}const af=Yd(0,((e,t)=>{I(e)&&j(t)?rf($d[e]||($d[e]={}),t):j(e)&&rf(Pd,e)})),sf=new rt,lf=Yd(0,((e,t)=>(sf.on(e,t),()=>sf.off(e,t)))),cf=Yd(0,((e,t)=>{e?(C(e)||(e=[e]),e.forEach((e=>sf.off(e,t)))):sf.e={}})),uf=Yd(0,((e,...t)=>{sf.emit(e,...t)})),df=[.5,.8,1,1.25,1.5,2];const ff=(e,t,n,o)=>{!function(e,t,n,o,r){Hb.invokeViewMethod("map."+e,{type:n,data:o},t,r)}(e,t,n,o,(e=>{o&&((e,t)=>{const n=t.errMsg||"";new RegExp("\\:\\s*fail").test(n)?e.fail&&e.fail(t):e.success&&e.success(t),e.complete&&e.complete(t)})(o,e)}))};function hf(e,t){return function(n,o){n?o[e]=Math.round(n):void 0!==t&&(o[e]=t)}}const pf=hf("width"),gf=hf("height"),mf={formatArgs:{x:hf("x"),y:hf("y"),width:pf,height:gf}},vf={PNG:"png",JPG:"jpg",JPEG:"jpg"},yf={formatArgs:{x:hf("x",0),y:hf("y",0),width:pf,height:gf,destWidth:hf("destWidth"),destHeight:hf("destHeight"),fileType(e,t){e=(e||"").toUpperCase();let n=vf[e];n||(n=vf.PNG),t.fileType=n},quality(e,t){t.quality=e&&e>0&&e<1?e:1}}};function bf(e,t,n,o,r){Hb.invokeViewMethod(`canvas.${e}`,{type:n,data:o},t,(e=>{r&&r(e)}))}var _f=["scale","rotate","translate","setTransform","transform"],wf=["drawImage","fillText","fill","stroke","fillRect","strokeRect","clearRect","strokeText"],xf=["setFillStyle","setTextAlign","setStrokeStyle","setGlobalAlpha","setShadow","setFontSize","setLineCap","setLineJoin","setLineWidth","setMiterLimit","setTextBaseline","setLineDash"];const Tf={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgrey:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32",transparent:"#00000000"};function kf(e){var t=null;if(null!=(t=/^#([0-9|A-F|a-f]{6})$/.exec(e=e||"#000000"))){return[parseInt(t[1].slice(0,2),16),parseInt(t[1].slice(2,4),16),parseInt(t[1].slice(4),16),255]}if(null!=(t=/^#([0-9|A-F|a-f]{3})$/.exec(e))){let e=t[1].slice(0,1),n=t[1].slice(1,2),o=t[1].slice(2,3);return e=parseInt(e+e,16),n=parseInt(n+n,16),o=parseInt(o+o,16),[e,n,o,255]}if(null!=(t=/^rgb\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e){return Math.min(255,parseInt(e.trim()))})).concat(255);if(null!=(t=/^rgba\((.+)\)$/.exec(e)))return t[1].split(",").map((function(e,t){return 3===t?Math.floor(255*parseFloat(e.trim())):Math.min(255,parseInt(e.trim()))}));var n=e.toLowerCase();if(S(Tf,n)){t=/^#([0-9|A-F|a-f]{6,8})$/.exec(Tf[n]);const e=parseInt(t[1].slice(0,2),16),o=parseInt(t[1].slice(2,4),16),r=parseInt(t[1].slice(4,6),16);let i=parseInt(t[1].slice(6,8),16);return i=i>=0?i:255,[e,o,r,i]}return console.error("unsupported color:"+e),[0,0,0,255]}class Sf{constructor(e,t){this.type=e,this.data=t,this.colorStop=[]}addColorStop(e,t){this.colorStop.push([e,kf(t)])}}class Cf{constructor(e,t){this.type="pattern",this.data=e,this.colorStop=t}}class Ef{constructor(e){this.width=e}}class Of{constructor(e,t){this.id=e,this.pageId=t,this.actions=[],this.path=[],this.subpath=[],this.drawingState=[],this.state={lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}draw(e=!1,t){var n=[...this.actions];this.actions=[],this.path=[],bf(this.id,this.pageId,"actionsChanged",{actions:n,reserve:e},t)}createLinearGradient(e,t,n,o){return new Sf("linear",[e,t,n,o])}createCircularGradient(e,t,n){return new Sf("radial",[e,t,n])}createPattern(e,t){if(void 0===t)console.error("Failed to execute 'createPattern' on 'CanvasContext': 2 arguments required, but only 1 present.");else{if(!(["repeat","repeat-x","repeat-y","no-repeat"].indexOf(t)<0))return new Cf(e,t);console.error("Failed to execute 'createPattern' on 'CanvasContext': The provided type ('"+t+"') is not one of 'repeat', 'no-repeat', 'repeat-x', or 'repeat-y'.")}}measureText(e){let t=0;return t=function(e,t){const n=document.createElement("canvas").getContext("2d");return n.font=t,n.measureText(e).width||0}(e,this.state.font),new Ef(t)}save(){this.actions.push({method:"save",data:[]}),this.drawingState.push(this.state)}restore(){this.actions.push({method:"restore",data:[]}),this.state=this.drawingState.pop()||{lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}beginPath(){this.path=[],this.subpath=[],this.path.push({method:"beginPath",data:[]})}moveTo(e,t){this.path.push({method:"moveTo",data:[e,t]}),this.subpath=[[e,t]]}lineTo(e,t){0===this.path.length&&0===this.subpath.length?this.path.push({method:"moveTo",data:[e,t]}):this.path.push({method:"lineTo",data:[e,t]}),this.subpath.push([e,t])}quadraticCurveTo(e,t,n,o){this.path.push({method:"quadraticCurveTo",data:[e,t,n,o]}),this.subpath.push([n,o])}bezierCurveTo(e,t,n,o,r,i){this.path.push({method:"bezierCurveTo",data:[e,t,n,o,r,i]}),this.subpath.push([r,i])}arc(e,t,n,o,r,i=!1){this.path.push({method:"arc",data:[e,t,n,o,r,i]}),this.subpath.push([e,t])}rect(e,t,n,o){this.path.push({method:"rect",data:[e,t,n,o]}),this.subpath=[[e,t]]}arcTo(e,t,n,o,r){this.path.push({method:"arcTo",data:[e,t,n,o,r]}),this.subpath.push([n,o])}clip(){this.actions.push({method:"clip",data:[...this.path]})}closePath(){this.path.push({method:"closePath",data:[]}),this.subpath.length&&(this.subpath=[this.subpath.shift()])}clearActions(){this.actions=[],this.path=[],this.subpath=[]}getActions(){var e=[...this.actions];return this.clearActions(),e}set lineDashOffset(e){this.actions.push({method:"setLineDashOffset",data:[e]})}set globalCompositeOperation(e){this.actions.push({method:"setGlobalCompositeOperation",data:[e]})}set shadowBlur(e){this.actions.push({method:"setShadowBlur",data:[e]})}set shadowColor(e){this.actions.push({method:"setShadowColor",data:[e]})}set shadowOffsetX(e){this.actions.push({method:"setShadowOffsetX",data:[e]})}set shadowOffsetY(e){this.actions.push({method:"setShadowOffsetY",data:[e]})}set font(e){var t=this;this.state.font=e;var n=e.match(/^(([\w\-]+\s)*)(\d+r?px)(\/(\d+\.?\d*(r?px)?))?\s+(.*)/);if(n){var o=n[1].trim().split(/\s/),r=parseFloat(n[3]),i=n[7],a=[];o.forEach((function(e,n){["italic","oblique","normal"].indexOf(e)>-1?(a.push({method:"setFontStyle",data:[e]}),t.state.fontStyle=e):["bold","normal"].indexOf(e)>-1?(a.push({method:"setFontWeight",data:[e]}),t.state.fontWeight=e):0===n?(a.push({method:"setFontStyle",data:["normal"]}),t.state.fontStyle="normal"):1===n&&s()})),1===o.length&&s(),o=a.map((function(e){return e.data[0]})).join(" "),this.state.fontSize=r,this.state.fontFamily=i,this.actions.push({method:"setFont",data:[`${o} ${r}px ${i}`]})}else console.warn("Failed to set 'font' on 'CanvasContext': invalid format.");function s(){a.push({method:"setFontWeight",data:["normal"]}),t.state.fontWeight="normal"}}get font(){return this.state.font}set fillStyle(e){this.setFillStyle(e)}set strokeStyle(e){this.setStrokeStyle(e)}set globalAlpha(e){e=Math.floor(255*parseFloat(e)),this.actions.push({method:"setGlobalAlpha",data:[e]})}set textAlign(e){this.actions.push({method:"setTextAlign",data:[e]})}set lineCap(e){this.actions.push({method:"setLineCap",data:[e]})}set lineJoin(e){this.actions.push({method:"setLineJoin",data:[e]})}set lineWidth(e){this.actions.push({method:"setLineWidth",data:[e]})}set miterLimit(e){this.actions.push({method:"setMiterLimit",data:[e]})}set textBaseline(e){this.actions.push({method:"setTextBaseline",data:[e]})}}const Mf=Be((()=>{[..._f,...wf].forEach((function(e){Of.prototype[e]=function(e){switch(e){case"fill":case"stroke":return function(){this.actions.push({method:e+"Path",data:[...this.path]})};case"fillRect":return function(e,t,n,o){this.actions.push({method:"fillPath",data:[{method:"rect",data:[e,t,n,o]}]})};case"strokeRect":return function(e,t,n,o){this.actions.push({method:"strokePath",data:[{method:"rect",data:[e,t,n,o]}]})};case"fillText":case"strokeText":return function(t,n,o,r){var i=[t.toString(),n,o];"number"==typeof r&&i.push(r),this.actions.push({method:e,data:i})};case"drawImage":return function(t,n,o,r,i,a,s,l,c){var u;function d(e){return"number"==typeof e}void 0===c&&(a=n,s=o,l=r,c=i,n=void 0,o=void 0,r=void 0,i=void 0),u=d(n)&&d(o)&&d(r)&&d(i)?[t,a,s,l,c,n,o,r,i]:d(l)&&d(c)?[t,a,s,l,c]:[t,a,s],this.actions.push({method:e,data:u})};default:return function(...t){this.actions.push({method:e,data:t})}}}(e)})),xf.forEach((function(e){Of.prototype[e]=function(e){switch(e){case"setFillStyle":case"setStrokeStyle":return function(t){"object"!=typeof t?this.actions.push({method:e,data:["normal",kf(t)]}):this.actions.push({method:e,data:[t.type,t.data,t.colorStop]})};case"setGlobalAlpha":return function(t){t=Math.floor(255*parseFloat(t)),this.actions.push({method:e,data:[t]})};case"setShadow":return function(t,n,o,r){r=kf(r),this.actions.push({method:e,data:[t,n,o,r]}),this.state.shadowBlur=o,this.state.shadowColor=r,this.state.shadowOffsetX=t,this.state.shadowOffsetY=n};case"setLineDash":return function(t,n){t=t||[0,0],n=n||0,this.actions.push({method:e,data:[t,n]}),this.state.lineDash=t};case"setFontSize":return function(t){this.state.font=this.state.font.replace(/\d+\.?\d*px/,t+"px"),this.state.fontSize=t,this.actions.push({method:e,data:[t]})};default:return function(...t){this.actions.push({method:e,data:t})}}}(e)}))})),If=Yd(0,((e,t)=>{if(Mf(),t)return new Of(e,Gc(t));const n=Gc(Qc());if(n)return new Of(e,n);Hb.emit(ie,"createCanvasContext:fail")})),Af=Xd("canvasGetImageData",(({canvasId:e,x:t,y:n,width:o,height:r},{resolve:i,reject:a})=>{const s=Gc(Qc());s?bf(e,s,"getImageData",{x:t,y:n,width:o,height:r},(function(e){if(e.errMsg&&-1!==e.errMsg.indexOf("fail"))return void a("",e);let t=e.data;t&&t.length&&(e.data=new Uint8ClampedArray(t)),delete e.compressed,i(e)})):a()}),0,mf),Pf=Xd("canvasToTempFilePath",(({x:e=0,y:t=0,width:n,height:o,destWidth:r,destHeight:i,canvasId:a,fileType:s,quality:l},{resolve:c,reject:u})=>{var d=Gc(Qc());if(!d)return void u();bf(a,d,"toTempFilePath",{x:e,y:t,width:n,height:o,destWidth:r,destHeight:i,fileType:s,quality:l,dirname:`${zh}/canvas`},(e=>{e.errMsg&&-1!==e.errMsg.indexOf("fail")?u("",e):c(e)}))}),0,yf),$f={thresholds:[0],initialRatio:0,observeAll:!1},Lf=["top","right","bottom","left"];let Rf=1;function jf(e={}){return Lf.map((t=>`${Number(e[t])||0}px`)).join(" ")}class Bf{constructor(e,t){this._pageId=Gc(e),this._component=e,this._options=x({},$f,t)}relativeTo(e,t){return this._options.relativeToSelector=e,this._options.rootMargin=jf(t),this}relativeToViewport(e){return this._options.relativeToSelector=void 0,this._options.rootMargin=jf(e),this}observe(e,t){M(t)&&(this._options.selector=e,this._reqId=Rf++,function({reqId:e,component:t,options:n,callback:o}){const r=Ku(t);(r.__io||(r.__io={}))[e]=function(e,t,n){qh();const o=t.relativeToSelector?e.querySelector(t.relativeToSelector):null,r=new IntersectionObserver((e=>{e.forEach((e=>{n({intersectionRatio:Hh(e),intersectionRect:Wh(e.intersectionRect),boundingClientRect:Wh(e.boundingClientRect),relativeRect:Wh(e.rootBounds),time:Date.now(),dataset:qe(e.target),id:e.target.id})}))}),{root:o,rootMargin:t.rootMargin,threshold:t.thresholds});if(t.observeAll){r.USE_MUTATION_OBSERVER=!0;const n=e.querySelectorAll(t.selector);for(let e=0;e<n.length;e++)r.observe(n[e])}else{r.USE_MUTATION_OBSERVER=!1;const n=e.querySelector(t.selector);n?r.observe(n):console.warn(`Node ${t.selector} is not found. Intersection observer will not trigger.`)}return r}(r,n,o)}({reqId:this._reqId,component:this._component,options:this._options,callback:t},this._pageId))}disconnect(){this._reqId&&function({reqId:e,component:t}){const n=Ku(t),o=n.__io&&n.__io[e];o&&(o.disconnect(),delete n.__io[e])}({reqId:this._reqId,component:this._component},this._pageId)}}const Df=Yd(0,((e,t)=>((e=Ie(e))&&!Gc(e)&&(t=e,e=null),new Bf(e||Qc(),t))));let Nf=0,Ff={};function Vf(e,t,n,o){const r={options:o},i=o&&("success"in o||"fail"in o||"complete"in o);if(i){const e=String(Nf++);r.callbackId=e,Ff[e]=o}Hb.invokeViewMethod(`editor.${e}`,{type:n,data:r},t,(({callbackId:e,data:t})=>{i&&(!function(e,t){e=e||{},I(t)&&(t={errMsg:t}),/:ok$/.test(t.errMsg)?M(e.success)&&e.success(t):M(e.fail)&&e.fail(t),M(e.complete)&&e.complete(t)}(Ff[e],t),delete Ff[e])}))}const qf={canvas:Of,map:class{constructor(e,t){this.id=e,this.pageId=t}getCenterLocation(e){ff(this.id,this.pageId,"getCenterLocation",e)}moveToLocation(e){ff(this.id,this.pageId,"moveToLocation",e)}getScale(e){ff(this.id,this.pageId,"getScale",e)}getRegion(e){ff(this.id,this.pageId,"getRegion",e)}includePoints(e){ff(this.id,this.pageId,"includePoints",e)}translateMarker(e){ff(this.id,this.pageId,"translateMarker",e)}$getAppMap(){}addCustomLayer(e){ff(this.id,this.pageId,"addCustomLayer",e)}removeCustomLayer(e){ff(this.id,this.pageId,"removeCustomLayer",e)}addGroundOverlay(e){ff(this.id,this.pageId,"addGroundOverlay",e)}removeGroundOverlay(e){ff(this.id,this.pageId,"removeGroundOverlay",e)}updateGroundOverlay(e){ff(this.id,this.pageId,"updateGroundOverlay",e)}initMarkerCluster(e){ff(this.id,this.pageId,"initMarkerCluster",e)}addMarkers(e){ff(this.id,this.pageId,"addMarkers",e)}removeMarkers(e){ff(this.id,this.pageId,"removeMarkers",e)}moveAlong(e){ff(this.id,this.pageId,"moveAlong",e)}setLocMarkerIcon(e){ff(this.id,this.pageId,"setLocMarkerIcon",e)}openMapApp(e){ff(this.id,this.pageId,"openMapApp",e)}on(e){ff(this.id,this.pageId,"on",e)}},video:class{constructor(e,t){this.id=e,this.pageId=t}play(){dd(this.id,this.pageId,"play")}pause(){dd(this.id,this.pageId,"pause")}stop(){dd(this.id,this.pageId,"stop")}seek(e){dd(this.id,this.pageId,"seek",{position:e})}sendDanmu(e){dd(this.id,this.pageId,"sendDanmu",e)}playbackRate(e){~df.indexOf(e)||(e=1),dd(this.id,this.pageId,"playbackRate",{rate:e})}requestFullScreen(e={}){dd(this.id,this.pageId,"requestFullScreen",e)}exitFullScreen(){dd(this.id,this.pageId,"exitFullScreen")}showStatusBar(){dd(this.id,this.pageId,"showStatusBar")}hideStatusBar(){dd(this.id,this.pageId,"hideStatusBar")}},editor:class{constructor(e,t){this.id=e,this.pageId=t}format(e,t){this._exec("format",{name:e,value:t})}insertDivider(){this._exec("insertDivider")}insertImage(e){this._exec("insertImage",e)}insertText(e){this._exec("insertText",e)}setContents(e){this._exec("setContents",e)}getContents(e){this._exec("getContents",e)}clear(e){this._exec("clear",e)}removeFormat(e){this._exec("removeFormat",e)}undo(e){this._exec("undo",e)}redo(e){this._exec("redo",e)}blur(e){this._exec("blur",e)}getSelectionText(e){this._exec("getSelectionText",e)}scrollIntoView(e){this._exec("scrollIntoView",e)}_exec(e,t){Vf(this.id,this.pageId,e,t)}}};function Wf(e){if(e&&e.contextInfo){const{id:t,type:n,page:o}=e.contextInfo,r=qf[n];e.context=new r(t,o),delete e.contextInfo}}class Hf{constructor(e,t,n,o){this._selectorQuery=e,this._component=t,this._selector=n,this._single=o}boundingClientRect(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,rect:!0,size:!0},e),this._selectorQuery}fields(e,t){return this._selectorQuery._push(this._selector,this._component,this._single,e,t),this._selectorQuery}scrollOffset(e){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,scrollOffset:!0},e),this._selectorQuery}context(e){return this._selectorQuery._push(this._selector,this._component,this._single,{context:!0},e),this._selectorQuery}node(e){return this._selectorQuery}}class zf{constructor(e){this._component=void 0,this._page=e,this._queue=[],this._queueCb=[]}exec(e){return pd(this._page,this._queue,(t=>{const n=this._queueCb;t.forEach(((e,t)=>{C(e)?e.forEach(Wf):Wf(e);const o=n[t];M(o)&&o.call(this,e)})),M(e)&&e.call(this,t)})),this._nodesRef}in(e){return this._component=Ie(e),this}select(e){return this._nodesRef=new Hf(this,this._component,e,!0)}selectAll(e){return this._nodesRef=new Hf(this,this._component,e,!1)}selectViewport(){return this._nodesRef=new Hf(this,null,"",!0)}_push(e,t,n,o,r){this._queue.push({component:t,selector:e,single:n,fields:o}),this._queueCb.push(r)}}const Uf=Yd(0,(e=>((e=Ie(e))&&!Gc(e)&&(e=null),new zf(e||Qc())))),Yf={formatArgs:{}},Xf={duration:400,timingFunction:"linear",delay:0,transformOrigin:"50% 50% 0"};class Gf{constructor(e){this.actions=[],this.currentTransform={},this.currentStepAnimates=[],this.option=x({},Xf,e)}_getOption(e){const t={transition:x({},this.option,e),transformOrigin:""};return t.transformOrigin=t.transition.transformOrigin,delete t.transition.transformOrigin,t}_pushAnimates(e,t){this.currentStepAnimates.push({type:e,args:t})}_converType(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))}_getValue(e){return"number"==typeof e?`${e}px`:e}export(){const e=this.actions;return this.actions=[],{actions:e}}step(e){return this.currentStepAnimates.forEach((e=>{"style"!==e.type?this.currentTransform[e.type]=e:this.currentTransform[`${e.type}.${e.args[0]}`]=e})),this.actions.push({animates:Object.values(this.currentTransform),option:this._getOption(e)}),this.currentStepAnimates=[],this}}const Jf=Be((()=>{const e=["opacity","backgroundColor"],t=["width","height","left","right","top","bottom"];["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"].concat(e,t).forEach((n=>{Gf.prototype[n]=function(...o){return e.concat(t).includes(n)?this._pushAnimates("style",[this._converType(n),t.includes(n)?this._getValue(o[0]):o[0]]):this._pushAnimates(n,o),this}}))})),Kf=Yd(0,(e=>(Jf(),new Gf(e))),0,Yf),Zf=zd("onWindowResize",(()=>{})),Qf=Yd(0,(()=>{const e=ov();return e&&e.$vm?e.$vm.$locale:Jl().getLocale()})),eh=Xd("setPageMeta",((e,{resolve:t})=>{t(function(e,{pageStyle:t,rootFontSize:n}){t&&(document.querySelector("uni-page-body")||document.body).setAttribute("style",t);n&&document.documentElement.style.fontSize!==n&&(document.documentElement.style.fontSize=n)}(Qc(),e))})),th={[le]:[],[se]:[],[ie]:[],[ne]:[],[oe]:[]};const nh=Yd(0,(()=>x({},Kh))),oh={formatArgs:{showToast:!0},beforeInvoke(){rc()},beforeSuccess(e,t){if(!t.showToast)return;const{t:n}=Jl(),o=n("uni.setClipboardData.success");o&&Hy({title:o,icon:"success",mask:!1})}},rh=(Boolean,{formatArgs:{count(e,t){(!e||e<=0)&&(t.count=9)},sizeType(e,t){t.sizeType=bd(e,gd)},sourceType(e,t){t.sourceType=bd(e,md)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}}),ih={formatArgs:{sourceType(e,t){t.sourceType=bd(e,md)},compressed:!0,maxDuration:60,camera:"back",extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=["*"])}}},ah=(Boolean,["all","image","video"]),sh={formatArgs:{count(e,t){(!e||e<=0)&&(t.count=100)},sourceType(e,t){t.sourceType=bd(e,md)},type(e,t){t.type=yd(e,ah)},extension(e,t){if(e instanceof Array&&0===e.length)return"param extension should not be empty.";e||(t.extension=[""])}}},lh={formatArgs:{src(e,t){t.src=Qu(e)}}},ch={formatArgs:{urls(e,t){t.urls=e.map((e=>I(e)&&e?Qu(e):""))},current(e,t){"number"==typeof e?t.current=e>0&&e<t.urls.length?e:0:I(e)&&e&&(t.current=Qu(e))}}},uh="json",dh=["text","arraybuffer"],fh=encodeURIComponent;ArrayBuffer,Boolean;const hh={formatArgs:{method(e,t){t.method=yd((e||"").toUpperCase(),vd)},data(e,t){t.data=e||""},url(e,t){t.method===vd[0]&&j(t.data)&&Object.keys(t.data).length&&(t.url=function(e,t){let n=e.split("#");const o=n[1]||"";n=n[0].split("?");let r=n[1]||"";e=n[0];const i=r.split("&").filter((e=>e)),a={};i.forEach((e=>{const t=e.split("=");a[t[0]]=t[1]}));for(const s in t)if(S(t,s)){let e=t[s];null==e?e="":j(e)&&(e=JSON.stringify(e)),a[fh(s)]=fh(e)}return r=Object.keys(a).map((e=>`${e}=${a[e]}`)).join("&"),e+(r?"?"+r:"")+(o?"#"+o:"")}(e,t.data))},header(e,t){const n=t.header=e||{};t.method!==vd[0]&&(Object.keys(n).find((e=>"content-type"===e.toLowerCase()))||(n["Content-Type"]="application/json"))},dataType(e,t){t.dataType=(e||uh).toLowerCase()},responseType(e,t){t.responseType=(e||"").toLowerCase(),-1===dh.indexOf(t.responseType)&&(t.responseType="text")}}},ph={formatArgs:{filePath(e,t){e&&(t.filePath=Qu(e))},header(e,t){t.header=e||{}},formData(e,t){t.formData=e||{}}}};const gh={url:{type:String,required:!0}},mh="navigateTo",vh="redirectTo",yh="reLaunch",bh="switchTab",_h="preloadPage",wh=(Ch(["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"]),Ch(["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"]),Mh(mh)),xh=Mh(vh),Th=Mh(yh),kh=Mh(bh),Sh={formatArgs:{delta(e,t){e=parseInt(e+"")||1,t.delta=Math.min($m().length-1,e)}}};function Ch(e){return{animationType:{type:String,validator(t){if(t&&-1===e.indexOf(t))return"`"+t+"` is not supported for `animationType` (supported values are: `"+e.join("`|`")+"`)"}},animationDuration:{type:Number}}}let Eh;function Oh(){Eh=""}function Mh(e){return{formatArgs:{url:Ih(e)},beforeAll:Oh}}function Ih(e){return function(t,n){if(!t)return'Missing required args: "url"';const o=(t=function(e){if(0===e.indexOf("/"))return e;let t="";const n=$m();return n.length&&(t=n[n.length-1].$page.route),su(t,e)}(t)).split("?")[0],r=lu(o,!0);if(!r)return"page `"+t+"` is not found";if(e===mh||e===vh){if(r.meta.isTabBar)return`can not ${e} a tabbar page`}else if(e===bh&&!r.meta.isTabBar)return"can not switch to no-tabBar page";if(e!==bh&&e!==_h||!r.meta.isTabBar||"appLaunch"===n.openType||(t=o),r.meta.isEntry&&(t=t.replace(r.alias,"/")),n.url=function(e){if(!I(e))return e;const t=e.indexOf("?");if(-1===t)return e;const n=e.slice(t+1).trim().replace(/^(\?|#|&)/,"");if(!n)return e;e=e.slice(0,t);const o=[];return n.split("&").forEach((e=>{const t=e.replace(/\+/g," ").split("="),n=t.shift(),r=t.length>0?t.join("="):"";o.push(n+"="+encodeURIComponent(r))})),o.length?e+"?"+o.join("&"):e}(t),"unPreloadPage"!==e)if(e!==_h){if(Eh===t&&"appLaunch"!==n.openType)return`${Eh} locked`;__uniConfig.ready&&(Eh=t)}else if(r.meta.isTabBar){const e=$m(),t=r.path.slice(1);if(e.find((e=>e.route===t)))return"tabBar page `"+t+"` already exists"}}}const Ah="setNavigationBarColor",Ph={formatArgs:{animation(e,t){e||(e={duration:0,timingFunc:"linear"}),t.animation={duration:e.duration||0,timingFunc:e.timingFunc||"linear"}}}},$h="setNavigationBarTitle",Lh={formatArgs:{duration:300}},Rh={formatArgs:{itemColor:"#000"}},jh=(Boolean,{formatArgs:{title:"",mask:!1}}),Bh=(Boolean,{beforeInvoke(){nc()},formatArgs:{title:"",content:"",placeholderText:"",showCancel:!0,editable:!1,cancelText(e,t){if(!S(t,"cancelText")){const{t:e}=Jl();t.cancelText=e("uni.showModal.cancel")}},cancelColor:"#000",confirmText(e,t){if(!S(t,"confirmText")){const{t:e}=Jl();t.confirmText=e("uni.showModal.confirm")}},confirmColor:"#007aff"}}),Dh=["success","loading","none","error"],Nh=(Boolean,{formatArgs:{title:"",icon(e,t){t.icon=yd(e,Dh)},image(e,t){t.image=e?Qu(e):""},duration:1500,mask:!1}}),Fh="stopPullDownRefresh",Vh="hideTabBar",qh=function(){if("object"==typeof window)if("IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype)"isIntersecting"in window.IntersectionObserverEntry.prototype||Object.defineProperty(window.IntersectionObserverEntry.prototype,"isIntersecting",{get:function(){return this.intersectionRatio>0}});else{var e=function(){for(var e=window.document,t=r(e);t;)t=r(e=t.ownerDocument);return e}(),t=[],n=null,o=null;a.prototype.THROTTLE_TIMEOUT=100,a.prototype.POLL_INTERVAL=null,a.prototype.USE_MUTATION_OBSERVER=!0,a._setupCrossOriginUpdater=function(){return n||(n=function(e,n){o=e&&n?d(e,n):{top:0,bottom:0,left:0,right:0,width:0,height:0},t.forEach((function(e){e._checkForIntersections()}))}),n},a._resetCrossOriginUpdater=function(){n=null,o=null},a.prototype.observe=function(e){if(!this._observationTargets.some((function(t){return t.element==e}))){if(!e||1!=e.nodeType)throw new Error("target must be an Element");this._registerInstance(),this._observationTargets.push({element:e,entry:null}),this._monitorIntersections(e.ownerDocument),this._checkForIntersections()}},a.prototype.unobserve=function(e){this._observationTargets=this._observationTargets.filter((function(t){return t.element!=e})),this._unmonitorIntersections(e.ownerDocument),0==this._observationTargets.length&&this._unregisterInstance()},a.prototype.disconnect=function(){this._observationTargets=[],this._unmonitorAllIntersections(),this._unregisterInstance()},a.prototype.takeRecords=function(){var e=this._queuedEntries.slice();return this._queuedEntries=[],e},a.prototype._initThresholds=function(e){var t=e||[0];return Array.isArray(t)||(t=[t]),t.sort().filter((function(e,t,n){if("number"!=typeof e||isNaN(e)||e<0||e>1)throw new Error("threshold must be a number between 0 and 1 inclusively");return e!==n[t-1]}))},a.prototype._parseRootMargin=function(e){var t=(e||"0px").split(/\s+/).map((function(e){var t=/^(-?\d*\.?\d+)(px|%)$/.exec(e);if(!t)throw new Error("rootMargin must be specified in pixels or percent");return{value:parseFloat(t[1]),unit:t[2]}}));return t[1]=t[1]||t[0],t[2]=t[2]||t[0],t[3]=t[3]||t[1],t},a.prototype._monitorIntersections=function(t){var n=t.defaultView;if(n&&-1==this._monitoringDocuments.indexOf(t)){var o=this._checkForIntersections,i=null,a=null;this.POLL_INTERVAL?i=n.setInterval(o,this.POLL_INTERVAL):(s(n,"resize",o,!0),s(t,"scroll",o,!0),this.USE_MUTATION_OBSERVER&&"MutationObserver"in n&&(a=new n.MutationObserver(o)).observe(t,{attributes:!0,childList:!0,characterData:!0,subtree:!0})),this._monitoringDocuments.push(t),this._monitoringUnsubscribes.push((function(){var e=t.defaultView;e&&(i&&e.clearInterval(i),l(e,"resize",o,!0)),l(t,"scroll",o,!0),a&&a.disconnect()}));var c=this.root&&(this.root.ownerDocument||this.root)||e;if(t!=c){var u=r(t);u&&this._monitorIntersections(u.ownerDocument)}}},a.prototype._unmonitorIntersections=function(t){var n=this._monitoringDocuments.indexOf(t);if(-1!=n){var o=this.root&&(this.root.ownerDocument||this.root)||e;if(!this._observationTargets.some((function(e){var n=e.element.ownerDocument;if(n==t)return!0;for(;n&&n!=o;){var i=r(n);if((n=i&&i.ownerDocument)==t)return!0}return!1}))){var i=this._monitoringUnsubscribes[n];if(this._monitoringDocuments.splice(n,1),this._monitoringUnsubscribes.splice(n,1),i(),t!=o){var a=r(t);a&&this._unmonitorIntersections(a.ownerDocument)}}}},a.prototype._unmonitorAllIntersections=function(){var e=this._monitoringUnsubscribes.slice(0);this._monitoringDocuments.length=0,this._monitoringUnsubscribes.length=0;for(var t=0;t<e.length;t++)e[t]()},a.prototype._checkForIntersections=function(){if(this.root||!n||o){var e=this._rootIsInDom(),t=e?this._getRootRect():{top:0,bottom:0,left:0,right:0,width:0,height:0};this._observationTargets.forEach((function(o){var r=o.element,a=c(r),s=this._rootContainsTarget(r),l=o.entry,u=e&&s&&this._computeTargetAndRootIntersection(r,a,t),d=null;this._rootContainsTarget(r)?n&&!this.root||(d=t):d={top:0,bottom:0,left:0,right:0,width:0,height:0};var f=o.entry=new i({time:window.performance&&performance.now&&performance.now(),target:r,boundingClientRect:a,rootBounds:d,intersectionRect:u});l?e&&s?this._hasCrossedThreshold(l,f)&&this._queuedEntries.push(f):l&&l.isIntersecting&&this._queuedEntries.push(f):this._queuedEntries.push(f)}),this),this._queuedEntries.length&&this._callback(this.takeRecords(),this)}},a.prototype._computeTargetAndRootIntersection=function(t,r,i){if("none"!=window.getComputedStyle(t).display){for(var a,s,l,u,f,p,g,m,v=r,y=h(t),b=!1;!b&&y;){var _=null,w=1==y.nodeType?window.getComputedStyle(y):{};if("none"==w.display)return null;if(y==this.root||9==y.nodeType)if(b=!0,y==this.root||y==e)n&&!this.root?!o||0==o.width&&0==o.height?(y=null,_=null,v=null):_=o:_=i;else{var x=h(y),T=x&&c(x),k=x&&this._computeTargetAndRootIntersection(x,T,i);T&&k?(y=x,_=d(T,k)):(y=null,v=null)}else{var S=y.ownerDocument;y!=S.body&&y!=S.documentElement&&"visible"!=w.overflow&&(_=c(y))}if(_&&(a=_,s=v,l=void 0,u=void 0,f=void 0,p=void 0,g=void 0,m=void 0,l=Math.max(a.top,s.top),u=Math.min(a.bottom,s.bottom),f=Math.max(a.left,s.left),p=Math.min(a.right,s.right),m=u-l,v=(g=p-f)>=0&&m>=0&&{top:l,bottom:u,left:f,right:p,width:g,height:m}||null),!v)break;y=y&&h(y)}return v}},a.prototype._getRootRect=function(){var t;if(this.root&&!p(this.root))t=c(this.root);else{var n=p(this.root)?this.root:e,o=n.documentElement,r=n.body;t={top:0,left:0,right:o.clientWidth||r.clientWidth,width:o.clientWidth||r.clientWidth,bottom:o.clientHeight||r.clientHeight,height:o.clientHeight||r.clientHeight}}return this._expandRectByRootMargin(t)},a.prototype._expandRectByRootMargin=function(e){var t=this._rootMarginValues.map((function(t,n){return"px"==t.unit?t.value:t.value*(n%2?e.width:e.height)/100})),n={top:e.top-t[0],right:e.right+t[1],bottom:e.bottom+t[2],left:e.left-t[3]};return n.width=n.right-n.left,n.height=n.bottom-n.top,n},a.prototype._hasCrossedThreshold=function(e,t){var n=e&&e.isIntersecting?e.intersectionRatio||0:-1,o=t.isIntersecting?t.intersectionRatio||0:-1;if(n!==o)for(var r=0;r<this.thresholds.length;r++){var i=this.thresholds[r];if(i==n||i==o||i<n!=i<o)return!0}},a.prototype._rootIsInDom=function(){return!this.root||f(e,this.root)},a.prototype._rootContainsTarget=function(t){var n=this.root&&(this.root.ownerDocument||this.root)||e;return f(n,t)&&(!this.root||n==t.ownerDocument)},a.prototype._registerInstance=function(){t.indexOf(this)<0&&t.push(this)},a.prototype._unregisterInstance=function(){var e=t.indexOf(this);-1!=e&&t.splice(e,1)},window.IntersectionObserver=a,window.IntersectionObserverEntry=i}function r(e){try{return e.defaultView&&e.defaultView.frameElement||null}catch(zE){return null}}function i(e){this.time=e.time,this.target=e.target,this.rootBounds=u(e.rootBounds),this.boundingClientRect=u(e.boundingClientRect),this.intersectionRect=u(e.intersectionRect||{top:0,bottom:0,left:0,right:0,width:0,height:0}),this.isIntersecting=!!e.intersectionRect;var t=this.boundingClientRect,n=t.width*t.height,o=this.intersectionRect,r=o.width*o.height;this.intersectionRatio=n?Number((r/n).toFixed(4)):this.isIntersecting?1:0}function a(e,t){var n,o,r,i=t||{};if("function"!=typeof e)throw new Error("callback must be a function");if(i.root&&1!=i.root.nodeType&&9!=i.root.nodeType)throw new Error("root must be a Document or Element");this._checkForIntersections=(n=this._checkForIntersections.bind(this),o=this.THROTTLE_TIMEOUT,r=null,function(){r||(r=setTimeout((function(){n(),r=null}),o))}),this._callback=e,this._observationTargets=[],this._queuedEntries=[],this._rootMarginValues=this._parseRootMargin(i.rootMargin),this.thresholds=this._initThresholds(i.threshold),this.root=i.root||null,this.rootMargin=this._rootMarginValues.map((function(e){return e.value+e.unit})).join(" "),this._monitoringDocuments=[],this._monitoringUnsubscribes=[]}function s(e,t,n,o){"function"==typeof e.addEventListener?e.addEventListener(t,n,o||!1):"function"==typeof e.attachEvent&&e.attachEvent("on"+t,n)}function l(e,t,n,o){"function"==typeof e.removeEventListener?e.removeEventListener(t,n,o||!1):"function"==typeof e.detatchEvent&&e.detatchEvent("on"+t,n)}function c(e){var t;try{t=e.getBoundingClientRect()}catch(n){}return t?(t.width&&t.height||(t={top:t.top,right:t.right,bottom:t.bottom,left:t.left,width:t.right-t.left,height:t.bottom-t.top}),t):{top:0,bottom:0,left:0,right:0,width:0,height:0}}function u(e){return!e||"x"in e?e:{top:e.top,y:e.top,bottom:e.bottom,left:e.left,x:e.left,right:e.right,width:e.width,height:e.height}}function d(e,t){var n=t.top-e.top,o=t.left-e.left;return{top:n,left:o,height:t.height,width:t.width,bottom:n+t.height,right:o+t.width}}function f(e,t){for(var n=t;n;){if(n==e)return!0;n=h(n)}return!1}function h(t){var n=t.parentNode;return 9==t.nodeType&&t!=e?r(t):(n&&n.assignedSlot&&(n=n.assignedSlot.parentNode),n&&11==n.nodeType&&n.host?n.host:n)}function p(e){return e&&9===e.nodeType}};function Wh(e){const{bottom:t,height:n,left:o,right:r,top:i,width:a}=e||{};return{bottom:t,height:n,left:o,right:r,top:i,width:a}}function Hh(e){const{intersectionRatio:t,boundingClientRect:{height:n,width:o},intersectionRect:{height:r,width:i}}=e;return 0!==t?t:r===n?i/o:r/n}const zh="",Uh={};function Yh(e,t){const n=Uh[e];return n?Promise.resolve(n):/^data:[a-z-]+\/[a-z-]+;base64,/.test(e)?Promise.resolve(function(e){const t=e.split(","),n=t[0].match(/:(.*?);/),o=n?n[1]:"",r=atob(t[1]);let i=r.length;const a=new Uint8Array(i);for(;i--;)a[i]=r.charCodeAt(i);return Xh(a,o)}(e)):t?Promise.reject(new Error("not find")):new Promise(((t,n)=>{const o=new XMLHttpRequest;o.open("GET",e,!0),o.responseType="blob",o.onload=function(){t(this.response)},o.onerror=n,o.send()}))}function Xh(e,t){let n;if(e instanceof File)n=e;else{t=t||e.type||"";const r=`${Date.now()}${function(e){const t=e.split("/")[1];return t?`.${t}`:""}(t)}`;try{n=new File([e],r,{type:t})}catch(o){n=e=e instanceof Blob?e:new Blob([e],{type:t}),n.name=n.name||r}}return n}function Gh(e){for(const n in Uh)if(S(Uh,n)){if(Uh[n]===e)return n}var t=(window.URL||window.webkitURL).createObjectURL(e);return Uh[t]=e,t}function Jh(e){(window.URL||window.webkitURL).revokeObjectURL(e),delete Uh[e]}const Kh=Au(),Zh=Au();const Qh=Ru({name:"ResizeSensor",props:{initial:{type:Boolean,default:!1}},emits:["resize"],setup(e,{emit:t}){const n=An(null),o=function(e){return()=>{const{firstElementChild:t,lastElementChild:n}=e.value;t.scrollLeft=1e5,t.scrollTop=1e5,n.scrollLeft=1e5,n.scrollTop=1e5}}(n),r=function(e,t,n){const o=mn({width:-1,height:-1});return Co((()=>x({},o)),(e=>t("resize",e))),()=>{const t=e.value;o.width=t.offsetWidth,o.height=t.offsetHeight,n()}}(n,t,o);return function(e,t,n,o){Jo(o),lr((()=>{t.initial&&eo(n);const r=e.value;r.offsetParent!==r.parentElement&&(r.parentElement.style.position="relative"),"AnimationEvent"in window||o()}))}(n,e,r,o),()=>Ei("uni-resize-sensor",{ref:n,onAnimationstartOnce:r},[Ei("div",{onScroll:r},[Ei("div",null,null)],40,["onScroll"]),Ei("div",{onScroll:r},[Ei("div",null,null)],40,["onScroll"])],40,["onAnimationstartOnce"])}});const ep=function(){if(navigator.userAgent.includes("jsdom"))return 1;const e=document.createElement("canvas");e.height=e.width=0;const t=e.getContext("2d"),n=t.backingStorePixelRatio||t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1;return(window.devicePixelRatio||1)/n}();function tp(e,t=!0){e.width=e.offsetWidth*(t?ep:1),e.height=e.offsetHeight*(t?ep:1),e.getContext("2d").__hidpi__=t}let np=!1;function op(){if(np)return;np=!0;const e={fillRect:"all",clearRect:"all",strokeRect:"all",moveTo:"all",lineTo:"all",arc:[0,1,2],arcTo:"all",bezierCurveTo:"all",isPointinPath:"all",isPointinStroke:"all",quadraticCurveTo:"all",rect:"all",translate:"all",createRadialGradient:"all",createLinearGradient:"all",transform:[4,5],setTransform:[4,5]},t=CanvasRenderingContext2D.prototype;var n;t.drawImageByCanvas=(n=t.drawImage,function(e,t,o,r,i,a,s,l,c,u){if(!this.__hidpi__)return n.apply(this,arguments);t*=ep,o*=ep,r*=ep,i*=ep,a*=ep,s*=ep,l=u?l*ep:l,c=u?c*ep:c,n.call(this,e,t,o,r,i,a,s,l,c)}),1!==ep&&(!function(e,t){for(const n in e)S(e,n)&&t(e[n],n)}(e,(function(e,n){t[n]=function(t){return function(){if(!this.__hidpi__)return t.apply(this,arguments);let n=Array.prototype.slice.call(arguments);if("all"===e)n=n.map((function(e){return e*ep}));else if(Array.isArray(e))for(let t=0;t<e.length;t++)n[e[t]]*=ep;return t.apply(this,n)}}(t[n])})),t.stroke=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);this.lineWidth*=ep,e.apply(this,arguments),this.lineWidth/=ep}}(t.stroke),t.fillText=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);const t=Array.prototype.slice.call(arguments);t[1]*=ep,t[2]*=ep,t[3]&&"number"==typeof t[3]&&(t[3]*=ep);var n=this.font;this.font=n.replace(/(\d+\.?\d*)(px|em|rem|pt)/g,(function(e,t,n){return t*ep+n})),e.apply(this,t),this.font=n}}(t.fillText),t.strokeText=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);var t=Array.prototype.slice.call(arguments);t[1]*=ep,t[2]*=ep,t[3]&&"number"==typeof t[3]&&(t[3]*=ep);var n=this.font;this.font=n.replace(/(\d+\.?\d*)(px|em|rem|pt)/g,(function(e,t,n){return t*ep+n})),e.apply(this,t),this.font=n}}(t.strokeText),t.drawImage=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);this.scale(ep,ep),e.apply(this,arguments),this.scale(1/ep,1/ep)}}(t.drawImage))}const rp=Be((()=>op()));function ip(e){return e?Qu(e):e}function ap(e){return(e=e.slice(0))[3]=e[3]/255,"rgba("+e.join(",")+")"}function sp(e,t){Array.from(t).forEach((t=>{t.x=t.clientX-e.left,t.y=t.clientY-e.top}))}let lp;function cp(e=0,t=0){return lp||(lp=document.createElement("canvas")),lp.width=e,lp.height=t,lp}const up=Ru({inheritAttrs:!1,name:"Canvas",compatConfig:{MODE:3},props:{canvasId:{type:String,default:""},disableScroll:{type:[Boolean,String],default:!1},hidpi:{type:Boolean,default:!0}},computed:{id(){return this.canvasId}},setup(e,{emit:t,slots:n}){rp();const o=An(null),r=An(null),i=An(!1),a=function(e){return(t,n)=>{e(t,gu(n))}}(t),{$attrs:s,$excludeAttrs:l,$listeners:c}=Zp({excludeListeners:!0}),{_listeners:u}=function(e,t,n){const o=Gi((()=>{let o=["onTouchstart","onTouchmove","onTouchend"],r=t.value,i=x({},(()=>{let e={};for(const t in r)if(S(r,t)){const n=r[t];e[t]=n}return e})());return o.forEach((t=>{let o=[];i[t]&&o.push(Fu((e=>{const o=e.currentTarget.getBoundingClientRect();sp(o,e.touches),sp(o,e.changedTouches),n(t.replace("on","").toLocaleLowerCase(),e)}))),e.disableScroll&&"onTouchmove"===t&&o.push(Rc),i[t]=o})),i}));return{_listeners:o}}(e,c,a),{_handleSubscribe:d,_resize:f}=function(e,t,n){let o=[],r={};const i=Gi((()=>e.hidpi?ep:1));function a(n){let o=t.value;if(!n||o.width!==Math.floor(n.width*i.value)||o.height!==Math.floor(n.height*i.value))if(o.width>0&&o.height>0){let t=o.getContext("2d"),n=t.getImageData(0,0,o.width,o.height);tp(o,e.hidpi),t.putImageData(n,0,0)}else tp(o,e.hidpi)}function s({actions:e,reserve:i},a){if(!e)return;if(n.value)return void o.push([e,i]);let s=t.value,u=s.getContext("2d");i||(u.fillStyle="#000000",u.strokeStyle="#000000",u.shadowColor="#000000",u.shadowBlur=0,u.shadowOffsetX=0,u.shadowOffsetY=0,u.setTransform(1,0,0,1,0,0),u.clearRect(0,0,s.width,s.height)),l(e);for(let t=0;t<e.length;t++){const n=e[t];let o=n.method;const i=n.data,s=i[0];if(/^set/.test(o)&&"setTransform"!==o){const n=o[3].toLowerCase()+o.slice(4);let r;if("fillStyle"===n||"strokeStyle"===n){if("normal"===s)r=ap(i[1]);else if("linear"===s){const e=u.createLinearGradient(...i[1]);i[2].forEach((function(t){const n=t[0],o=ap(t[1]);e.addColorStop(n,o)})),r=e}else if("radial"===s){let e=i[1];const t=e[0],n=e[1],o=e[2],a=u.createRadialGradient(t,n,0,t,n,o);i[2].forEach((function(e){const t=e[0],n=ap(e[1]);a.addColorStop(t,n)})),r=a}else if("pattern"===s){if(!c(i[1],e.slice(t+1),a,(function(e){e&&(u[n]=u.createPattern(e,i[2]))})))break;continue}u[n]=r}else if("globalAlpha"===n)u[n]=Number(s)/255;else if("shadow"===n){let e=["shadowOffsetX","shadowOffsetY","shadowBlur","shadowColor"];i.forEach((function(t,n){u[e[n]]="shadowColor"===e[n]?ap(t):t}))}else if("fontSize"===n){const e=u.__font__||u.font;u.__font__=u.font=e.replace(/\d+\.?\d*px/,s+"px")}else"lineDash"===n?(u.setLineDash(s),u.lineDashOffset=i[1]||0):"textBaseline"===n?("normal"===s&&(i[0]="alphabetic"),u[n]=s):"font"===n?u.__font__=u.font=s:u[n]=s}else if("fillPath"===o||"strokePath"===o)o=o.replace(/Path/,""),u.beginPath(),i.forEach((function(e){u[e.method].apply(u,e.data)})),u[o]();else if("fillText"===o)u.fillText.apply(u,i);else if("drawImage"===o){if("break"===function(){let n=[...i],o=n[0],s=n.slice(1);if(r=r||{},!c(o,e.slice(t+1),a,(function(e){e&&u.drawImage.apply(u,[e].concat([...s.slice(4,8)],[...s.slice(0,4)]))})))return"break"}())break}else"clip"===o?(i.forEach((function(e){u[e.method].apply(u,e.data)})),u.clip()):u[o].apply(u,i)}n.value||a({errMsg:"drawCanvas:ok"})}function l(e){e.forEach((function(e){let t=e.method,n=e.data,o="";function i(){const e=r[o]=new Image;e.onload=function(){e.ready=!0},function(e){const t=document.createElement("a");return t.href=e,t.origin===location.origin?Promise.resolve(e):Yh(e).then(Gh)}(o).then((t=>{e.src=t})).catch((()=>{e.src=o}))}"drawImage"===t?(o=n[0],o=ip(o),n[0]=o):"setFillStyle"===t&&"pattern"===n[0]&&(o=n[1],o=ip(o),n[1]=o),o&&!r[o]&&i()}))}function c(e,t,i,a){let l=r[e];return l.ready?(a(l),!0):(o.unshift([t,!0]),n.value=!0,l.onload=function(){l.ready=!0,a(l),n.value=!1;let e=o.slice(0);o=[];for(let t=e.shift();t;)s({actions:t[0],reserve:t[1]},i),t=e.shift()},!1)}function u({x:e=0,y:n=0,width:o,height:r,destWidth:a,destHeight:s,hidpi:l=!0,dataType:c,quality:u=1,type:d="png"},f){const h=t.value;let p;const g=h.offsetWidth-e;o=o?Math.min(o,g):g;const m=h.offsetHeight-n;r=r?Math.min(r,m):m,l?(a=o,s=r):a||s?a?s||(s=Math.round(r/o*a)):a=Math.round(o/r*s):(a=Math.round(o*i.value),s=Math.round(r*i.value));const v=cp(a,s),y=v.getContext("2d");let b;"jpeg"!==d&&"jpg"!==d||(d="jpeg",y.fillStyle="#fff",y.fillRect(0,0,a,s)),y.__hidpi__=!0,y.drawImageByCanvas(h,e,n,o,r,0,0,a,s,!1);try{let e;if("base64"===c)p=v.toDataURL(`image/${d}`,u);else{const e=y.getImageData(0,0,a,s);p=Array.prototype.slice.call(e.data)}b={data:p,compressed:e,width:a,height:s}}catch(_){b={errMsg:`canvasGetImageData:fail ${_}`}}if(v.height=v.width=0,y.__hidpi__=!1,!f)return b;f(b)}function d({data:e,x:n,y:o,width:r,height:i,compressed:a},s){try{0,i||(i=Math.round(e.length/4/r));const a=cp(r,i);a.getContext("2d").putImageData(new ImageData(new Uint8ClampedArray(e),r,i),0,0),t.value.getContext("2d").drawImage(a,n,o,r,i),a.height=a.width=0}catch(l){return void s({errMsg:"canvasPutImageData:fail"})}s({errMsg:"canvasPutImageData:ok"})}function f({x:e=0,y:t=0,width:n,height:o,destWidth:r,destHeight:i,fileType:a,quality:s,dirname:l},c){const d=u({x:e,y:t,width:n,height:o,destWidth:r,destHeight:i,hidpi:!1,dataType:"base64",type:a,quality:s});var f;d.data&&d.data.length?(f=d.data,((e,t)=>{let n="toTempFilePath:"+(e?"fail":"ok");e&&(n+=` ${e.message}`),c({errMsg:n,tempFilePath:t})})(null,f)):c({errMsg:d.errMsg.replace("canvasPutImageData","toTempFilePath")})}const h={actionsChanged:s,getImageData:u,putImageData:d,toTempFilePath:f};function p(e,t,n){let o=h[e];0!==e.indexOf("_")&&M(o)&&o(t,n)}return x(h,{_resize:a,_handleSubscribe:p})}(e,o,i);return sm(d,function(e){const t=Xc(),n=Ni().proxy,o=n.$options.name.toLowerCase(),r=e||n.id||"context"+lm++;return lr((()=>{n.$el.__uniContextInfo={id:r,type:o,page:t}})),`${o}.${r}`}(e.canvasId),!0),lr((()=>{f()})),()=>{const{canvasId:t,disableScroll:i}=e;return Ei("uni-canvas",Li({"canvas-id":t,"disable-scroll":i},s.value,l.value,u.value),[Ei("canvas",{ref:o,class:"uni-canvas-canvas",width:"300",height:"150"},null,512),Ei("div",{style:"position: absolute;top: 0;left: 0;width: 100%;height: 100%;overflow: hidden;"},[n.default&&n.default()]),Ei(Qh,{ref:r,onResize:f},null,8,["onResize"])],16,["canvas-id","disable-scroll"])}}});const dp=qc("ucg"),fp=Ru({name:"CheckboxGroup",props:{name:{type:String,default:""}},emits:["change"],setup(e,{emit:t,slots:n}){const o=An(null);return function(e,t){const n=[],o=()=>n.reduce(((e,t)=>(t.value.checkboxChecked&&e.push(t.value.value),e)),new Array);xo(dp,{addField(e){n.push(e)},removeField(e){n.splice(n.indexOf(e),1)},checkboxChange(e){t("change",e,{value:o()})}});const r=To(qu,!1);r&&r.addField({submit:()=>{let t=["",null];return""!==e.name&&(t[0]=e.name,t[1]=o()),t}})}(e,Vu(o,t)),()=>Ei("uni-checkbox-group",{ref:o},[n.default&&n.default()],512)}});const hp=Ru({name:"Checkbox",props:{checked:{type:[Boolean,String],default:!1},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},color:{type:String,default:"#007aff"},value:{type:String,default:""}},setup(e,{slots:t}){const n=An(e.checked),o=An(e.value);Co([()=>e.checked,()=>e.value],(([e,t])=>{n.value=e,o.value=t}));const{uniCheckGroup:r,uniLabel:i}=function(e,t,n){const o=Gi((()=>({checkboxChecked:Boolean(e.value),value:t.value}))),r={reset:n},i=To(dp,!1);i&&i.addField(o);const a=To(qu,!1);a&&a.addField(r);const s=To(zu,!1);return dr((()=>{i&&i.removeField(o),a&&a.removeField(r)})),{uniCheckGroup:i,uniForm:a,uniLabel:s}}(n,o,(()=>{n.value=!1})),a=t=>{e.disabled||(n.value=!n.value,r&&r.checkboxChange(t),t.stopPropagation())};return i&&(i.addHandler(a),dr((()=>{i.removeHandler(a)}))),Yu(e,{"label-click":a}),()=>{const o=Nu(e,"disabled");return Ei("uni-checkbox",Li(o,{onClick:a}),[Ei("div",{class:"uni-checkbox-wrapper"},[Ei("div",{class:["uni-checkbox-input",{"uni-checkbox-input-disabled":e.disabled}]},[n.value?Yc(zc,e.color,22):""],2),t.default&&t.default()])],16,["onClick"])}}});function pp(){}const gp={cursorSpacing:{type:[Number,String],default:0},showConfirmBar:{type:[Boolean,String],default:"auto"},adjustPosition:{type:[Boolean,String],default:!0},autoBlur:{type:[Boolean,String],default:!1}};function mp(e,t,n){function o(e){const t=Gi((()=>0===String(navigator.vendor).indexOf("Apple")));e.addEventListener("focus",(()=>{clearTimeout(undefined),document.addEventListener("click",pp,!1)}));e.addEventListener("blur",(()=>{t.value&&e.blur(),document.removeEventListener("click",pp,!1),t.value&&document.documentElement.scrollTo(document.documentElement.scrollLeft,document.documentElement.scrollTop)}))}Co((()=>t.value),(e=>e&&o(e)))}var vp=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,yp=/^<\/([-A-Za-z0-9_]+)[^>]*>/,bp=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,_p=Cp("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),wp=Cp("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),xp=Cp("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),Tp=Cp("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),kp=Cp("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),Sp=Cp("script,style");function Cp(e){for(var t={},n=e.split(","),o=0;o<n.length;o++)t[n[o]]=!0;return t}const Ep={src:{type:String,default:""},mode:{type:String,default:"scaleToFill"},lazyLoad:{type:[Boolean,String],default:!1},draggable:{type:Boolean,default:!1}},Op={widthFix:["offsetWidth","height",(e,t)=>e/t],heightFix:["offsetHeight","width",(e,t)=>e*t]},Mp={aspectFit:["center center","contain"],aspectFill:["center center","cover"],widthFix:[,"100% 100%"],heightFix:[,"100% 100%"],top:["center top"],bottom:["center bottom"],center:["center center"],left:["left center"],right:["right center"],"top left":["left top"],"top right":["right top"],"bottom left":["left bottom"],"bottom right":["right bottom"]},Ip=Ru({name:"Image",props:Ep,setup(e,{emit:t}){const n=An(null),o=function(e,t){const n=An(""),o=Gi((()=>{let e="auto",o="";const r=Mp[t.mode];return r?(r[0]&&(o=r[0]),r[1]&&(e=r[1])):(o="0% 0%",e="100% 100%"),`background-image:${n.value?'url("'+n.value+'")':"none"};background-position:${o};background-size:${e};`})),r=mn({rootEl:e,src:Gi((()=>t.src?Qu(t.src):"")),origWidth:0,origHeight:0,origStyle:{width:"",height:""},modeStyle:o,imgSrc:n});return lr((()=>{const t=e.value.style;r.origWidth=Number(t.width)||0,r.origHeight=Number(t.height)||0})),r}(n,e),r=Vu(n,t),{fixSize:i}=function(e,t,n){const o=()=>{const{mode:o}=t,r=Op[o];if(!r)return;const{origWidth:i,origHeight:a}=n,s=i&&a?i/a:0;if(!s)return;const l=e.value,c=l[r[0]];c&&(l.style[r[1]]=function(e){Ap&&e>10&&(e=2*Math.round(e/2));return e}(r[2](c,s))+"px")},r=()=>{const{style:t}=e.value,{origStyle:{width:o,height:r}}=n;t.width=o,t.height=r};return Co((()=>t.mode),((e,t)=>{Op[t]&&r(),Op[e]&&o()})),{fixSize:o,resetSize:r}}(n,e,o);return function(e,t,n,o,r){let i,a;const s=(t=0,n=0,o="")=>{e.origWidth=t,e.origHeight=n,e.imgSrc=o},l=l=>{if(!l)return c(),void s();i=i||new Image,i.onload=e=>{const{width:u,height:d}=i;s(u,d,l),o(),i.draggable=t.draggable,a&&a.remove(),a=i,n.value.appendChild(i),c(),r("load",e,{width:u,height:d})},i.onerror=t=>{s(),c(),r("error",t,{errMsg:`GET ${e.src} 404 (Not Found)`})},i.src=l},c=()=>{i&&(i.onload=null,i.onerror=null,i=null)};Co((()=>e.src),(e=>l(e))),Co((()=>e.imgSrc),(e=>{!e&&a&&(a.remove(),a=null)})),lr((()=>l(e.src))),dr((()=>c()))}(o,e,n,i,r),()=>Ei("uni-image",{ref:n},[Ei("div",{style:o.modeStyle},null,4),Op[e.mode]?Ei(Qh,{onResize:i},null,8,["onResize"]):Ei("span",null,null)],512)}});const Ap="Google Inc."===navigator.vendor;const Pp=He(!0),$p=[];let Lp,Rp=0;const jp=e=>$p.forEach((t=>t.userAction=e));function Bp(e={userAction:!1}){if(!Lp){["touchstart","touchmove","touchend","mousedown","mouseup"].forEach((e=>{document.addEventListener(e,(function(){!Rp&&jp(!0),Rp++,setTimeout((()=>{! --Rp&&jp(!1)}),0)}),Pp)})),Lp=!0}$p.push(e)}const Dp=()=>!!Rp;function Np(){const e=mn({userAction:!1});return lr((()=>{Bp(e)})),dr((()=>{!function(e){const t=$p.indexOf(e);t>=0&&$p.splice(t,1)}(e)})),{state:e}}function Fp(){const e=mn({attrs:{}});return lr((()=>{let t=Ni();for(;t;){const n=t.type.__scopeId;n&&(e.attrs[n]=""),t=t.proxy&&"page"===t.proxy.$mpType?null:t.parent}})),{state:e}}function Vp(e,t){const n=document.activeElement;if(!n)return t({});const o={};["input","textarea"].includes(n.tagName.toLowerCase())&&(o.start=n.selectionStart,o.end=n.selectionEnd),t(o)}function qp(e,t){return"number"===t&&isNaN(Number(e))&&(e=""),null===e?"":String(e)}const Wp=["none","text","decimal","numeric","tel","search","email","url"],Hp=x({},{name:{type:String,default:""},modelValue:{type:[String,Number],default:""},value:{type:[String,Number],default:""},disabled:{type:[Boolean,String],default:!1},autoFocus:{type:[Boolean,String],default:!1},focus:{type:[Boolean,String],default:!1},cursor:{type:[Number,String],default:-1},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},type:{type:String,default:"text"},password:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},placeholderStyle:{type:String,default:""},placeholderClass:{type:String,default:""},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},ignoreCompositionEvent:{type:Boolean,default:!0},step:{type:String,default:"0.000000000000000001"},inputmode:{type:String,default:void 0,validator:e=>!!~Wp.indexOf(e)}},gp),zp=["input","focus","blur","update:value","update:modelValue","update:focus","compositionstart","compositionupdate","compositionend","keyboardheightchange"];function Up(e,t,n,o){const r=Je((n=>{t.value=qp(n,e.type)}),100,{setTimeout:setTimeout,clearTimeout:clearTimeout});Co((()=>e.modelValue),r),Co((()=>e.value),r);const i=function(e,t){let n,o,r=0;const i=function(...i){const a=Date.now();clearTimeout(n),o=()=>{o=null,r=a,e.apply(this,i)},a-r<t?n=setTimeout(o,t-(a-r)):o()};return i.cancel=function(){clearTimeout(n),o=null},i.flush=function(){clearTimeout(n),o&&o()},i}(((e,t)=>{r.cancel(),n("update:modelValue",t.value),n("update:value",t.value),o("input",e,t)}),100);return sr((()=>{r.cancel(),i.cancel()})),{trigger:o,triggerInput:(e,t,n)=>{r.cancel(),i(e,t),n&&i.flush()}}}function Yp(e,t){Np();const n=Gi((()=>e.autoFocus||e.focus));function o(){if(!n.value)return;const e=t.value;e?e.focus():setTimeout(o,100)}Co((()=>e.focus),(e=>{e?o():function(){const e=t.value;e&&e.blur()}()})),lr((()=>{n.value&&eo(o)}))}function Xp(e,t,n,o){fc(Zc(),"getSelectedTextRange",Vp);const{fieldRef:r,state:i,trigger:a}=function(e,t,n){const o=An(null),r=Vu(t,n),i=Gi((()=>{const t=Number(e.selectionStart);return isNaN(t)?-1:t})),a=Gi((()=>{const t=Number(e.selectionEnd);return isNaN(t)?-1:t})),s=Gi((()=>{const t=Number(e.cursor);return isNaN(t)?-1:t})),l=Gi((()=>{var t=Number(e.maxlength);return isNaN(t)?140:t})),c=qp(e.modelValue,e.type)||qp(e.value,e.type),u=mn({value:c,valueOrigin:c,maxlength:l,focus:e.focus,composing:!1,selectionStart:i,selectionEnd:a,cursor:s});return Co((()=>u.focus),(e=>n("update:focus",e))),Co((()=>u.maxlength),(e=>u.value=u.value.slice(0,e))),{fieldRef:o,state:u,trigger:r}}(e,t,n),{triggerInput:s}=Up(e,i,n,a);Yp(e,r),mp(0,r);const{state:l}=Fp();!function(e,t){const n=To(qu,!1);if(!n)return;const o=Ni(),r={submit(){const n=o.proxy;return[n[e],I(t)?n[t]:t.value]},reset(){I(t)?o.proxy[t]="":t.value=""}};n.addField(r),dr((()=>{n.removeField(r)}))}("name",i),function(e,t,n,o,r,i){function a(){const n=e.value;n&&t.focus&&t.selectionStart>-1&&t.selectionEnd>-1&&"number"!==n.type&&(n.selectionStart=t.selectionStart,n.selectionEnd=t.selectionEnd)}function s(){const n=e.value;n&&t.focus&&t.selectionStart<0&&t.selectionEnd<0&&t.cursor>-1&&"number"!==n.type&&(n.selectionEnd=n.selectionStart=t.cursor)}function l(e){return"number"===e.type?null:e.selectionEnd}Co([()=>t.selectionStart,()=>t.selectionEnd],a),Co((()=>t.cursor),s),Co((()=>e.value),(function(){const c=e.value;if(!c)return;const u=function(e,o){e.stopPropagation(),M(i)&&!1===i(e,t)||(t.value=c.value,t.composing&&n.ignoreCompositionEvent||r(e,{value:c.value,cursor:l(c)},o))};function d(e){n.ignoreCompositionEvent||o(e.type,e,{value:e.data})}c.addEventListener("change",(e=>e.stopPropagation())),c.addEventListener("focus",(function(e){t.focus=!0,o("focus",e,{value:t.value}),a(),s()})),c.addEventListener("blur",(function(e){t.composing&&(t.composing=!1,u(e,!0)),t.focus=!1,o("blur",e,{value:t.value,cursor:l(e.target)})})),c.addEventListener("input",u),c.addEventListener("compositionstart",(e=>{e.stopPropagation(),t.composing=!0,d(e)})),c.addEventListener("compositionend",(e=>{e.stopPropagation(),t.composing&&(t.composing=!1,u(e)),d(e)})),c.addEventListener("compositionupdate",d)}))}(r,i,e,a,s,o);return{fieldRef:r,state:i,scopedAttrsState:l,fixDisabledColor:0===String(navigator.vendor).indexOf("Apple")&&CSS.supports("image-orientation:from-image"),trigger:a}}const Gp=Ru({name:"Input",props:x({},Hp,{placeholderClass:{type:String,default:"input-placeholder"},textContentType:{type:String,default:""}}),emits:["confirm",...zp],setup(e,{emit:t}){const n=["text","number","idcard","digit","password","tel"],o=["off","one-time-code"],r=Gi((()=>{let t="";switch(e.type){case"text":"search"===e.confirmType&&(t="search");break;case"idcard":t="text";break;case"digit":t="number";break;default:t=~n.includes(e.type)?e.type:"text"}return e.password?"password":t})),i=Gi((()=>{const t=o.indexOf(e.textContentType),n=o.indexOf(W(e.textContentType));return o[-1!==t?t:-1!==n?n:0]}));let a,s=An("");const l=An(null),{fieldRef:c,state:u,scopedAttrsState:d,fixDisabledColor:f,trigger:h}=Xp(e,l,t,((e,t)=>{const n=e.target;if("number"===r.value){if(a&&(n.removeEventListener("blur",a),a=null),n.validity&&!n.validity.valid){if((!s.value||!n.value)&&"-"===e.data||"-"===s.value[0]&&"deleteContentBackward"===e.inputType)return s.value="-",t.value="",a=()=>{s.value=n.value=""},n.addEventListener("blur",a),!1;if(s.value)if(-1!==s.value.indexOf(".")){if("."!==e.data&&"deleteContentBackward"===e.inputType){const e=s.value.indexOf(".");return s.value=n.value=t.value=s.value.slice(0,e),!0}}else if("."===e.data)return s.value+=".",a=()=>{s.value=n.value=s.value.slice(0,-1)},n.addEventListener("blur",a),!1;return s.value=t.value=n.value="-"===s.value?"":s.value,!1}s.value=n.value;const o=t.maxlength;if(o>0&&n.value.length>o)return n.value=n.value.slice(0,o),t.value=n.value,!1}}));Co((()=>u.value),(t=>{"number"!==e.type||"-"===s.value&&""===t||(s.value=t)}));const p=["number","digit"],g=Gi((()=>p.includes(e.type)?e.step:""));function m(t){if("Enter"!==t.key)return;const n=t.target;t.stopPropagation(),h("confirm",t,{value:n.value}),!e.confirmHold&&n.blur()}return()=>{let t=e.disabled&&f?Ei("input",{key:"disabled-input",ref:c,value:u.value,tabindex:"-1",readonly:!!e.disabled,type:r.value,maxlength:u.maxlength,step:g.value,class:"uni-input-input",onFocus:e=>e.target.blur()},null,40,["value","readonly","type","maxlength","step","onFocus"]):Ei("input",{key:"input",ref:c,value:u.value,disabled:!!e.disabled,type:r.value,maxlength:u.maxlength,step:g.value,enterkeyhint:e.confirmType,pattern:"number"===e.type?"[0-9]*":void 0,class:"uni-input-input",autocomplete:i.value,onKeyup:m,inputmode:e.inputmode},null,40,["value","disabled","type","maxlength","step","enterkeyhint","pattern","autocomplete","onKeyup","inputmode"]);return Ei("uni-input",{ref:l},[Ei("div",{class:"uni-input-wrapper"},[vr(Ei("div",Li(d.attrs,{style:e.placeholderStyle,class:["uni-input-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Wa,!(u.value.length||"-"===s.value)]]),"search"===e.confirmType?Ei("form",{action:"",onSubmit:e=>e.preventDefault(),class:"uni-input-form"},[t],40,["onSubmit"]):t])],512)}}});const Jp=["class","style"],Kp=/^on[A-Z]+/,Zp=(e={})=>{const{excludeListeners:t=!1,excludeKeys:n=[]}=e,o=Ni(),r=Pn({}),i=Pn({}),a=Pn({}),s=n.concat(Jp);return o.attrs=mn(o.attrs),ko((()=>{const e=(n=o.attrs,Object.keys(n).map((e=>[e,n[e]]))).reduce(((e,[n,o])=>(s.includes(n)?e.exclude[n]=o:Kp.test(n)?(t||(e.attrs[n]=o),e.listeners[n]=o):e.attrs[n]=o,e)),{exclude:{},attrs:{},listeners:{}});var n;r.value=e.attrs,i.value=e.listeners,a.value=e.exclude})),{$attrs:r,$listeners:i,$excludeAttrs:a}};function Qp(e){const t=[];return C(e)&&e.forEach((e=>{wi(e)?e.type===ci?t.push(...Qp(e.children)):t.push(e):C(e)&&t.push(...Qp(e))})),t}const eg=Ru({inheritAttrs:!1,name:"MovableArea",props:{scaleArea:{type:Boolean,default:!1}},setup(e,{slots:t}){const n=An(null),o=An(!1);let{setContexts:r,events:i}=function(e,t){const n=An(0),o=An(0),r=mn({x:null,y:null}),i=An(null);let a=null,s=[];function l(t){t&&1!==t&&(e.scaleArea?s.forEach((function(e){e._setScale(t)})):a&&a._setScale(t))}function c(e,n=s){let o=t.value;function r(e){for(let t=0;t<n.length;t++){const o=n[t];if(e===o.rootRef.value)return o}return e===o||e===document.body||e===document?null:r(e.parentNode)}return r(e)}const u=Fu((t=>{let n=t.touches;if(n&&n.length>1){let t={x:n[1].pageX-n[0].pageX,y:n[1].pageY-n[0].pageY};if(i.value=tg(t),r.x=t.x,r.y=t.y,!e.scaleArea){let e=c(n[0].target),t=c(n[1].target);a=e&&e===t?e:null}}})),d=Fu((e=>{let t=e.touches;if(t&&t.length>1){e.preventDefault();let n={x:t[1].pageX-t[0].pageX,y:t[1].pageY-t[0].pageY};if(null!==r.x&&i.value&&i.value>0){l(tg(n)/i.value)}r.x=n.x,r.y=n.y}})),f=Fu((t=>{let n=t.touches;n&&n.length||t.changedTouches&&(r.x=0,r.y=0,i.value=null,e.scaleArea?s.forEach((function(e){e._endScale()})):a&&a._endScale())}));function h(){p(),s.forEach((function(e,t){e.setParent()}))}function p(){let e=window.getComputedStyle(t.value),r=t.value.getBoundingClientRect();n.value=r.width-["Left","Right"].reduce((function(t,n){const o="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[o])}),0),o.value=r.height-["Top","Bottom"].reduce((function(t,n){const o="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[o])}),0)}return xo("movableAreaWidth",n),xo("movableAreaHeight",o),{setContexts(e){s=e},events:{_onTouchstart:u,_onTouchmove:d,_onTouchend:f,_resize:h}}}(e,n);const{$listeners:a,$attrs:s,$excludeAttrs:l}=Zp(),c=a.value;["onTouchstart","onTouchmove","onTouchend"].forEach((e=>{let t=c[e],n=i[`_${e}`];c[e]=t?[].concat(t,n):n})),lr((()=>{i._resize(),o.value=!0}));let u=[];const d=[];function f(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n=n.el;const o=d.find((e=>n===e.rootRef.value));o&&e.push(Sn(o))}r(e)}return xo("_isMounted",o),xo("movableAreaRootRef",n),xo("addMovableViewContext",(e=>{d.push(e),f()})),xo("removeMovableViewContext",(e=>{const t=d.indexOf(e);t>=0&&(d.splice(t,1),f())})),()=>{const e=t.default&&t.default();return u=Qp(e),Ei("uni-movable-area",Li({ref:n},s.value,l.value,c),[Ei(Qh,{onResize:i._resize},null,8,["onResize"]),u],16)}}});function tg(e){return Math.sqrt(e.x*e.x+e.y*e.y)}const ng=function(e,t,n,o){e.addEventListener(t,(e=>{M(n)&&!1===n(e)&&((void 0===e.cancelable||e.cancelable)&&e.preventDefault(),e.stopPropagation())}),{passive:!1})};let og,rg;function ig(e,t,n){dr((()=>{document.removeEventListener("mousemove",og),document.removeEventListener("mouseup",rg)}));let o=0,r=0,i=0,a=0;const s=function(e,n,s,l){if(!1===t({cancelable:e.cancelable,target:e.target,currentTarget:e.currentTarget,preventDefault:e.preventDefault.bind(e),stopPropagation:e.stopPropagation.bind(e),touches:e.touches,changedTouches:e.changedTouches,detail:{state:n,x:s,y:l,dx:s-o,dy:l-r,ddx:s-i,ddy:l-a,timeStamp:e.timeStamp}}))return!1};let l,c,u=null;ng(e,"touchstart",(function(e){if(l=!0,1===e.touches.length&&!u)return u=e,o=i=e.touches[0].pageX,r=a=e.touches[0].pageY,s(e,"start",o,r)})),ng(e,"mousedown",(function(e){if(c=!0,!l&&!u)return u=e,o=i=e.pageX,r=a=e.pageY,s(e,"start",o,r)})),ng(e,"touchmove",(function(e){if(1===e.touches.length&&u){const t=s(e,"move",e.touches[0].pageX,e.touches[0].pageY);return i=e.touches[0].pageX,a=e.touches[0].pageY,t}}));const d=og=function(e){if(!l&&c&&u){const t=s(e,"move",e.pageX,e.pageY);return i=e.pageX,a=e.pageY,t}};document.addEventListener("mousemove",d),ng(e,"touchend",(function(e){if(0===e.touches.length&&u)return l=!1,u=null,s(e,"end",e.changedTouches[0].pageX,e.changedTouches[0].pageY)}));const f=rg=function(e){if(c=!1,!l&&u)return u=null,s(e,"end",e.pageX,e.pageY)};document.addEventListener("mouseup",f),ng(e,"touchcancel",(function(e){if(u){l=!1;const t=u;return u=null,s(e,n?"cancel":"end",t.touches[0].pageX,t.touches[0].pageY)}}))}function ag(e,t,n){return e>t-n&&e<t+n}function sg(e,t){return ag(e,0,t)}function lg(){}function cg(e,t){this._m=e,this._f=1e3*t,this._startTime=0,this._v=0}function ug(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}function dg(e,t,n){this._springX=new ug(e,t,n),this._springY=new ug(e,t,n),this._springScale=new ug(e,t,n),this._startTime=0}lg.prototype.x=function(e){return Math.sqrt(e)},cg.prototype.setV=function(e,t){const n=Math.pow(Math.pow(e,2)+Math.pow(t,2),.5);this._x_v=e,this._y_v=t,this._x_a=-this._f*this._x_v/n,this._y_a=-this._f*this._y_v/n,this._t=Math.abs(e/this._x_a)||Math.abs(t/this._y_a),this._lastDt=null,this._startTime=(new Date).getTime()},cg.prototype.setS=function(e,t){this._x_s=e,this._y_s=t},cg.prototype.s=function(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t,this._lastDt=e);let t=this._x_v*e+.5*this._x_a*Math.pow(e,2)+this._x_s,n=this._y_v*e+.5*this._y_a*Math.pow(e,2)+this._y_s;return(this._x_a>0&&t<this._endPositionX||this._x_a<0&&t>this._endPositionX)&&(t=this._endPositionX),(this._y_a>0&&n<this._endPositionY||this._y_a<0&&n>this._endPositionY)&&(n=this._endPositionY),{x:t,y:n}},cg.prototype.ds=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t),{dx:this._x_v+this._x_a*e,dy:this._y_v+this._y_a*e}},cg.prototype.delta=function(){return{x:-1.5*Math.pow(this._x_v,2)/this._x_a||0,y:-1.5*Math.pow(this._y_v,2)/this._y_a||0}},cg.prototype.dt=function(){return-this._x_v/this._x_a},cg.prototype.done=function(){const e=ag(this.s().x,this._endPositionX)||ag(this.s().y,this._endPositionY)||this._lastDt===this._t;return this._lastDt=null,e},cg.prototype.setEnd=function(e,t){this._endPositionX=e,this._endPositionY=t},cg.prototype.reconfigure=function(e,t){this._m=e,this._f=1e3*t},ug.prototype._solve=function(e,t){const n=this._c,o=this._m,r=this._k,i=n*n-4*o*r;if(0===i){const r=-n/(2*o),i=e,a=t/(r*e);return{x:function(e){return(i+a*e)*Math.pow(Math.E,r*e)},dx:function(e){const t=Math.pow(Math.E,r*e);return r*(i+a*e)*t+a*t}}}if(i>0){const r=(-n-Math.sqrt(i))/(2*o),a=(-n+Math.sqrt(i))/(2*o),s=(t-r*e)/(a-r),l=e-s;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,a*e)),l*t+s*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,a*e)),l*r*t+s*a*n}}}const a=Math.sqrt(4*o*r-n*n)/(2*o),s=-n/2*o,l=e,c=(t-s*e)/a;return{x:function(e){return Math.pow(Math.E,s*e)*(l*Math.cos(a*e)+c*Math.sin(a*e))},dx:function(e){const t=Math.pow(Math.E,s*e),n=Math.cos(a*e),o=Math.sin(a*e);return t*(c*a*n-l*a*o)+s*t*(c*o+l*n)}}},ug.prototype.x=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0},ug.prototype.dx=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0},ug.prototype.setEnd=function(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!sg(t,.1)){t=t||0;let o=this._endPosition;this._solution&&(sg(t,.1)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),sg(t,.1)&&(t=0),sg(o,.1)&&(o=0),o+=this._endPosition),this._solution&&sg(o-e,.1)&&sg(t,.1)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}},ug.prototype.snap=function(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}},ug.prototype.done=function(e){return e||(e=(new Date).getTime()),ag(this.x(),this._endPosition,.1)&&sg(this.dx(),.1)},ug.prototype.reconfigure=function(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())},ug.prototype.springConstant=function(){return this._k},ug.prototype.damping=function(){return this._c},ug.prototype.configuration=function(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]},dg.prototype.setEnd=function(e,t,n,o){const r=(new Date).getTime();this._springX.setEnd(e,o,r),this._springY.setEnd(t,o,r),this._springScale.setEnd(n,o,r),this._startTime=r},dg.prototype.x=function(){const e=((new Date).getTime()-this._startTime)/1e3;return{x:this._springX.x(e),y:this._springY.x(e),scale:this._springScale.x(e)}},dg.prototype.done=function(){const e=(new Date).getTime();return this._springX.done(e)&&this._springY.done(e)&&this._springScale.done(e)},dg.prototype.reconfigure=function(e,t,n){this._springX.reconfigure(e,t,n),this._springY.reconfigure(e,t,n),this._springScale.reconfigure(e,t,n)};function fg(e,t){return+((1e3*e-1e3*t)/1e3).toFixed(1)}const hg=Ru({name:"MovableView",props:{direction:{type:String,default:"none"},inertia:{type:[Boolean,String],default:!1},outOfBounds:{type:[Boolean,String],default:!1},x:{type:[Number,String],default:0},y:{type:[Number,String],default:0},damping:{type:[Number,String],default:20},friction:{type:[Number,String],default:2},disabled:{type:[Boolean,String],default:!1},scale:{type:[Boolean,String],default:!1},scaleMin:{type:[Number,String],default:.5},scaleMax:{type:[Number,String],default:10},scaleValue:{type:[Number,String],default:1},animation:{type:[Boolean,String],default:!0}},emits:["change","scale"],setup(e,{slots:t,emit:n}){const o=An(null),r=Vu(o,n),{setParent:i}=function(e,t,n){const o=To("_isMounted",An(!1)),r=To("addMovableViewContext",(()=>{})),i=To("removeMovableViewContext",(()=>{}));let a,s,l=An(1),c=An(1),u=An(!1),d=An(0),f=An(0),h=null,p=null,g=!1,m=null,v=null;const y=new lg,b=new lg,_={historyX:[0,0],historyY:[0,0],historyT:[0,0]},w=Gi((()=>{let t=Number(e.friction);return isNaN(t)||t<=0?2:t})),x=new cg(1,w.value);Co((()=>e.disabled),(()=>{z()}));const{_updateOldScale:T,_endScale:k,_setScale:S,scaleValueSync:C,_updateBoundary:E,_updateOffset:O,_updateWH:M,_scaleOffset:I,minX:A,minY:P,maxX:$,maxY:L,FAandSFACancel:R,_getLimitXY:j,_setTransform:B,_revise:D,dampingNumber:N,xMove:F,yMove:V,xSync:q,ySync:W,_STD:H}=function(e,t,n,o,r,i,a,s,l,c){const u=Gi((()=>{let t=Number(e.scaleMin);return isNaN(t)?.5:t})),d=Gi((()=>{let t=Number(e.scaleMax);return isNaN(t)?10:t})),f=An(Number(e.scaleValue)||1);Co(f,(e=>{B(e)})),Co(u,(()=>{j()})),Co(d,(()=>{j()})),Co((()=>e.scaleValue),(e=>{f.value=Number(e)||0}));const{_updateBoundary:h,_updateOffset:p,_updateWH:g,_scaleOffset:m,minX:v,minY:y,maxX:b,maxY:_}=function(e,t,n){const o=To("movableAreaWidth",An(0)),r=To("movableAreaHeight",An(0)),i=To("movableAreaRootRef"),a={x:0,y:0},s={x:0,y:0},l=An(0),c=An(0),u=An(0),d=An(0),f=An(0),h=An(0);function p(){let e=0-a.x+s.x,t=o.value-l.value-a.x-s.x;u.value=Math.min(e,t),f.value=Math.max(e,t);let n=0-a.y+s.y,i=r.value-c.value-a.y-s.y;d.value=Math.min(n,i),h.value=Math.max(n,i)}function g(){a.x=mg(e.value,i.value),a.y=vg(e.value,i.value)}function m(o){o=o||t.value,o=n(o);let r=e.value.getBoundingClientRect();c.value=r.height/t.value,l.value=r.width/t.value;let i=c.value*o,a=l.value*o;s.x=(a-l.value)/2,s.y=(i-c.value)/2}return{_updateBoundary:p,_updateOffset:g,_updateWH:m,_scaleOffset:s,minX:u,minY:d,maxX:f,maxY:h}}(t,o,R),{FAandSFACancel:w,_getLimitXY:x,_animationTo:T,_setTransform:k,_revise:S,dampingNumber:C,xMove:E,yMove:O,xSync:M,ySync:I,_STD:A}=function(e,t,n,o,r,i,a,s,l,c,u,d,f,h){const p=Gi((()=>{let e=Number(t.damping);return isNaN(e)?20:e})),g=Gi((()=>"all"===t.direction||"horizontal"===t.direction)),m=Gi((()=>"all"===t.direction||"vertical"===t.direction)),v=An(bg(t.x)),y=An(bg(t.y));Co((()=>t.x),(e=>{v.value=bg(e)})),Co((()=>t.y),(e=>{y.value=bg(e)})),Co(v,(e=>{S(e)})),Co(y,(e=>{C(e)}));const b=new dg(1,9*Math.pow(p.value,2)/40,p.value);function _(e,t){let n=!1;return e>r.value?(e=r.value,n=!0):e<a.value&&(e=a.value,n=!0),t>i.value?(t=i.value,n=!0):t<s.value&&(t=s.value,n=!0),{x:e,y:t,outOfBounds:n}}function w(){d&&d.cancel(),u&&u.cancel()}function x(e,n,r,i,a,s){w(),g.value||(e=l.value),m.value||(n=c.value),t.scale||(r=o.value);let d=_(e,n);e=d.x,n=d.y,t.animation?(b._springX._solution=null,b._springY._solution=null,b._springScale._solution=null,b._springX._endPosition=l.value,b._springY._endPosition=c.value,b._springScale._endPosition=o.value,b.setEnd(e,n,r,1),u=yg(b,(function(){let e=b.x();T(e.x,e.y,e.scale,i,a,s)}),(function(){u.cancel()}))):T(e,n,r,i,a,s)}function T(r,i,a,s="",u,d){null!==r&&"NaN"!==r.toString()&&"number"==typeof r||(r=l.value||0),null!==i&&"NaN"!==i.toString()&&"number"==typeof i||(i=c.value||0),r=Number(r.toFixed(1)),i=Number(i.toFixed(1)),a=Number(a.toFixed(1)),l.value===r&&c.value===i||u||h("change",{},{x:fg(r,n.x),y:fg(i,n.y),source:s}),t.scale||(a=o.value),a=+(a=f(a)).toFixed(3),d&&a!==o.value&&h("scale",{},{x:r,y:i,scale:a});let p="translateX("+r+"px) translateY("+i+"px) translateZ(0px) scale("+a+")";e.value&&(e.value.style.transform=p,e.value.style.webkitTransform=p,l.value=r,c.value=i,o.value=a)}function k(e){let t=_(l.value,c.value),n=t.x,r=t.y,i=t.outOfBounds;return i&&x(n,r,o.value,e),i}function S(e){if(g.value){if(e+n.x===l.value)return l;u&&u.cancel(),x(e+n.x,y.value+n.y,o.value)}return e}function C(e){if(m.value){if(e+n.y===c.value)return c;u&&u.cancel(),x(v.value+n.x,e+n.y,o.value)}return e}return{FAandSFACancel:w,_getLimitXY:_,_animationTo:x,_setTransform:T,_revise:k,dampingNumber:p,xMove:g,yMove:m,xSync:v,ySync:y,_STD:b}}(t,e,m,o,b,_,v,y,a,s,l,c,R,n);function P(t,n){if(e.scale){t=R(t),g(t),h();const e=x(a.value,s.value),o=e.x,r=e.y;n?T(o,r,t,"",!0,!0):gg((function(){k(o,r,t,"",!0,!0)}))}}function $(){i.value=!0}function L(e){r.value=e}function R(e){return e=Math.max(.5,u.value,e),e=Math.min(10,d.value,e)}function j(){if(!e.scale)return!1;P(o.value,!0),L(o.value)}function B(t){return!!e.scale&&(P(t=R(t),!0),L(t),t)}function D(){i.value=!1,L(o.value)}function N(e){e&&(e=r.value*e,$(),P(e))}return{_updateOldScale:L,_endScale:D,_setScale:N,scaleValueSync:f,_updateBoundary:h,_updateOffset:p,_updateWH:g,_scaleOffset:m,minX:v,minY:y,maxX:b,maxY:_,FAandSFACancel:w,_getLimitXY:x,_animationTo:T,_setTransform:k,_revise:S,dampingNumber:C,xMove:E,yMove:O,xSync:M,ySync:I,_STD:A}}(e,n,t,l,c,u,d,f,h,p);function z(){u.value||e.disabled||(R(),_.historyX=[0,0],_.historyY=[0,0],_.historyT=[0,0],F.value&&(a=d.value),V.value&&(s=f.value),n.value.style.willChange="transform",m=null,v=null,g=!0)}function U(t){if(!u.value&&!e.disabled&&g){let n=d.value,o=f.value;if(null===v&&(v=Math.abs(t.detail.dx/t.detail.dy)>1?"htouchmove":"vtouchmove"),F.value&&(n=t.detail.dx+a,_.historyX.shift(),_.historyX.push(n),V.value||null!==m||(m=Math.abs(t.detail.dx/t.detail.dy)<1)),V.value&&(o=t.detail.dy+s,_.historyY.shift(),_.historyY.push(o),F.value||null!==m||(m=Math.abs(t.detail.dy/t.detail.dx)<1)),_.historyT.shift(),_.historyT.push(t.detail.timeStamp),!m){t.preventDefault();let r="touch";n<A.value?e.outOfBounds?(r="touch-out-of-bounds",n=A.value-y.x(A.value-n)):n=A.value:n>$.value&&(e.outOfBounds?(r="touch-out-of-bounds",n=$.value+y.x(n-$.value)):n=$.value),o<P.value?e.outOfBounds?(r="touch-out-of-bounds",o=P.value-b.x(P.value-o)):o=P.value:o>L.value&&(e.outOfBounds?(r="touch-out-of-bounds",o=L.value+b.x(o-L.value)):o=L.value),gg((function(){B(n,o,l.value,r)}))}}}function Y(){if(!u.value&&!e.disabled&&g&&(n.value.style.willChange="auto",g=!1,!m&&!D("out-of-bounds")&&e.inertia)){const e=1e3*(_.historyX[1]-_.historyX[0])/(_.historyT[1]-_.historyT[0]),t=1e3*(_.historyY[1]-_.historyY[0])/(_.historyT[1]-_.historyT[0]),n=d.value,o=f.value;x.setV(e,t),x.setS(n,o);const r=x.delta().x,i=x.delta().y;let a=r+n,s=i+o;a<A.value?(a=A.value,s=o+(A.value-n)*i/r):a>$.value&&(a=$.value,s=o+($.value-n)*i/r),s<P.value?(s=P.value,a=n+(P.value-o)*r/i):s>L.value&&(s=L.value,a=n+(L.value-o)*r/i),x.setEnd(a,s),p=yg(x,(function(){let e=x.s(),t=e.x,n=e.y;B(t,n,l.value,"friction")}),(function(){p.cancel()}))}e.outOfBounds||e.inertia||R()}function X(){if(!o.value)return;R();let t=e.scale?C.value:1;O(),M(t),E();let n=j(q.value+I.x,W.value+I.y),r=n.x,i=n.y;B(r,i,t,"",!0),T(t)}return lr((()=>{ig(n.value,(e=>{switch(e.detail.state){case"start":z();break;case"move":U(e);break;case"end":Y()}})),X(),x.reconfigure(1,w.value),H.reconfigure(1,9*Math.pow(N.value,2)/40,N.value),n.value.style.transformOrigin="center";const e={rootRef:n,setParent:X,_endScale:k,_setScale:S};r(e),fr((()=>{i(e)}))})),fr((()=>{R()})),{setParent:X}}(e,r,o);return()=>Ei("uni-movable-view",{ref:o},[Ei(Qh,{onResize:i},null,8,["onResize"]),t.default&&t.default()],512)}});let pg=!1;function gg(e){pg||(pg=!0,requestAnimationFrame((function(){e(),pg=!1})))}function mg(e,t){if(e===t)return 0;let n=e.offsetLeft;return e.offsetParent?n+=mg(e.offsetParent,t):0}function vg(e,t){if(e===t)return 0;let n=e.offsetTop;return e.offsetParent?n+=vg(e.offsetParent,t):0}function yg(e,t,n){let o={id:0,cancelled:!1};return function e(t,n,o,r){if(!t||!t.cancelled){o(n);let i=n.done();i||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,o,r))),i&&r&&r(n)}}(o,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,o),model:e}}function bg(e){return/\d+[ur]px$/i.test(e)?of(parseFloat(e)):Number(e)||0}const _g=["navigate","redirect","switchTab","reLaunch","navigateBack"],wg=["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"],xg=["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"],Tg={hoverClass:{type:String,default:"navigator-hover"},url:{type:String,default:""},openType:{type:String,default:"navigate",validator:e=>Boolean(~_g.indexOf(e))},delta:{type:Number,default:1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:600},exists:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},animationType:{type:String,default:"",validator:e=>!e||wg.concat(xg).includes(e)},animationDuration:{type:[String,Number],default:300}};const kg=Ru({name:"Navigator",inheritAttrs:!1,compatConfig:{MODE:3},props:x({},Tg,{renderLink:{type:Boolean,default:!0}}),setup(e,{slots:t}){const n=Ni(),o=n&&n.vnode.scopeId||"",{hovering:r,binding:i}=Du(e),a=function(e){return()=>{if("navigateBack"!==e.openType&&!e.url)return void console.error("<navigator/> should have url attribute when using navigateTo, redirectTo, reLaunch or switchTab");const t=parseInt(e.animationDuration);switch(e.openType){case"navigate":by({url:e.url,animationType:e.animationType||"pop-in",animationDuration:t});break;case"redirect":_y({url:e.url,exists:e.exists});break;case"switchTab":Ty({url:e.url});break;case"reLaunch":wy({url:e.url});break;case"navigateBack":vy({delta:e.delta,animationType:e.animationType||"pop-out",animationDuration:t})}}}(e);return()=>{const{hoverClass:s,url:l}=e,c=e.hoverClass&&"none"!==e.hoverClass,u=Ei("uni-navigator",Li({class:c&&r.value?s:""},c&&i,n?n.attrs:{},{[o]:""},{onClick:a}),[t.default&&t.default()],16,["onClick"]);return e.renderLink?Ei("a",{class:"navigator-wrap",href:l,onClick:Rc,onMousedown:Rc},[u],40,["href","onClick","onMousedown"]):u}}});const Sg=Ru({name:"PickerView",props:{value:{type:Array,default:()=>[],validator:function(e){return C(e)&&e.filter((e=>"number"==typeof e)).length===e.length}},indicatorStyle:{type:String,default:""},indicatorClass:{type:String,default:""},maskStyle:{type:String,default:""},maskClass:{type:String,default:""}},emits:["change","pickstart","pickend","update:value"],setup(e,{slots:t,emit:n}){const o=An(null),r=An(null),i=Vu(o,n),a=function(e){const t=mn([...e.value]),n=mn({value:t,height:34});return Co((()=>e.value),((e,t)=>{n.value.length=e.length,e.forEach(((e,t)=>{e!==n.value[t]&&n.value.splice(t,1,e)}))})),n}(e),s=An(null);lr((()=>{const e=s.value;a.height=e.$el.offsetHeight}));let l=An([]),c=An([]);function u(e){let t=c.value;t=t.filter((e=>e.type!==di));let n=t.indexOf(e);return-1!==n?n:l.value.indexOf(e)}return xo("getPickerViewColumn",(function(e){return Gi({get(){const t=u(e.vnode);return a.value[t]||0},set(t){const o=u(e.vnode);if(o<0)return;if(a.value[o]!==t){a.value[o]=t;const e=a.value.map((e=>e));n("update:value",e),i("change",{},{value:e})}}})})),xo("pickerViewProps",e),xo("pickerViewState",a),()=>{const e=t.default&&t.default();{const t=Qp(e);l.value=t,eo((()=>{c.value=t}))}return Ei("uni-picker-view",{ref:o},[Ei(Qh,{ref:s,onResize:({height:e})=>a.height=e},null,8,["onResize"]),Ei("div",{ref:r,class:"uni-picker-view-wrapper"},[e],512)],512)}}});class Cg{constructor(e){this._drag=e,this._dragLog=Math.log(e),this._x=0,this._v=0,this._startTime=0}set(e,t){this._x=e,this._v=t,this._startTime=(new Date).getTime()}setVelocityByEnd(e){this._v=(e-this._x)*this._dragLog/(Math.pow(this._drag,100)-1)}x(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._x+this._v*t/this._dragLog-this._v/this._dragLog}dx(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);const t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._v*t}done(){return Math.abs(this.dx())<3}reconfigure(e){const t=this.x(),n=this.dx();this._drag=e,this._dragLog=Math.log(e),this.set(t,n)}configuration(){const e=this;return[{label:"Friction",read:function(){return e._drag},write:function(t){e.reconfigure(t)},min:.001,max:.1,step:.001}]}}function Eg(e,t,n){return e>t-n&&e<t+n}function Og(e,t){return Eg(e,0,t)}class Mg{constructor(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}_solve(e,t){const n=this._c,o=this._m,r=this._k,i=n*n-4*o*r;if(0===i){const r=-n/(2*o),i=e,a=t/(r*e);return{x:function(e){return(i+a*e)*Math.pow(Math.E,r*e)},dx:function(e){const t=Math.pow(Math.E,r*e);return r*(i+a*e)*t+a*t}}}if(i>0){const r=(-n-Math.sqrt(i))/(2*o),a=(-n+Math.sqrt(i))/(2*o),s=(t-r*e)/(a-r),l=e-s;return{x:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,a*e)),l*t+s*n},dx:function(e){let t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,r*e)),n||(n=this._powER2T=Math.pow(Math.E,a*e)),l*r*t+s*a*n}}}const a=Math.sqrt(4*o*r-n*n)/(2*o),s=-n/2*o,l=e,c=(t-s*e)/a;return{x:function(e){return Math.pow(Math.E,s*e)*(l*Math.cos(a*e)+c*Math.sin(a*e))},dx:function(e){const t=Math.pow(Math.E,s*e),n=Math.cos(a*e),o=Math.sin(a*e);return t*(c*a*n-l*a*o)+s*t*(c*o+l*n)}}}x(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0}dx(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0}setEnd(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!Og(t,.4)){t=t||0;let o=this._endPosition;this._solution&&(Og(t,.4)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),Og(t,.4)&&(t=0),Og(o,.4)&&(o=0),o+=this._endPosition),this._solution&&Og(o-e,.4)&&Og(t,.4)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}}snap(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}}done(e){return e||(e=(new Date).getTime()),Eg(this.x(),this._endPosition,.4)&&Og(this.dx(),.4)}reconfigure(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())}springConstant(){return this._k}damping(){return this._c}configuration(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]}}class Ig{constructor(e,t,n){this._extent=e,this._friction=t||new Cg(.01),this._spring=n||new Mg(1,90,20),this._startTime=0,this._springing=!1,this._springOffset=0}snap(e,t){this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(t)}set(e,t){this._friction.set(e,t),e>0&&t>=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(0)):e<-this._extent&&t<=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(-this._extent)):this._springing=!1,this._startTime=(new Date).getTime()}x(e){if(!this._startTime)return 0;if(e||(e=((new Date).getTime()-this._startTime)/1e3),this._springing)return this._spring.x()+this._springOffset;let t=this._friction.x(e),n=this.dx(e);return(t>0&&n>=0||t<-this._extent&&n<=0)&&(this._springing=!0,this._spring.setEnd(0,n),t<-this._extent?this._springOffset=-this._extent:this._springOffset=0,t=this._spring.x()+this._springOffset),t}dx(e){let t;return t=this._lastTime===e?this._lastDx:this._springing?this._spring.dx(e):this._friction.dx(e),this._lastTime=e,this._lastDx=t,t}done(){return this._springing?this._spring.done():this._friction.done()}setVelocityByEnd(e){this._friction.setVelocityByEnd(e)}configuration(){const e=this._friction.configuration();return e.push.apply(e,this._spring.configuration()),e}}class Ag{constructor(e,t){t=t||{},this._element=e,this._options=t,this._enableSnap=t.enableSnap||!1,this._itemSize=t.itemSize||0,this._enableX=t.enableX||!1,this._enableY=t.enableY||!1,this._shouldDispatchScrollEvent=!!t.onScroll,this._enableX?(this._extent=(t.scrollWidth||this._element.offsetWidth)-this._element.parentElement.offsetWidth,this._scrollWidth=t.scrollWidth):(this._extent=(t.scrollHeight||this._element.offsetHeight)-this._element.parentElement.offsetHeight,this._scrollHeight=t.scrollHeight),this._position=0,this._scroll=new Ig(this._extent,t.friction,t.spring),this._onTransitionEnd=this.onTransitionEnd.bind(this),this.updatePosition()}onTouchStart(){this._startPosition=this._position,this._lastChangePos=this._startPosition,this._startPosition>0?this._startPosition/=.5:this._startPosition<-this._extent&&(this._startPosition=(this._startPosition+this._extent)/.5-this._extent),this._animation&&(this._animation.cancel(),this._scrolling=!1),this.updatePosition()}onTouchMove(e,t){let n=this._startPosition;this._enableX?n+=e:this._enableY&&(n+=t),n>0?n*=.5:n<-this._extent&&(n=.5*(n+this._extent)-this._extent),this._position=n,this.updatePosition(),this.dispatchScroll()}onTouchEnd(e,t,n){if(this._enableSnap&&this._position>-this._extent&&this._position<0){if(this._enableY&&(Math.abs(t)<this._itemSize&&Math.abs(n.y)<300||Math.abs(n.y)<150))return void this.snap();if(this._enableX&&(Math.abs(e)<this._itemSize&&Math.abs(n.x)<300||Math.abs(n.x)<150))return void this.snap()}let o;if(this._enableX?this._scroll.set(this._position,n.x):this._enableY&&this._scroll.set(this._position,n.y),this._enableSnap){const e=this._scroll._friction.x(100),t=e%this._itemSize;o=Math.abs(t)>this._itemSize/2?e-(this._itemSize-Math.abs(t)):e-t,o<=0&&o>=-this._extent&&this._scroll.setVelocityByEnd(o)}this._lastTime=Date.now(),this._lastDelay=0,this._scrolling=!0,this._lastChangePos=this._position,this._lastIdx=Math.floor(Math.abs(this._position/this._itemSize)),this._animation=function(e,t,n){const o={id:0,cancelled:!1};return function e(t,n,o,r){if(!t||!t.cancelled){o(n);const i=n.done();i||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,o,r))),i&&r&&r(n)}}(o,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,o),model:e}}(this._scroll,(()=>{const e=Date.now(),t=(e-this._scroll._startTime)/1e3,n=this._scroll.x(t);this._position=n,this.updatePosition();const o=this._scroll.dx(t);this._shouldDispatchScrollEvent&&e-this._lastTime>this._lastDelay&&(this.dispatchScroll(),this._lastDelay=Math.abs(2e3/o),this._lastTime=e)}),(()=>{this._enableSnap&&(o<=0&&o>=-this._extent&&(this._position=o,this.updatePosition()),M(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._shouldDispatchScrollEvent&&this.dispatchScroll(),this._scrolling=!1}))}onTransitionEnd(){this._element.style.webkitTransition="",this._element.style.transition="",this._element.removeEventListener("transitionend",this._onTransitionEnd),this._snapping&&(this._snapping=!1),this.dispatchScroll()}snap(){const e=this._itemSize,t=this._position%e,n=Math.abs(t)>this._itemSize/2?this._position-(e-Math.abs(t)):this._position-t;this._position!==n&&(this._snapping=!0,this.scrollTo(-n),M(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize)))}scrollTo(e,t){this._animation&&(this._animation.cancel(),this._scrolling=!1),"number"==typeof e&&(this._position=-e),this._position<-this._extent?this._position=-this._extent:this._position>0&&(this._position=0);const n="transform "+(t||.2)+"s ease-out";this._element.style.webkitTransition="-webkit-"+n,this._element.style.transition=n,this.updatePosition(),this._element.addEventListener("transitionend",this._onTransitionEnd)}dispatchScroll(){if(M(this._options.onScroll)&&Math.round(Number(this._lastPos))!==Math.round(this._position)){this._lastPos=this._position;const e={target:{scrollLeft:this._enableX?-this._position:0,scrollTop:this._enableY?-this._position:0,scrollHeight:this._scrollHeight||this._element.offsetHeight,scrollWidth:this._scrollWidth||this._element.offsetWidth,offsetHeight:this._element.parentElement.offsetHeight,offsetWidth:this._element.parentElement.offsetWidth}};this._options.onScroll(e)}}update(e,t,n){let o=0;const r=this._position;this._enableX?(o=this._element.childNodes.length?(t||this._element.offsetWidth)-this._element.parentElement.offsetWidth:0,this._scrollWidth=t):(o=this._element.childNodes.length?(t||this._element.offsetHeight)-this._element.parentElement.offsetHeight:0,this._scrollHeight=t),"number"==typeof e&&(this._position=-e),this._position<-o?this._position=-o:this._position>0&&(this._position=0),this._itemSize=n||this._itemSize,this.updatePosition(),r!==this._position&&(this.dispatchScroll(),M(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._extent=o,this._scroll._extent=o}updatePosition(){let e="";this._enableX?e="translateX("+this._position+"px) translateZ(0)":this._enableY&&(e="translateY("+this._position+"px) translateZ(0)"),this._element.style.webkitTransform=e,this._element.style.transform=e}isScrolling(){return this._scrolling||this._snapping}}function Pg(e,t){const n={trackingID:-1,maxDy:0,maxDx:0},o=new Ag(e,t);function r(e){const t=e,o=e;return"move"===t.detail.state||"end"===t.detail.state?{x:t.detail.dx,y:t.detail.dy}:{x:o.screenX-n.x,y:o.screenY-n.y}}return{scroller:o,handleTouchStart:function(e){const t=e,r=e;"start"===t.detail.state?(n.trackingID="touch",n.x=t.detail.x,n.y=t.detail.y):(n.trackingID="mouse",n.x=r.screenX,n.y=r.screenY),n.maxDx=0,n.maxDy=0,n.historyX=[0],n.historyY=[0],n.historyTime=[t.detail.timeStamp||r.timeStamp],n.listener=o,o.onTouchStart&&o.onTouchStart(),("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault()},handleTouchMove:function(e){const t=e,o=e;if(-1!==n.trackingID){("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault();const i=r(e);if(i){for(n.maxDy=Math.max(n.maxDy,Math.abs(i.y)),n.maxDx=Math.max(n.maxDx,Math.abs(i.x)),n.historyX.push(i.x),n.historyY.push(i.y),n.historyTime.push(t.detail.timeStamp||o.timeStamp);n.historyTime.length>10;)n.historyTime.shift(),n.historyX.shift(),n.historyY.shift();n.listener&&n.listener.onTouchMove&&n.listener.onTouchMove(i.x,i.y)}}},handleTouchEnd:function(e){if(-1!==n.trackingID){e.preventDefault();const t=r(e);if(t){const e=n.listener;n.trackingID=-1,n.listener=null;const o={x:0,y:0};if(n.historyTime.length>2)for(let t=n.historyTime.length-1,r=n.historyTime[t],i=n.historyX[t],a=n.historyY[t];t>0;){t--;const e=r-n.historyTime[t];if(e>30&&e<50){o.x=(i-n.historyX[t])/(e/1e3),o.y=(a-n.historyY[t])/(e/1e3);break}}n.historyTime=[],n.historyX=[],n.historyY=[],e&&e.onTouchEnd&&e.onTouchEnd(t.x,t.y,o)}}}}}let $g=0;const Lg=Ru({name:"PickerViewColumn",setup(e,{slots:t,emit:n}){const o=An(null),r=An(null),i=To("getPickerViewColumn"),a=Ni(),s=i?i(a):An(0),l=To("pickerViewProps"),c=To("pickerViewState"),u=An(34),d=An(null);lr((()=>{const e=d.value;u.value=e.$el.offsetHeight}));const f=Gi((()=>(c.height-u.value)/2)),{state:h}=Fp(),p=function(e){const t="uni-picker-view-content-"+$g++;return Co((()=>e.value),(function(){const n=document.createElement("style");n.innerText=`.uni-picker-view-content.${t}>*{height: ${e.value}px;overflow: hidden;}`,document.head.appendChild(n)})),t}(u);let g;const m=mn({current:s.value,length:0});let v;function y(){g&&!v&&(v=!0,eo((()=>{v=!1;let e=Math.min(m.current,m.length-1);e=Math.max(e,0),g.update(e*u.value,void 0,u.value)})))}Co((()=>s.value),(e=>{e!==m.current&&(m.current=e,y())})),Co((()=>m.current),(e=>s.value=e)),Co([()=>u.value,()=>m.length,()=>c.height],y);let b=0;function _(e){const t=b+e.deltaY;if(Math.abs(t)>10){b=0;let e=Math.min(m.current+(t<0?-1:1),m.length-1);m.current=e=Math.max(e,0),g.scrollTo(e*u.value)}else b=t;e.preventDefault()}function w({clientY:e}){const t=o.value;if(!g.isScrolling()){const n=e-t.getBoundingClientRect().top-c.height/2,o=u.value/2;if(!(Math.abs(n)<=o)){const e=Math.ceil((Math.abs(n)-o)/u.value),t=n<0?-e:e;let r=Math.min(m.current+t,m.length-1);m.current=r=Math.max(r,0),g.scrollTo(r*u.value)}}}const x=()=>{const e=o.value,t=r.value,{scroller:n,handleTouchStart:i,handleTouchMove:a,handleTouchEnd:s}=Pg(t,{enableY:!0,enableX:!1,enableSnap:!0,itemSize:u.value,friction:new Cg(1e-4),spring:new Mg(2,90,20),onSnap:e=>{isNaN(e)||e===m.current||(m.current=e)}});g=n,ig(e,(e=>{switch(e.detail.state){case"start":i(e);break;case"move":a(e),e.stopPropagation();break;case"end":case"cancel":s(e)}}),!0),function(e){let t=0,n=0;e.addEventListener("touchstart",(e=>{const o=e.changedTouches[0];t=o.clientX,n=o.clientY})),e.addEventListener("touchend",(e=>{const o=e.changedTouches[0];if(Math.abs(o.clientX-t)<20&&Math.abs(o.clientY-n)<20){const t={bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget},n=new CustomEvent("click",t);["screenX","screenY","clientX","clientY","pageX","pageY"].forEach((e=>{n[e]=o[e]})),e.target.dispatchEvent(n)}}))}(e),y()};return lr(x),()=>{const e=t.default&&t.default();m.length=Qp(e).length;const n=`${f.value}px 0`;return Ei("uni-picker-view-column",{ref:o},[Ei("div",{onWheel:_,onClick:w,class:"uni-picker-view-group"},[Ei("div",Li(h.attrs,{class:["uni-picker-view-mask",l.maskClass],style:`background-size: 100% ${f.value}px;${l.maskStyle}`}),null,16),Ei("div",Li(h.attrs,{class:["uni-picker-view-indicator",l.indicatorClass],style:l.indicatorStyle}),[Ei(Qh,{ref:d,onResize:({height:e})=>u.value=e},null,8,["onResize"])],16),Ei("div",{ref:r,class:["uni-picker-view-content",p],style:{padding:n}},[e],6)],40,["onWheel","onClick"])],512)}}}),Rg=qc("ucg"),jg=Ru({name:"RadioGroup",props:{name:{type:String,default:""}},setup(e,{emit:t,slots:n}){const o=An(null);return function(e,t){const n=[];lr((()=>{s(n.length-1)}));const o=()=>{var e;return null==(e=n.find((e=>e.value.radioChecked)))?void 0:e.value.value};xo(Rg,{addField(e){n.push(e)},removeField(e){n.splice(n.indexOf(e),1)},radioChange(e,r){s(n.indexOf(r),!0),t("change",e,{value:o()})}});const r=To(qu,!1),i={submit:()=>{let t=["",null];return""!==e.name&&(t[0]=e.name,t[1]=o()),t}};r&&(r.addField(i),dr((()=>{r.removeField(i)})));function a(e,t){e.value={radioChecked:t,value:e.value.value}}function s(e,t){n.forEach(((o,r)=>{r!==e&&(t?a(n[r],!1):n.forEach(((e,t)=>{r>=t||n[t].value.radioChecked&&a(n[r],!1)})))}))}}(e,Vu(o,t)),()=>Ei("uni-radio-group",{ref:o},[n.default&&n.default()],512)}});const Bg=Ru({name:"Radio",props:{checked:{type:[Boolean,String],default:!1},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},color:{type:String,default:"#007aff"},value:{type:String,default:""}},setup(e,{slots:t}){const n=An(e.checked),o=An(e.value),r=Gi((()=>e.disabled?"background-color: #E1E1E1;border-color: ##D1D1D1;":`background-color: ${e.color};border-color: ${e.color};`));Co([()=>e.checked,()=>e.value],(([e,t])=>{n.value=e,o.value=t}));const{uniCheckGroup:i,uniLabel:a,field:s}=function(e,t,n){const o=Gi({get:()=>({radioChecked:Boolean(e.value),value:t.value}),set:({radioChecked:t})=>{e.value=t}}),r={reset:n},i=To(Rg,!1);i&&i.addField(o);const a=To(qu,!1);a&&a.addField(r);const s=To(zu,!1);return dr((()=>{i&&i.removeField(o),a&&a.removeField(r)})),{uniCheckGroup:i,uniForm:a,uniLabel:s,field:o}}(n,o,(()=>{n.value=!1})),l=t=>{e.disabled||(n.value=!0,i&&i.radioChange(t,s),t.stopPropagation())};return a&&(a.addHandler(l),dr((()=>{a.removeHandler(l)}))),Yu(e,{"label-click":l}),()=>{const o=Nu(e,"disabled");return Ei("uni-radio",Li(o,{onClick:l}),[Ei("div",{class:"uni-radio-wrapper"},[Ei("div",{class:["uni-radio-input",{"uni-radio-input-disabled":e.disabled}],style:n.value?r.value:""},[n.value?Yc(zc,e.disabled?"#ADADAD":"#fff",18):""],6),t.default&&t.default()])],16,["onClick"])}}});const Dg={a:"",abbr:"",address:"",article:"",aside:"",b:"",bdi:"",bdo:["dir"],big:"",blockquote:"",br:"",caption:"",center:"",cite:"",code:"",col:["span","width"],colgroup:["span","width"],dd:"",del:"",div:"",dl:"",dt:"",em:"",fieldset:"",font:"",footer:"",h1:"",h2:"",h3:"",h4:"",h5:"",h6:"",header:"",hr:"",i:"",img:["alt","src","height","width"],ins:"",label:"",legend:"",li:"",mark:"",nav:"",ol:["start","type"],p:"",pre:"",q:"",rt:"",ruby:"",s:"",section:"",small:"",span:"",strong:"",sub:"",sup:"",table:["width"],tbody:"",td:["colspan","height","rowspan","width"],tfoot:"",th:["colspan","height","rowspan","width"],thead:"",tr:["colspan","height","rowspan","width"],tt:"",u:"",ul:""},Ng={amp:"&",gt:">",lt:"<",nbsp:" ",quot:'"',apos:"'",ldquo:"“",rdquo:"”",yen:"￥",radic:"√",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",hellip:"…"};const Fg=(e,t,n)=>!n||C(n)&&!n.length?[]:n.map((n=>{if(j(n)){if(!S(n,"type")||"node"===n.type){let o={[e]:""};const r=n.name.toLowerCase();if(!S(Dg,r))return;return function(e,t){if(j(t))for(const n in t)if(S(t,n)){const o=t[n];"img"===e&&"src"===n&&(t[n]=Qu(o))}}(r,n.attrs),o=x(o,function(e,t){if(["a","img"].includes(e.name)&&t)return{onClick:n=>{t(n,{node:e}),n.stopPropagation(),n.preventDefault(),n.returnValue=!1}}}(n,t),n.attrs),Ji(n.name,o,Fg(e,t,n.children))}return"text"===n.type&&I(n.text)&&""!==n.text?Mi((n.text||"").replace(/&(([a-zA-Z]+)|(#x{0,1}[\da-zA-Z]+));/gi,(function(e,t){return S(Ng,t)&&Ng[t]?Ng[t]:/^#[0-9]{1,4}$/.test(t)?String.fromCharCode(t.slice(1)):/^#x[0-9a-f]{1,4}$/i.test(t)?String.fromCharCode(0+t.slice(1)):e}))):void 0}}));function Vg(e){e=function(e){return e.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}(e);const t=[],n={node:"root",children:[]};return function(e,t){var n,o,r,i=[],a=e;for(i.last=function(){return this[this.length-1]};e;){if(o=!0,i.last()&&Sp[i.last()])e=e.replace(new RegExp("([\\s\\S]*?)</"+i.last()+"[^>]*>"),(function(e,n){return n=n.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),t.chars&&t.chars(n),""})),c("",i.last());else if(0==e.indexOf("\x3c!--")?(n=e.indexOf("--\x3e"))>=0&&(t.comment&&t.comment(e.substring(4,n)),e=e.substring(n+3),o=!1):0==e.indexOf("</")?(r=e.match(yp))&&(e=e.substring(r[0].length),r[0].replace(yp,c),o=!1):0==e.indexOf("<")&&(r=e.match(vp))&&(e=e.substring(r[0].length),r[0].replace(vp,l),o=!1),o){var s=(n=e.indexOf("<"))<0?e:e.substring(0,n);e=n<0?"":e.substring(n),t.chars&&t.chars(s)}if(e==a)throw"Parse Error: "+e;a=e}function l(e,n,o,r){if(n=n.toLowerCase(),wp[n])for(;i.last()&&xp[i.last()];)c("",i.last());if(Tp[n]&&i.last()==n&&c("",n),(r=_p[n]||!!r)||i.push(n),t.start){var a=[];o.replace(bp,(function(e,t){var n=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:kp[t]?t:"";a.push({name:t,value:n,escaped:n.replace(/(^|[^\\])"/g,'$1\\"')})})),t.start&&t.start(n,a,r)}}function c(e,n){if(n)for(o=i.length-1;o>=0&&i[o]!=n;o--);else var o=0;if(o>=0){for(var r=i.length-1;r>=o;r--)t.end&&t.end(i[r]);i.length=o}}c()}(e,{start:function(e,o,r){const i={name:e};if(0!==o.length&&(i.attrs=function(e){return e.reduce((function(e,t){let n=t.value;const o=t.name;return n.match(/ /)&&-1===["style","src"].indexOf(o)&&(n=n.split(" ")),e[o]?Array.isArray(e[o])?e[o].push(n):e[o]=[e[o],n]:e[o]=n,e}),{})}(o)),r){const e=t[0]||n;e.children||(e.children=[]),e.children.push(i)}else t.unshift(i)},end:function(e){const o=t.shift();if(o.name!==e&&console.error("invalid state: mismatch end tag"),0===t.length)n.children.push(o);else{const e=t[0];e.children||(e.children=[]),e.children.push(o)}},chars:function(e){const o={type:"text",text:e};if(0===t.length)n.children.push(o);else{const e=t[0];e.children||(e.children=[]),e.children.push(o)}},comment:function(e){const n={node:"comment",text:e},o=t[0];o.children||(o.children=[]),o.children.push(n)}}),n.children}const qg=Ru({name:"RichText",compatConfig:{MODE:3},props:{nodes:{type:[Array,String],default:function(){return[]}}},emits:["click","touchstart","touchmove","touchcancel","touchend","longpress","itemclick"],setup(e,{emit:t}){const n=Ni(),o=n&&n.vnode.scopeId||"",r=An(null),i=An([]),a=Vu(r,t);function s(e,t={}){a("itemclick",e,t)}return Co((()=>e.nodes),(function(){let t=e.nodes;I(t)&&(t=Vg(e.nodes)),i.value=Fg(o,s,t)}),{immediate:!0}),()=>Ji("uni-rich-text",{ref:r},Ji("div",{},i.value))}}),Wg=He(!0),Hg=Ru({name:"ScrollView",compatConfig:{MODE:3},props:{scrollX:{type:[Boolean,String],default:!1},scrollY:{type:[Boolean,String],default:!1},upperThreshold:{type:[Number,String],default:50},lowerThreshold:{type:[Number,String],default:50},scrollTop:{type:[Number,String],default:0},scrollLeft:{type:[Number,String],default:0},scrollIntoView:{type:String,default:""},scrollWithAnimation:{type:[Boolean,String],default:!1},enableBackToTop:{type:[Boolean,String],default:!1},refresherEnabled:{type:[Boolean,String],default:!1},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"back"},refresherBackground:{type:String,default:"#fff"},refresherTriggered:{type:[Boolean,String],default:!1}},emits:["scroll","scrolltoupper","scrolltolower","refresherrefresh","refresherrestore","refresherpulling","refresherabort","update:refresherTriggered"],setup(e,{emit:t,slots:n}){const o=An(null),r=An(null),i=An(null),a=An(null),s=An(null),l=Vu(o,t),{state:c,scrollTopNumber:u,scrollLeftNumber:d}=function(e){const t=Gi((()=>Number(e.scrollTop)||0)),n=Gi((()=>Number(e.scrollLeft)||0)),o=mn({lastScrollTop:t.value,lastScrollLeft:n.value,lastScrollToUpperTime:0,lastScrollToLowerTime:0,refresherHeight:0,refreshRotate:0,refreshState:""});return{state:o,scrollTopNumber:t,scrollLeftNumber:n}}(e);!function(e,t,n,o,r,i,a,s,l){let c=!1,u=0,d=!1,f=()=>{};const h=Gi((()=>{let t=Number(e.upperThreshold);return isNaN(t)?50:t})),p=Gi((()=>{let t=Number(e.lowerThreshold);return isNaN(t)?50:t}));function g(e,t){const n=a.value;let o=0,r="";if(e<0?e=0:"x"===t&&e>n.scrollWidth-n.offsetWidth?e=n.scrollWidth-n.offsetWidth:"y"===t&&e>n.scrollHeight-n.offsetHeight&&(e=n.scrollHeight-n.offsetHeight),"x"===t?o=n.scrollLeft-e:"y"===t&&(o=n.scrollTop-e),0===o)return;let i=s.value;i.style.transition="transform .3s ease-out",i.style.webkitTransition="-webkit-transform .3s ease-out","x"===t?r="translateX("+o+"px) translateZ(0)":"y"===t&&(r="translateY("+o+"px) translateZ(0)"),i.removeEventListener("transitionend",f),i.removeEventListener("webkitTransitionEnd",f),f=()=>_(e,t),i.addEventListener("transitionend",f),i.addEventListener("webkitTransitionEnd",f),"x"===t?n.style.overflowX="hidden":"y"===t&&(n.style.overflowY="hidden"),i.style.transform=r,i.style.webkitTransform=r}function m(n){const o=n.target;r("scroll",n,{scrollLeft:o.scrollLeft,scrollTop:o.scrollTop,scrollHeight:o.scrollHeight,scrollWidth:o.scrollWidth,deltaX:t.lastScrollLeft-o.scrollLeft,deltaY:t.lastScrollTop-o.scrollTop}),e.scrollY&&(o.scrollTop<=h.value&&t.lastScrollTop-o.scrollTop>0&&n.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",n,{direction:"top"}),t.lastScrollToUpperTime=n.timeStamp),o.scrollTop+o.offsetHeight+p.value>=o.scrollHeight&&t.lastScrollTop-o.scrollTop<0&&n.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",n,{direction:"bottom"}),t.lastScrollToLowerTime=n.timeStamp)),e.scrollX&&(o.scrollLeft<=h.value&&t.lastScrollLeft-o.scrollLeft>0&&n.timeStamp-t.lastScrollToUpperTime>200&&(r("scrolltoupper",n,{direction:"left"}),t.lastScrollToUpperTime=n.timeStamp),o.scrollLeft+o.offsetWidth+p.value>=o.scrollWidth&&t.lastScrollLeft-o.scrollLeft<0&&n.timeStamp-t.lastScrollToLowerTime>200&&(r("scrolltolower",n,{direction:"right"}),t.lastScrollToLowerTime=n.timeStamp)),t.lastScrollTop=o.scrollTop,t.lastScrollLeft=o.scrollLeft}function v(t){e.scrollY&&(e.scrollWithAnimation?g(t,"y"):a.value.scrollTop=t)}function y(t){e.scrollX&&(e.scrollWithAnimation?g(t,"x"):a.value.scrollLeft=t)}function b(t){if(t){if(!/^[_a-zA-Z][-_a-zA-Z0-9:]*$/.test(t))return void console.error(`id error: scroll-into-view=${t}`);let n=i.value.querySelector("#"+t);if(n){let t=a.value.getBoundingClientRect(),o=n.getBoundingClientRect();if(e.scrollX){let n=o.left-t.left,r=a.value.scrollLeft+n;e.scrollWithAnimation?g(r,"x"):a.value.scrollLeft=r}if(e.scrollY){let n=o.top-t.top,r=a.value.scrollTop+n;e.scrollWithAnimation?g(r,"y"):a.value.scrollTop=r}}}}function _(t,n){s.value.style.transition="",s.value.style.webkitTransition="",s.value.style.transform="",s.value.style.webkitTransform="";let o=a.value;"x"===n?(o.style.overflowX=e.scrollX?"auto":"hidden",o.scrollLeft=t):"y"===n&&(o.style.overflowY=e.scrollY?"auto":"hidden",o.scrollTop=t),s.value.removeEventListener("transitionend",f),s.value.removeEventListener("webkitTransitionEnd",f)}function w(n){if(e.refresherEnabled){switch(n){case"refreshing":t.refresherHeight=e.refresherThreshold,c||(c=!0,r("refresherrefresh",{},{}),l("update:refresherTriggered",!0));break;case"restore":case"refresherabort":c=!1,t.refresherHeight=u=0,"restore"===n&&(d=!1,r("refresherrestore",{},{})),"refresherabort"===n&&d&&(d=!1,r("refresherabort",{},{}))}t.refreshState=n}}lr((()=>{eo((()=>{v(n.value),y(o.value)})),b(e.scrollIntoView);let i=function(e){e.preventDefault(),e.stopPropagation(),m(e)},s={x:0,y:0},l=null,f=function(n){if(null===s)return;let o=n.touches[0].pageX,i=n.touches[0].pageY,f=a.value;if(Math.abs(o-s.x)>Math.abs(i-s.y))if(e.scrollX){if(0===f.scrollLeft&&o>s.x)return void(l=!1);if(f.scrollWidth===f.offsetWidth+f.scrollLeft&&o<s.x)return void(l=!1);l=!0}else l=!1;else if(e.scrollY)if(0===f.scrollTop&&i>s.y)l=!1,e.refresherEnabled&&!1!==n.cancelable&&n.preventDefault();else{if(f.scrollHeight===f.offsetHeight+f.scrollTop&&i<s.y)return void(l=!1);l=!0}else l=!1;if(l&&n.stopPropagation(),0===f.scrollTop&&1===n.touches.length&&w("pulling"),e.refresherEnabled&&"pulling"===t.refreshState){const o=i-s.y;0===u&&(u=i),c?(t.refresherHeight=o+e.refresherThreshold,d=!1):(t.refresherHeight=i-u,t.refresherHeight>0&&(d=!0,r("refresherpulling",n,{deltaY:o})));const a=t.refresherHeight/e.refresherThreshold;t.refreshRotate=360*(a>1?1:a)}},h=function(e){1===e.touches.length&&(s={x:e.touches[0].pageX,y:e.touches[0].pageY})},p=function(n){s=null,t.refresherHeight>=e.refresherThreshold?w("refreshing"):w("refresherabort")};a.value.addEventListener("touchstart",h,Wg),a.value.addEventListener("touchmove",f,He(!1)),a.value.addEventListener("scroll",i,He(!1)),a.value.addEventListener("touchend",p,Wg),dr((()=>{a.value.removeEventListener("touchstart",h),a.value.removeEventListener("touchmove",f),a.value.removeEventListener("scroll",i),a.value.removeEventListener("touchend",p)}))})),Jo((()=>{e.scrollY&&(a.value.scrollTop=t.lastScrollTop),e.scrollX&&(a.value.scrollLeft=t.lastScrollLeft)})),Co(n,(e=>{v(e)})),Co(o,(e=>{y(e)})),Co((()=>e.scrollIntoView),(e=>{b(e)})),Co((()=>e.refresherTriggered),(e=>{!0===e?w("refreshing"):!1===e&&w("restore")}))}(e,c,u,d,l,o,r,a,t);const f=Gi((()=>{let t="";return e.scrollX?t+="overflow-x:auto;":t+="overflow-x:hidden;",e.scrollY?t+="overflow-y:auto;":t+="overflow-y:hidden;",t}));return()=>{const{refresherEnabled:t,refresherBackground:l,refresherDefaultStyle:u}=e,{refresherHeight:d,refreshState:h,refreshRotate:p}=c;return Ei("uni-scroll-view",{ref:o},[Ei("div",{ref:i,class:"uni-scroll-view"},[Ei("div",{ref:r,style:f.value,class:"uni-scroll-view"},[Ei("div",{ref:a,class:"uni-scroll-view-content"},[t?Ei("div",{ref:s,style:{backgroundColor:l,height:d+"px"},class:"uni-scroll-view-refresher"},["none"!==u?Ei("div",{class:"uni-scroll-view-refresh"},[Ei("div",{class:"uni-scroll-view-refresh-inner"},["pulling"==h?Ei("svg",{key:"refresh__icon",style:{transform:"rotate("+p+"deg)"},fill:"#2BD009",class:"uni-scroll-view-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},[Ei("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null),Ei("path",{d:"M0 0h24v24H0z",fill:"none"},null)],4):null,"refreshing"==h?Ei("svg",{key:"refresh__spinner",class:"uni-scroll-view-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},[Ei("circle",{cx:"50",cy:"50",r:"20",fill:"none",style:"color: #2bd009","stroke-width":"3"},null)]):null])]):null,"none"==u?n.refresher&&n.refresher():null],4):null,n.default&&n.default()],512)],4)],512)],512)}}});function zg(e,t,n,o,r,i){function a(){c&&(clearTimeout(c),c=null)}let s,l,c=null,u=!0,d=0,f=1,h=null,p=!1,g=0,m="";const v=Gi((()=>n.value.length>t.displayMultipleItems)),y=Gi((()=>e.circular&&v.value));function b(r){Math.floor(2*d)===Math.floor(2*r)&&Math.ceil(2*d)===Math.ceil(2*r)||y.value&&function(o){if(!u)for(let r=n.value,i=r.length,a=o+t.displayMultipleItems,s=0;s<i;s++){const t=r[s],n=Math.floor(o/i)*i+s,l=n+i,c=n-i,u=Math.max(o-(n+1),n-a,0),d=Math.max(o-(l+1),l-a,0),f=Math.max(o-(c+1),c-a,0),h=Math.min(u,d,f),p=[n,l,c][[u,d,f].indexOf(h)];t.updatePosition(p,e.vertical)}}(r);const a="translate("+(e.vertical?"0":100*-r*f+"%")+", "+(e.vertical?100*-r*f+"%":"0")+") translateZ(0)",l=o.value;if(l&&(l.style.webkitTransform=a,l.style.transform=a),d=r,!s){if(r%1==0)return;s=r}r-=Math.floor(s);const c=n.value;r<=-(c.length-1)?r+=c.length:r>=c.length&&(r-=c.length),r=s%1>.5||s<0?r-1:r,i("transition",{},{dx:e.vertical?0:r*l.offsetWidth,dy:e.vertical?r*l.offsetHeight:0})}function _(e){const o=n.value.length;if(!o)return-1;const r=(Math.round(e)%o+o)%o;if(y.value){if(o<=t.displayMultipleItems)return 0}else if(r>o-t.displayMultipleItems)return o-t.displayMultipleItems;return r}function w(){h=null}function x(){if(!h)return void(p=!1);const e=h,o=e.toPos,r=e.acc,a=e.endTime,c=e.source,u=a-Date.now();if(u<=0){b(o),h=null,p=!1,s=null;const e=n.value[t.current];if(e){const n=e.getItemId();i("animationfinish",{},{current:t.current,currentItemId:n,source:c})}return}b(o+r*u*u/2),l=requestAnimationFrame(x)}function T(e,o,r){w();const i=t.duration,a=n.value.length;let s=d;if(y.value)if(r<0){for(;s<e;)s+=a;for(;s-a>e;)s-=a}else if(r>0){for(;s>e;)s-=a;for(;s+a<e;)s+=a;s+a-e<e-s&&(s+=a)}else{for(;s+a<e;)s+=a;for(;s-a>e;)s-=a;s+a-e<e-s&&(s+=a)}else"click"===o&&(e=e+t.displayMultipleItems-1<a?e:0);h={toPos:e,acc:2*(s-e)/(i*i),endTime:Date.now()+i,source:o},p||(p=!0,l=requestAnimationFrame(x))}function k(){a();const e=n.value,o=function(){c=null,m="autoplay",y.value?t.current=_(t.current+1):t.current=t.current+t.displayMultipleItems<e.length?t.current+1:0,T(t.current,"autoplay",y.value?1:0),c=setTimeout(o,t.interval)};u||e.length<=t.displayMultipleItems||(c=setTimeout(o,t.interval))}function S(e){e?k():a()}return Co([()=>e.current,()=>e.currentItemId,()=>[...n.value]],(()=>{let o=-1;if(e.currentItemId)for(let t=0,r=n.value;t<r.length;t++){if(r[t].getItemId()===e.currentItemId){o=t;break}}o<0&&(o=Math.round(e.current)||0),o=o<0?0:o,t.current!==o&&(m="",t.current=o)})),Co([()=>e.vertical,()=>y.value,()=>t.displayMultipleItems,()=>[...n.value]],(function(){a(),h&&(b(h.toPos),h=null);const r=n.value;for(let t=0;t<r.length;t++)r[t].updatePosition(t,e.vertical);f=1;const i=o.value;if(1===t.displayMultipleItems&&r.length){const e=r[0].getBoundingClientRect(),t=i.getBoundingClientRect();f=e.width/t.width,f>0&&f<1||(f=1)}const s=d;d=-2;const l=t.current;l>=0?(u=!1,t.userTracking?(b(s+l-g),g=l):(b(l),e.autoplay&&k())):(u=!0,b(-t.displayMultipleItems-1))})),Co((()=>t.interval),(()=>{c&&(a(),k())})),Co((()=>t.current),((e,o)=>{!function(e,o){const r=m;m="";const a=n.value;if(!r){const t=a.length;T(e,"",y.value&&o+(t-e)%t>t/2?1:0)}const s=a[e];if(s){const e=t.currentItemId=s.getItemId();i("change",{},{current:t.current,currentItemId:e,source:r})}}(e,o),r("update:current",e)})),Co((()=>t.currentItemId),(e=>{r("update:currentItemId",e)})),Co((()=>e.autoplay&&!t.userTracking),S),S(e.autoplay&&!t.userTracking),lr((()=>{let r=!1,i=0,s=0;function l(e){t.userTracking=!1;const n=i/Math.abs(i);let o=0;!e&&Math.abs(i)>.2&&(o=.5*n);const r=_(d+o);e?b(g):(m="touch",t.current=r,T(r,"touch",0!==o?o:0===r&&y.value&&d>=1?1:0))}ig(o.value,(c=>{if(!e.disableTouch&&!u){if("start"===c.detail.state)return t.userTracking=!0,r=!1,a(),g=d,i=0,s=Date.now(),void w();if("end"===c.detail.state)return l(!1);if("cancel"===c.detail.state)return l(!0);if(t.userTracking){if(!r){r=!0;const n=Math.abs(c.detail.dx),o=Math.abs(c.detail.dy);if((n>=o&&e.vertical||n<=o&&!e.vertical)&&(t.userTracking=!1),!t.userTracking)return void(e.autoplay&&k())}return function(r){const a=s;s=Date.now();const l=n.value.length-t.displayMultipleItems;function c(e){return.5-.25/(e+.5)}function u(e,t){let n=g+e;i=.6*i+.4*t,y.value||(n<0||n>l)&&(n<0?n=-c(-n):n>l&&(n=l+c(n-l)),i=0),b(n)}const d=s-a||1,f=o.value;e.vertical?u(-r.dy/f.offsetHeight,-r.ddy/d):u(-r.dx/f.offsetWidth,-r.ddx/d)}(c.detail),!1}}}))})),fr((()=>{a(),cancelAnimationFrame(l)})),{onSwiperDotClick:function(e){T(t.current=e,m="click",y.value?1:0)},circularEnabled:y,swiperEnabled:v}}const Ug=Ru({name:"Swiper",props:{indicatorDots:{type:[Boolean,String],default:!1},vertical:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},circular:{type:[Boolean,String],default:!1},interval:{type:[Number,String],default:5e3},duration:{type:[Number,String],default:500},current:{type:[Number,String],default:0},indicatorColor:{type:String,default:""},indicatorActiveColor:{type:String,default:""},previousMargin:{type:String,default:""},nextMargin:{type:String,default:""},currentItemId:{type:String,default:""},skipHiddenItemLayout:{type:[Boolean,String],default:!1},displayMultipleItems:{type:[Number,String],default:1},disableTouch:{type:[Boolean,String],default:!1},navigation:{type:[Boolean,String],default:!1},navigationColor:{type:String,default:"#fff"},navigationActiveColor:{type:String,default:"rgba(53, 53, 53, 0.6)"}},emits:["change","transition","animationfinish","update:current","update:currentItemId"],setup(e,{slots:t,emit:n}){const o=An(null),r=Vu(o,n),i=An(null),a=An(null),s=function(e){return mn({interval:Gi((()=>{const t=Number(e.interval);return isNaN(t)?5e3:t})),duration:Gi((()=>{const t=Number(e.duration);return isNaN(t)?500:t})),displayMultipleItems:Gi((()=>{const t=Math.round(e.displayMultipleItems);return isNaN(t)?1:t})),current:Math.round(e.current)||0,currentItemId:e.currentItemId,userTracking:!1})}(e),l=Gi((()=>{let t={};return(e.nextMargin||e.previousMargin)&&(t=e.vertical?{left:0,right:0,top:Hc(e.previousMargin,!0),bottom:Hc(e.nextMargin,!0)}:{top:0,bottom:0,left:Hc(e.previousMargin,!0),right:Hc(e.nextMargin,!0)}),t})),c=Gi((()=>{const t=Math.abs(100/s.displayMultipleItems)+"%";return{width:e.vertical?"100%":t,height:e.vertical?t:"100%"}}));let u=[];const d=[],f=An([]);function h(){const e=[];for(let t=0;t<u.length;t++){let n=u[t];n instanceof Element||(n=n.el);const o=d.find((e=>n===e.rootRef.value));o&&e.push(Sn(o))}f.value=e}xo("addSwiperContext",(function(e){d.push(e),h()}));xo("removeSwiperContext",(function(e){const t=d.indexOf(e);t>=0&&(d.splice(t,1),h())}));const{onSwiperDotClick:p,circularEnabled:g,swiperEnabled:m}=zg(e,s,f,a,n,r);let v=()=>null;return v=Yg(o,e,s,p,f,g,m),()=>{const n=t.default&&t.default();return u=Qp(n),Ei("uni-swiper",{ref:o},[Ei("div",{ref:i,class:"uni-swiper-wrapper"},[Ei("div",{class:"uni-swiper-slides",style:l.value},[Ei("div",{ref:a,class:"uni-swiper-slide-frame",style:c.value},[n],4)],4),e.indicatorDots&&Ei("div",{class:["uni-swiper-dots",e.vertical?"uni-swiper-dots-vertical":"uni-swiper-dots-horizontal"]},[f.value.map(((t,n,o)=>Ei("div",{onClick:()=>p(n),class:{"uni-swiper-dot":!0,"uni-swiper-dot-active":n<s.current+s.displayMultipleItems&&n>=s.current||n<s.current+s.displayMultipleItems-o.length},style:{background:n===s.current?e.indicatorActiveColor:e.indicatorColor}},null,14,["onClick"])))],2),v()],512)],512)}}}),Yg=(e,t,n,o,r,i,a)=>{let s=!1,l=!1,c=!1,u=An(!1);function d(e,n){const o=e.currentTarget;o&&(o.style.backgroundColor="over"===n?t.navigationActiveColor:"")}ko((()=>{s="auto"===t.navigation,u.value=!0!==t.navigation||s,y()})),ko((()=>{const e=r.value.length,t=!i.value;l=0===n.current&&t,c=n.current===e-1&&t||t&&n.current+n.displayMultipleItems>=e,a.value||(l=!0,c=!0,s&&(u.value=!0))}));const f={onMouseover:e=>d(e,"over"),onMouseout:e=>d(e,"out")};function h(e,t,a){if(e.stopPropagation(),a)return;const s=r.value.length;let l=n.current;switch(t){case"prev":l--,l<0&&i.value&&(l=s-1);break;case"next":l++,l>=s&&i.value&&(l=0)}o(l)}const p=()=>Yc("M21.781 7.844l-9.063 8.594 9.063 8.594q0.25 0.25 0.25 0.609t-0.25 0.578q-0.25 0.25-0.578 0.25t-0.578-0.25l-9.625-9.125q-0.156-0.125-0.203-0.297t-0.047-0.359q0-0.156 0.047-0.328t0.203-0.297l9.625-9.125q0.25-0.25 0.578-0.25t0.578 0.25q0.25 0.219 0.25 0.578t-0.25 0.578z",t.navigationColor,26);let g;const m=n=>{clearTimeout(g);const{clientX:o,clientY:r}=n,{left:i,right:a,top:s,bottom:l,width:c,height:d}=e.value.getBoundingClientRect();let f=!1;if(f=t.vertical?!(r-s<d/3||l-r<d/3):!(o-i<c/3||a-o<c/3),f)return g=setTimeout((()=>{u.value=f}),300);u.value=f},v=()=>{u.value=!0};function y(){e.value&&(e.value.removeEventListener("mousemove",m),e.value.removeEventListener("mouseleave",v),s&&(e.value.addEventListener("mousemove",m),e.value.addEventListener("mouseleave",v)))}return lr(y),function(){const e={"uni-swiper-navigation-hide":u.value,"uni-swiper-navigation-vertical":t.vertical};return t.navigation?Ei(ci,null,[Ei("div",Li({class:["uni-swiper-navigation uni-swiper-navigation-prev",x({"uni-swiper-navigation-disabled":l},e)],onClick:e=>h(e,"prev",l)},f),[p()],16,["onClick"]),Ei("div",Li({class:["uni-swiper-navigation uni-swiper-navigation-next",x({"uni-swiper-navigation-disabled":c},e)],onClick:e=>h(e,"next",c)},f),[p()],16,["onClick"])]):null}},Xg=Ru({name:"SwiperItem",props:{itemId:{type:String,default:""}},setup(e,{slots:t}){const n=An(null),o={rootRef:n,getItemId:()=>e.itemId,getBoundingClientRect:()=>n.value.getBoundingClientRect(),updatePosition(e,t){const o=t?"0":100*e+"%",r=t?100*e+"%":"0",i=n.value,a=`translate(${o},${r}) translateZ(0)`;i&&(i.style.webkitTransform=a,i.style.transform=a)}};return lr((()=>{const e=To("addSwiperContext");e&&e(o)})),fr((()=>{const e=To("removeSwiperContext");e&&e(o)})),()=>Ei("uni-swiper-item",{ref:n,style:{position:"absolute",width:"100%",height:"100%"}},[t.default&&t.default()],512)}}),Gg=Ru({name:"Switch",props:{name:{type:String,default:""},checked:{type:[Boolean,String],default:!1},type:{type:String,default:"switch"},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},color:{type:String,default:""}},emits:["change"],setup(e,{emit:t}){const n=An(null),o=An(e.checked),r=function(e,t){const n=To(qu,!1),o=To(zu,!1),r={submit:()=>{const n=["",null];return e.name&&(n[0]=e.name,n[1]=t.value),n},reset:()=>{t.value=!1}};n&&(n.addField(r),fr((()=>{n.removeField(r)})));return o}(e,o),i=Vu(n,t);Co((()=>e.checked),(e=>{o.value=e}));const a=t=>{e.disabled||(o.value=!o.value,i("change",t,{value:o.value}))};return r&&(r.addHandler(a),dr((()=>{r.removeHandler(a)}))),Yu(e,{"label-click":a}),()=>{const{color:t,type:r}=e,i=Nu(e,"disabled"),s={};return t&&o.value&&(s.backgroundColor=t,s.borderColor=t),Ei("uni-switch",Li({ref:n},i,{onClick:a}),[Ei("div",{class:"uni-switch-wrapper"},[vr(Ei("div",{class:["uni-switch-input",[o.value?"uni-switch-input-checked":""]],style:s},null,6),[[Wa,"switch"===r]]),vr(Ei("div",{class:"uni-checkbox-input"},[o.value?Yc(zc,e.color,22):""],512),[[Wa,"checkbox"===r]])])],16,["onClick"])}}});const Jg={ensp:" ",emsp:" ",nbsp:" "};function Kg(e,t){return e.replace(/\\n/g,Z).split(Z).map((e=>function(e,{space:t,decode:n}){if(!e)return e;t&&Jg[t]&&(e=e.replace(/ /g,Jg[t]));if(!n)return e;return e.replace(/&nbsp;/g,Jg.nbsp).replace(/&ensp;/g,Jg.ensp).replace(/&emsp;/g,Jg.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'")}(e,t)))}const Zg=Ru({name:"Text",props:{selectable:{type:[Boolean,String],default:!1},space:{type:String,default:""},decode:{type:[Boolean,String],default:!1}},setup:(e,{slots:t})=>()=>{const n=[];return t.default&&t.default().forEach((t=>{if(8&t.shapeFlag&&t.type!==di){const o=Kg(t.children,{space:e.space,decode:e.decode}),r=o.length-1;o.forEach(((e,t)=>{(0!==t||e)&&n.push(Mi(e)),t!==r&&n.push(Ei("br"))}))}else n.push(t)})),Ei("uni-text",{selectable:!!e.selectable||null},[Ei("span",null,n)],8,["selectable"])}}),Qg=x({},Hp,{placeholderClass:{type:String,default:"input-placeholder"},autoHeight:{type:[Boolean,String],default:!1},confirmType:{type:String,default:"return",validator:e=>tm.concat("return").includes(e)}});let em=!1;const tm=["done","go","next","search","send"];const nm=Ru({name:"Textarea",props:Qg,emits:["confirm","linechange",...zp],setup(e,{emit:t}){const n=An(null),o=An(null),{fieldRef:r,state:i,scopedAttrsState:a,fixDisabledColor:s,trigger:l}=Xp(e,n,t),c=Gi((()=>i.value.split(Z))),u=Gi((()=>tm.includes(e.confirmType))),d=An(0),f=An(null);function h({height:e}){d.value=e}function p(e){"Enter"===e.key&&u.value&&e.preventDefault()}function g(t){if("Enter"===t.key&&u.value){!function(e){l("confirm",e,{value:i.value})}(t);const n=t.target;!e.confirmHold&&n.blur()}}return Co((()=>d.value),(t=>{const r=n.value,i=f.value,a=o.value;let s=parseFloat(getComputedStyle(r).lineHeight);isNaN(s)&&(s=i.offsetHeight);var c=Math.round(t/s);l("linechange",{},{height:t,heightRpx:750/window.innerWidth*t,lineCount:c}),e.autoHeight&&(r.style.height="auto",a.style.height=t+"px")})),function(){const e="(prefers-color-scheme: dark)";em=0===String(navigator.platform).indexOf("iP")&&0===String(navigator.vendor).indexOf("Apple")&&window.matchMedia(e).media!==e}(),()=>{let t=e.disabled&&s?Ei("textarea",{key:"disabled-textarea",ref:r,value:i.value,tabindex:"-1",readonly:!!e.disabled,maxlength:i.maxlength,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":em},style:{overflowY:e.autoHeight?"hidden":"auto"},onFocus:e=>e.target.blur()},null,46,["value","readonly","maxlength","onFocus"]):Ei("textarea",{key:"textarea",ref:r,value:i.value,disabled:!!e.disabled,maxlength:i.maxlength,enterkeyhint:e.confirmType,inputmode:e.inputmode,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":em},style:{overflowY:e.autoHeight?"hidden":"auto"},onKeydown:p,onKeyup:g},null,46,["value","disabled","maxlength","enterkeyhint","inputmode","onKeydown","onKeyup"]);return Ei("uni-textarea",{ref:n},[Ei("div",{ref:o,class:"uni-textarea-wrapper"},[vr(Ei("div",Li(a.attrs,{style:e.placeholderStyle,class:["uni-textarea-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Wa,!i.value.length]]),Ei("div",{ref:f,class:"uni-textarea-line"},[" "],512),Ei("div",{class:"uni-textarea-compute"},[c.value.map((e=>Ei("div",null,[e.trim()?e:"."]))),Ei(Qh,{initial:!0,onResize:h},null,8,["initial","onResize"])]),"search"===e.confirmType?Ei("form",{action:"",onSubmit:()=>!1,class:"uni-input-form"},[t],40,["onSubmit"]):t],512)],512)}}}),om=Ru({name:"View",props:x({},Bu),setup(e,{slots:t}){const{hovering:n,binding:o}=Du(e);return()=>{const r=e.hoverClass;return r&&"none"!==r?Ei("uni-view",Li({class:n.value?r:""},o),[t.default&&t.default()],16):Ei("uni-view",null,[t.default&&t.default()])}}});function rm(e,t){if(t||(t=e.id),t)return e.$options.name.toLowerCase()+"."+t}function im(e,t,n){e&&fc(n||Zc(),e,(({type:e,data:n},o)=>{t(e,n,o)}))}function am(e,t){e&&function(e,t){t=dc(e,t),delete uc[t]}(t||Zc(),e)}function sm(e,t,n,o){const r=Ni().proxy;lr((()=>{im(t||rm(r),e,o),!n&&t||Co((()=>r.id),((t,n)=>{im(rm(r,t),e,o),am(n&&rm(r,n))}))})),dr((()=>{am(t||rm(r),o)}))}let lm=0;function cm(e,t,n,o){M(t)&&ir(e,t.bind(n),o)}function um(e,t,n){var o;const r=e.mpType||n.$mpType;if(r&&"component"!==r&&(Object.keys(e).forEach((o=>{if(function(e,t,n=!0){return!(n&&!M(t))&&(et.indexOf(e)>-1||0===e.indexOf("on"))}(o,e[o],!1)){const r=e[o];C(r)?r.forEach((e=>cm(o,e,n,t))):cm(o,r,n,t)}})),"page"===r)){t.__isVisible=!0;try{nu(n,ce,t.attrs.__pageQuery),delete t.attrs.__pageQuery,"preloadPage"!==(null==(o=n.$page)?void 0:o.openType)&&nu(n,ne)}catch(zE){console.error(zE.message+Z+zE.stack)}}}function dm(e,t,n){um(e,t,n)}function fm(e,t,n){return e[t]=n}function hm(e){return function(t,n,o){if(!n)throw t;const r=e._instance;if(!r||!r.proxy)throw t;nu(r.proxy,ie,t)}}function pm(e,t){return e?[...new Set([].concat(e,t))]:t}function gm(e){const t=e._context.config;var n;t.errorHandler=nt(e,hm),n=t.optionMergeStrategies,et.forEach((e=>{n[e]=pm}));const o=t.globalProperties;o.$set=fm,o.$applyOptions=dm,function(e){tt.forEach((t=>t(e)))}(e)}const mm=qc("upm");function vm(){return To(mm)}function ym(e){const t=function(e){return mn(function(e){{const{enablePullDownRefresh:t,navigationBar:n}=e;if(t){const t=function(e){return e.offset&&(e.offset=Hc(e.offset)),e.height&&(e.height=Hc(e.height)),e.range&&(e.range=Hc(e.range)),e}(x({support:!0,color:"#2BD009",style:"circle",height:70,range:150,offset:0},e.pullToRefresh)),{type:o,style:r}=n;"custom"!==r&&"transparent"!==o&&(t.offset+=44+Lc.top),e.pullToRefresh=t}}if(history.state){const t=history.state.__type__;"redirectTo"!==t&&"reLaunch"!==t||0!==$m().length||(e.isEntry=!0,e.isQuit=!0)}return e}(JSON.parse(JSON.stringify(tu(Hl().meta,e)))))}(e);return xo(mm,t),t}function bm(){return Hl()}function _m(){return history.state&&history.state.__id__||1}let wm;function xm(){var e;return wm||(wm=__uniConfig.tabBar&&mn((e=__uniConfig.tabBar,zl()&&e.list&&e.list.forEach((e=>{Gl(e,["text"])})),e))),wm}const Tm=window.CSS&&window.CSS.supports;function km(e){return Tm&&(Tm(e)||Tm.apply(window.CSS,e.split(":")))}const Sm=km("top:env(a)"),Cm=km("top:constant(a)"),Em=km("backdrop-filter:blur(10px)"),Om=(()=>Sm?"env":Cm?"constant":"")();function Mm(e){return Om?`calc(${e}px + ${Om}(safe-area-inset-bottom))`:`${e}px`}const Im="$$",Am=new Map;function Pm(){return Am}function $m(){const e=[],t=Am.values();for(const n of t)n.$.__isTabBar?n.$.__isActive&&e.push(n):e.push(n);return e}function Lm(e,t=!0){const n=Am.get(e);n.$.__isUnload=!0,nu(n,ue),Am.delete(e),t&&function(e){const t=Nm.get(e);t&&(Nm.delete(e),Fm.pruneCacheEntry(t))}(e)}let Rm=_m();function jm(e){const t=vm();let n=e.fullPath;return e.meta.isEntry&&-1===n.indexOf(e.meta.route)&&(n="/"+e.meta.route+n.replace("/","")),function(e,t,n,o,r,i){const{id:a,route:s}=o,l=at(o.navigationBar,__uniConfig.themeConfig,i).titleColor;return{id:a,path:Le(s),route:s,fullPath:t,options:n,meta:o,openType:e,eventChannel:r,statusBarStyle:"#000000"===l?"dark":"light"}}("navigateTo",n,{},t)}function Bm(e){const t=jm(e.$route);!function(e,t){e.route=t.route,e.$vm=e,e.$page=t,e.$mpType="page",t.meta.isTabBar&&(e.$.__isTabBar=!0,e.$.__isActive=!0)}(e,t),Am.set(Dm(t.path,t.id),e)}function Dm(e,t){return e+Im+t}const Nm=new Map,Fm={get:e=>Nm.get(e),set(e,t){!function(e){const t=parseInt(e.split(Im)[1]);if(!t)return;Fm.forEach(((e,n)=>{const o=parseInt(n.split(Im)[1]);if(o&&o>t){if(function(e){return"tabBar"===e.props.type}(e))return;Fm.delete(n),Fm.pruneCacheEntry(e),eo((()=>{Am.forEach(((e,t)=>{e.$.isUnmounted&&Am.delete(t)}))}))}}))}(e),Nm.set(e,t)},delete(e){Nm.get(e)&&Nm.delete(e)},forEach(e){Nm.forEach(e)}};function Vm(e,t){!function(e){const t=Wm(e),{body:n}=document;Hm&&n.removeAttribute(Hm),t&&n.setAttribute(t,""),Hm=t}(e),function(e){let t=0;if(e.isTabBar){const e=xm();e.shown&&(t=parseInt(e.height))}var n;Vc({"--window-top":(n=0,Om?`calc(${n}px + ${Om}(safe-area-inset-top))`:`${n}px`),"--window-bottom":Mm(t)})}(t),function(e){const t="nvue-dir-"+__uniConfig.nvue["flex-direction"];e.isNVue?(document.body.setAttribute("nvue",""),document.body.setAttribute(t,"")):(document.body.removeAttribute("nvue"),document.body.removeAttribute(t))}(t),function(e,t){document.removeEventListener("touchmove",ou),zm&&document.removeEventListener("scroll",zm);if(t.disableScroll)return document.addEventListener("touchmove",ou);const{onPageScroll:n,onReachBottom:o}=e,r="transparent"===t.navigationBar.type;if(!n&&!o&&!r)return;const i={},a=e.proxy.$page.id;(n||r)&&(i.onPageScroll=function(e,t,n){return o=>{t&&Wb.publishHandler(ge,{scrollTop:o},e),n&&Wb.emit(e+"."+ge,{scrollTop:o})}}(a,n,r));o&&(i.onReachBottomDistance=t.onReachBottomDistance||50,i.onReachBottom=()=>Wb.publishHandler(ve,{},a));zm=au(i),requestAnimationFrame((()=>document.addEventListener("scroll",zm)))}(e,t)}function qm(e){const t=Wm(e);t&&function(e){const t=document.querySelector("uni-page-body");t&&t.setAttribute(e,"")}(t)}function Wm(e){return e.type.__scopeId}let Hm,zm;function Um(e){const t=Wl({history:Xm(),strict:!!__uniConfig.router.strict,routes:__uniRoutes,scrollBehavior:Ym});e.router=t,e.use(t)}const Ym=(e,t,n)=>{if(n)return n};function Xm(){let{routerBase:e}=__uniConfig.router;"/"===e&&(e="");const t=tl(e);return t.listen(((e,t,n)=>{"back"===n.direction&&function(e=1){const t=$m(),n=t.length-1,o=n-e;for(let r=n;r>o;r--){const e=t[r].$page;Lm(Dm(e.path,e.id),!1)}}(Math.abs(n.delta))})),t}const Gm={install(e){gm(e),bu(e),Iu(e),e.config.warnHandler||(e.config.warnHandler=Jm),Um(e)}};function Jm(e,t,n){if(t){if("PageMetaHead"===t.$.type.name)return;const e=t.$.parent;if(e&&"PageMeta"===e.type.name)return}const o=[`[Vue warn]: ${e}`];n.length&&o.push("\n",n),console.warn(...o)}const Km={class:"uni-async-loading"},Zm=Ei("i",{class:"uni-loading"},null,-1),Qm=ju({name:"AsyncLoading",render:()=>(gi(),_i("div",Km,[Zm]))});function ev(){window.location.reload()}const tv=ju({name:"AsyncError",setup(){Zl();const{t:e}=Jl();return()=>Ei("div",{class:"uni-async-error",onClick:ev},[e("uni.async.error")],8,["onClick"])}});let nv;function ov(){return nv}function rv(e){nv=e,Object.defineProperty(nv.$.ctx,"$children",{get:()=>$m().map((e=>e.$vm))});const t=nv.$.appContext.app;t.component(Qm.name)||t.component(Qm.name,Qm),t.component(tv.name)||t.component(tv.name,tv),function(e){e.$vm=e,e.$mpType="app";const t=An(Jl().getLocale());Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(nv),function(e,t){const n=e.$options||{};n.globalData=x(n.globalData||{},t),Object.defineProperty(e,"globalData",{get:()=>n.globalData,set(e){n.globalData=e}})}(nv),Ou(),kc()}function iv(e,{clone:t,init:n,setup:o,before:r}){t&&(e=x({},e)),r&&r(e);const i=e.setup;return e.setup=(e,t)=>{const r=Ni();n(r.proxy);const a=o(r);if(i)return i(a||e,t)},e}function av(e,t){return e&&(e.__esModule||"Module"===e[Symbol.toStringTag])?iv(e.default,t):iv(e,t)}function sv(e){return av(e,{clone:!0,init:Bm,setup(e){e.$pageInstance=e;const t=bm(),n=Ye(t.query);e.attrs.__pageQuery=n,e.proxy.$page.options=n;const o=vm();var r,i,a;return sr((()=>{Vm(e,o)})),lr((()=>{qm(e);const{onReady:n}=e;n&&Y(n),dv(t)})),Zo((()=>{if(!e.__isVisible){Vm(e,o),e.__isVisible=!0;const{onShow:n}=e;n&&Y(n),eo((()=>{dv(t)}))}}),"ba",r),function(e,t){Zo(e,"bda",t)}((()=>{if(e.__isVisible&&!e.__isUnload){e.__isVisible=!1;const{onHide:t}=e;t&&Y(t)}})),i=o.id,Wb.subscribe(dc(i,sc),a?a(hc):hc),dr((()=>{!function(e){Wb.unsubscribe(dc(e,sc)),Object.keys(uc).forEach((t=>{0===t.indexOf(e+".")&&delete uc[t]}))}(o.id)})),n}})}function lv(){const{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}=Ov(),r=90===Math.abs(Number(window.orientation))?"landscape":"portrait";Hb.emit(he,{deviceOrientation:r,size:{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}})}function cv(e){j(e.data)&&"WEB_INVOKE_APPSERVICE"===e.data.type&&Hb.emit(Me,e.data.data,e.data.pageId)}function uv(){const{emit:e}=Hb;"visible"===document.visibilityState?e(Ee,x({},Zh)):e(Oe)}function dv(e){const{tabBarText:t,tabBarIndex:n,route:o}=e.meta;t&&nu("onTabItemTap",{index:n,text:t,pagePath:o})}const fv=({name:e,arg:t})=>{"postMessage"===e||uni[e](t)},hv=Be((()=>Hb.on(Me,fv))),pv=Ru({inheritAttrs:!1,name:"WebView",props:{src:{type:String,default:""},fullscreen:{type:Boolean,default:!0}},setup(e){hv();const t=An(null),n=An(null),{$attrs:o,$excludeAttrs:r,$listeners:i}=Zp({excludeListeners:!0});let a;return(()=>{const r=document.createElement("iframe");ko((()=>{for(const e in o.value)if(S(o.value,e)){const t=o.value[e];r[e]=t}})),ko((()=>{r.src=Qu(e.src)})),n.value=r,a=function(e,t,n){const o=()=>{var o,r;if(n){const{top:n,left:o,width:r,height:i}=e.value.getBoundingClientRect();je(t.value,{position:"absolute",display:"block",border:"0",top:n+"px",left:o+"px",width:r+"px",height:i+"px"})}else je(t.value,{width:(null==(o=e.value)?void 0:o.style.width)||"300px",height:(null==(r=e.value)?void 0:r.style.height)||"150px"})};return o}(t,n,e.fullscreen),e.fullscreen&&document.body.appendChild(r)})(),lr((()=>{var o;a(),!e.fullscreen&&(null==(o=t.value)||o.appendChild(n.value))})),Jo((()=>{e.fullscreen&&(n.value.style.display="block")})),Ko((()=>{e.fullscreen&&(n.value.style.display="none")})),dr((()=>{e.fullscreen&&document.body.removeChild(n.value)})),()=>Ei(ci,null,[Ei("uni-web-view",Li({class:e.fullscreen?"uni-webview--fullscreen":""},i.value,r.value,{ref:t}),[Ei(Qh,{onResize:a},null,8,["onResize"])],16)])}});const gv=Xd("makePhoneCall",(({phoneNumber:e},{resolve:t})=>(window.location.href=`tel:${e}`,t()))),mv="__DC_STAT_UUID",vv=window.localStorage||window.sessionStorage||{};let yv;function bv(){if(yv=yv||vv[mv],!yv){yv=Date.now()+""+Math.floor(1e7*Math.random());try{vv[mv]=yv}catch(e){}}return yv}function _v(){if(!0!==__uniConfig.darkmode)return I(__uniConfig.darkmode)?__uniConfig.darkmode:"light";try{return window.matchMedia("(prefers-color-scheme: light)").matches?"light":"dark"}catch(e){return"light"}}function wv(){let e,t="0",n="",o="phone";const r=navigator.language;if(nd){e="iOS";const o=ed.match(/OS\s([\w_]+)\slike/);o&&(t=o[1].replace(/_/g,"."));const r=ed.match(/\(([a-zA-Z]+);/);r&&(n=r[1])}else if(td){e="Android";const o=ed.match(/Android[\s/]([\w\.]+)[;\s]/);o&&(t=o[1]);const r=ed.match(/\((.+?)\)/),i=r?r[1].split(";"):ed.split(" "),a=[/\bAndroid\b/i,/\bLinux\b/i,/\bU\b/i,/^\s?[a-z][a-z]$/i,/^\s?[a-z][a-z]-[a-z][a-z]$/i,/\bwv\b/i,/\/[\d\.,]+$/,/^\s?[\d\.,]+$/,/\bBrowser\b/i,/\bMobile\b/i];for(let e=0;e<i.length;e++){const t=i[e];if(t.indexOf("Build")>0){n=t.split("Build")[0].trim();break}let o;for(let e=0;e<a.length;e++)if(a[e].test(t)){o=!0;break}if(!o){n=t.trim();break}}}else if(ad)n="iPad",e="iOS",o="pad",t=M(window.BigInt)?"14.0":"13.0";else if(od||rd||id){n="PC",e="PC",o="pc",t="0";let r=ed.match(/\((.+?)\)/)[1];if(od){switch(e="Windows",od[1]){case"5.1":t="XP";break;case"6.0":t="Vista";break;case"6.1":t="7";break;case"6.2":t="8";break;case"6.3":t="8.1";break;case"10.0":t="10"}const n=r&&r.match(/[Win|WOW]([\d]+)/);n&&(t+=` x${n[1]}`)}else if(rd){e="macOS";const n=r&&r.match(/Mac OS X (.+)/)||"";t&&(t=n[1].replace(/_/g,"."),-1!==t.indexOf(";")&&(t=t.split(";")[0]))}else if(id){e="Linux";const n=r&&r.match(/Linux (.*)/)||"";n&&(t=n[1],-1!==t.indexOf(";")&&(t=t.split(";")[0]))}}else e="Other",t="0",o="unknown";const i=`${e} ${t}`,a=e.toLocaleLowerCase();let s="",l=String(function(){const e=navigator.userAgent,t=e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1,n=e.indexOf("Edge")>-1&&!t,o=e.indexOf("Trident")>-1&&e.indexOf("rv:11.0")>-1;if(t){new RegExp("MSIE (\\d+\\.\\d+);").test(e);const t=parseFloat(RegExp.$1);return t>6?t:6}return n?-1:o?11:-1}());if("-1"!==l)s="IE";else{const e=["Version","Firefox","Chrome","Edge{0,1}"],t=["Safari","Firefox","Chrome","Edge"];for(let n=0;n<e.length;n++){const o=e[n],r=new RegExp(`(${o})/(\\S*)\\b`);r.test(ed)&&(s=t[n],l=ed.match(r)[2])}}let c="portrait";const u=void 0===window.screen.orientation?window.orientation:window.screen.orientation.angle;return c=90===Math.abs(u)?"landscape":"portrait",{deviceBrand:void 0,brand:void 0,deviceModel:n,deviceOrientation:c,model:n,system:i,platform:a,browserName:s.toLocaleLowerCase(),browserVersion:l,language:r,deviceType:o,ua:ed,osname:e,osversion:t,theme:_v()}}const xv=Yd(0,(()=>{const e=window.devicePixelRatio,t=sd(),n=ld(t),o=cd(t,n),r=function(e,t){return e?Math[t?"min":"max"](screen.height,screen.width):screen.height}(t,n),i=ud(o);let a=window.innerHeight;const s=Lc.top,l={left:Lc.left,right:i-Lc.right,top:Lc.top,bottom:a-Lc.bottom,width:i-Lc.left-Lc.right,height:a-Lc.top-Lc.bottom},{top:c,bottom:u}=Nc();return a-=c,a-=u,{windowTop:c,windowBottom:u,windowWidth:i,windowHeight:a,pixelRatio:e,screenWidth:o,screenHeight:r,statusBarHeight:s,safeArea:l,safeAreaInsets:{top:Lc.top,right:Lc.right,bottom:Lc.bottom,left:Lc.left},screenTop:r-a}}));let Tv,kv=!0;function Sv(){kv&&(Tv=wv())}const Cv=Yd(0,(()=>{Sv();const{deviceBrand:e,deviceModel:t,brand:n,model:o,platform:r,system:i,deviceOrientation:a,deviceType:s}=Tv;return{brand:n,deviceBrand:e,deviceModel:t,devicePixelRatio:window.devicePixelRatio,deviceId:bv(),deviceOrientation:a,deviceType:s,model:o,platform:r,system:i}})),Ev=Yd(0,(()=>{Sv();const{theme:e,language:t,browserName:n,browserVersion:o}=Tv;return{appId:__uniConfig.appId,appName:__uniConfig.appName,appVersion:__uniConfig.appVersion,appVersionCode:__uniConfig.appVersionCode,appLanguage:Qf?Qf():t,enableDebug:!1,hostSDKVersion:void 0,hostPackageName:void 0,hostFontSizeSetting:void 0,hostName:n,hostVersion:o,hostTheme:e,hostLanguage:t,language:t,SDKVersion:"",theme:e,version:""}})),Ov=Yd(0,(()=>{kv=!0,Sv(),kv=!1;const e=xv(),t=Cv(),n=Ev();kv=!0;const{ua:o,browserName:r,browserVersion:i,osname:a,osversion:s}=Tv,l=x(e,t,n,{ua:o,browserName:r,browserVersion:i,uniPlatform:"web",uniCompileVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion,fontSizeSetting:void 0,osName:a.toLocaleLowerCase(),osVersion:s,osLanguage:void 0,osTheme:void 0});return delete l.screenTop,delete l.enableDebug,__uniConfig.darkmode||delete l.theme,function(e){let t={};return j(e)&&Object.keys(e).sort().forEach((n=>{const o=n;t[o]=e[o]})),Object.keys(t)?t:e}(l)})),Mv=!!window.navigator.vibrate,Iv=Xd("vibrateShort",((e,{resolve:t,reject:n})=>{Mv&&window.navigator.vibrate(15)?t():n("vibrateLong:fail")}));const Av=Xd("setClipboardData",((e,t)=>{return n=void 0,o=[e,t],r=function*({data:e},{resolve:t,reject:n}){try{yield navigator.clipboard.writeText(e),t()}catch(o){!function(e,t,n){const o=document.getElementById("#clipboard");o&&o.remove();const r=document.createElement("textarea");r.id="#clipboard",r.style.position="fixed",r.style.top="-9999px",r.style.zIndex="-9999",document.body.appendChild(r),r.value=e,r.select(),r.setSelectionRange(0,r.value.length);const i=document.execCommand("Copy",!1);r.blur(),i?t():n()}(e,t,n)}},new Promise(((e,t)=>{var i=e=>{try{s(r.next(e))}catch(zE){t(zE)}},a=e=>{try{s(r.throw(e))}catch(zE){t(zE)}},s=t=>t.done?e(t.value):Promise.resolve(t.value).then(i,a);s((r=r.apply(n,o)).next())}));var n,o,r}),0,oh);const Pv=Yd(0,((e,t)=>{const n=typeof t,o="string"===n?t:JSON.stringify({type:n,data:t});localStorage.setItem(e,o)})),$v=Xd("setStorage",(({key:e,data:t},{resolve:n,reject:o})=>{try{Pv(e,t),n()}catch(r){o(r.message)}}));function Lv(e){const t=localStorage&&localStorage.getItem(e);if(!I(t))throw new Error("data not found");let n=t;try{const e=function(e){const t=["object","string","number","boolean","undefined"];try{const n=I(e)?JSON.parse(e):e,o=n.type;if(t.indexOf(o)>=0){const e=Object.keys(n);if(2===e.length&&"data"in n){if(typeof n.data===o)return n.data;if("object"===o&&/^\d{4}-\d{2}-\d{2}T\d{2}\:\d{2}\:\d{2}\.\d{3}Z$/.test(n.data))return new Date(n.data)}else if(1===e.length)return""}}catch(n){}}(JSON.parse(t));void 0!==e&&(n=e)}catch(o){}return n}const Rv=Yd(0,(e=>{try{return Lv(e)}catch(t){return""}})),jv=Xd("getStorage",(({key:e},{resolve:t,reject:n})=>{try{t({data:Lv(e)})}catch(o){n(o.message)}})),Bv=Yd(0,(e=>{localStorage&&localStorage.removeItem(e)})),Dv=Yd(0,(()=>{localStorage&&localStorage.clear()})),Nv=Xd("hideKeyboard",((e,{resolve:t,reject:n})=>{const o=document.activeElement;!o||"TEXTAREA"!==o.tagName&&"INPUT"!==o.tagName||(o.blur(),t())}));const Fv=Xd("getImageInfo",(({src:e},{resolve:t,reject:n})=>{const o=new Image;o.onload=function(){t({width:o.naturalWidth,height:o.naturalHeight,path:0===e.indexOf("/")?window.location.protocol+"//"+window.location.host+e:e})},o.onerror=function(){n()},o.src=e}),0,lh),Vv={image:{jpg:"jpeg",jpe:"jpeg",pbm:"x-portable-bitmap",pgm:"x-portable-graymap",pnm:"x-portable-anymap",ppm:"x-portable-pixmap",psd:"vnd.adobe.photoshop",pic:"x-pict",rgb:"x-rgb",svg:"svg+xml",svgz:"svg+xml",tif:"tiff",xif:"vnd.xiff",wbmp:"vnd.wap.wbmp",wdp:"vnd.ms-photo",xbm:"x-xbitmap",ico:"x-icon"},video:{"3g2":"3gpp2","3gp":"3gpp",avi:"x-msvideo",f4v:"x-f4v",flv:"x-flv",jpgm:"jpm",jpgv:"jpeg",m1v:"mpeg",m2v:"mpeg",mpe:"mpeg",mpg:"mpeg",mpg4:"mpeg",m4v:"x-m4v",mkv:"x-matroska",mov:"quicktime",qt:"quicktime",movie:"x-sgi-movie",mp4v:"mp4",ogv:"ogg",smv:"x-smv",wm:"x-ms-wm",wmv:"x-ms-wmv",wmx:"x-ms-wmx",wvx:"x-ms-wvx"}};function qv({count:e,sourceType:t,type:n,extension:o}){const r=document.createElement("input");return r.type="file",je(r,{position:"absolute",visibility:"hidden",zIndex:"-999",width:"0",height:"0",top:"0",left:"0"}),r.accept=o.map((e=>{if("all"!==n){const t=e.replace(".","");return`${n}/${Vv[n][t]||t}`}return function(){const e=window.navigator.userAgent.toLowerCase().match(/MicroMessenger/i);return!(!e||"micromessenger"!==e[0])}()?".":0===e.indexOf(".")?e:`.${e}`})).join(","),e&&e>1&&(r.multiple=!0),"all"!==n&&t instanceof Array&&1===t.length&&"camera"===t[0]&&r.setAttribute("capture","camera"),r}Bp();let Wv=null;const Hv=Xd("chooseFile",(({count:e,sourceType:t,type:n,extension:o},{resolve:r,reject:i})=>{oc();const{t:a}=Jl();Wv&&(document.body.removeChild(Wv),Wv=null),Wv=qv({count:e,sourceType:t,type:n,extension:o}),document.body.appendChild(Wv),Wv.addEventListener("change",(function(t){const n=t.target,o=[];if(n&&n.files){const t=n.files.length;for(let r=0;r<t;r++){const t=n.files[r];let i;Object.defineProperty(t,"path",{get:()=>(i=i||Gh(t),i)}),r<e&&o.push(t)}}r({get tempFilePaths(){return o.map((({path:e})=>e))},tempFiles:o})})),Wv.click(),Dp()||console.warn(a("uni.chooseFile.notUserActivation"))}),0,sh);let zv=null;const Uv=Xd("chooseImage",(({count:e,sourceType:t,extension:n},{resolve:o,reject:r})=>{oc();const{t:i}=Jl();zv&&(document.body.removeChild(zv),zv=null),zv=qv({count:e,sourceType:t,extension:n,type:"image"}),document.body.appendChild(zv),zv.addEventListener("change",(function(t){const n=t.target,r=[];if(n&&n.files){const t=n.files.length;for(let o=0;o<t;o++){const t=n.files[o];let i;Object.defineProperty(t,"path",{get:()=>(i=i||Gh(t),i)}),o<e&&r.push(t)}}o({get tempFilePaths(){return r.map((({path:e})=>e))},tempFiles:r})})),zv.click(),Dp()||console.warn(i("uni.chooseFile.notUserActivation"))}),0,rh),Yv={esc:["Esc","Escape"],enter:["Enter"]},Xv=Object.keys(Yv);function Gv(){const e=An(""),t=An(!1),n=n=>{if(t.value)return;const o=Xv.find((e=>-1!==Yv[e].indexOf(n.key)));o&&(e.value=o),eo((()=>e.value=""))};return lr((()=>{document.addEventListener("keyup",n)})),dr((()=>{document.removeEventListener("keyup",n)})),{key:e,disable:t}}const Jv=Ei("div",{class:"uni-mask"},null,-1);function Kv(e,t,n){return t.onClose=(...e)=>(t.visible=!1,n.apply(null,e)),Ya(Vo({setup:()=>()=>(gi(),_i(e,t,null,16))}))}function Zv(e){let t=document.getElementById(e);return t||(t=document.createElement("div"),t.id=e,document.body.append(t)),t}function Qv(e,{onEsc:t,onEnter:n}){const o=An(e.visible),{key:r,disable:i}=Gv();return Co((()=>e.visible),(e=>o.value=e)),Co((()=>o.value),(e=>i.value=!e)),ko((()=>{const{value:e}=r;"esc"===e?t&&t():"enter"===e&&n&&n()})),o}let ey=0,ty="";function ny(e){let t=ey;ey+=e?1:-1,ey=Math.max(0,ey),ey>0?0===t&&(ty=document.body.style.overflow,document.body.style.overflow="hidden"):(document.body.style.overflow=ty,ty="")}const oy=ju({name:"ImageView",props:{src:{type:String,default:""}},setup(e){const t=mn({direction:"none"});let n=1,o=0,r=0,i=0,a=0;function s({detail:e}){n=e.scale}function l(e){const t=e.target.getBoundingClientRect();o=t.width,r=t.height}function c(e){const t=e.target.getBoundingClientRect();i=t.width,a=t.height,d(e)}function u(e){const s=n*o>i,l=n*r>a;t.direction=s&&l?"all":s?"horizontal":l?"vertical":"none",d(e)}function d(e){"all"!==t.direction&&"horizontal"!==t.direction||e.stopPropagation()}return()=>{const n={position:"absolute",left:"0",top:"0",width:"100%",height:"100%"};return Ei(eg,{style:n,onTouchstart:Fu(c),onTouchmove:Fu(d),onTouchend:Fu(u)},{default:()=>[Ei(hg,{style:n,direction:t.direction,inertia:!0,scale:!0,"scale-min":"1","scale-max":"4",onScale:s},{default:()=>[Ei("img",{src:e.src,style:{position:"absolute",left:"50%",top:"50%",transform:"translate(-50%, -50%)",maxHeight:"100%",maxWidth:"100%"},onLoad:l},null,40,["src","onLoad"])]},8,["style","direction","inertia","scale","onScale"])]},8,["style","onTouchstart","onTouchmove","onTouchend"])}}});function ry(e){let t="number"==typeof e.current?e.current:e.urls.indexOf(e.current);return t=t<0?0:t,t}const iy=ju({name:"ImagePreview",props:{urls:{type:Array,default:()=>[]},current:{type:[Number,String],default:0}},emits:["close"],setup(e,{emit:t}){lr((()=>ny(!0))),fr((()=>ny(!1)));const n=An(null),o=An(ry(e));let r;function i(){r||eo((()=>{t("close")}))}function a(e){o.value=e.detail.current}Co((()=>e.current),(()=>o.value=ry(e))),lr((()=>{const e=n.value;let t=0,o=0;e.addEventListener("mousedown",(e=>{r=!1,t=e.clientX,o=e.clientY})),e.addEventListener("mouseup",(e=>{(Math.abs(e.clientX-t)>20||Math.abs(e.clientY-o)>20)&&(r=!0)}))}));const s={position:"absolute","box-sizing":"border-box",top:"0",right:"0",width:"60px",height:"44px",padding:"6px","line-height":"32px","font-size":"26px",color:"white","text-align":"center",cursor:"pointer"};return()=>{let t;return Ei("div",{ref:n,style:{display:"block",position:"fixed",left:"0",top:"0",width:"100%",height:"100%",zIndex:999,background:"rgba(0,0,0,0.8)"},onClick:i},[Ei(Ug,{navigation:"auto",current:o.value,onChange:a,"indicator-dots":!1,autoplay:!1,style:{position:"absolute",left:"0",top:"0",width:"100%",height:"100%"}},(r=t=e.urls.map((e=>Ei(Xg,null,{default:()=>[Ei(oy,{src:e},null,8,["src"])]}))),"function"==typeof r||"[object Object]"===Object.prototype.toString.call(r)&&!wi(r)?t:{default:()=>[t],_:1}),8,["current","onChange"]),Ei("div",{style:s},[Yc("M17.25 16.156l7.375-7.313q0.281-0.281 0.281-0.641t-0.281-0.641q-0.25-0.25-0.625-0.25t-0.625 0.25l-7.375 7.344-7.313-7.344q-0.25-0.25-0.625-0.25t-0.625 0.25q-0.281 0.25-0.281 0.625t0.281 0.625l7.313 7.344-7.375 7.344q-0.281 0.25-0.281 0.625t0.281 0.625q0.125 0.125 0.281 0.188t0.344 0.063q0.156 0 0.328-0.063t0.297-0.188l7.375-7.344 7.375 7.406q0.125 0.156 0.297 0.219t0.328 0.063q0.188 0 0.344-0.078t0.281-0.203q0.281-0.25 0.281-0.609t-0.281-0.641l-7.375-7.406z","#ffffff",26)],4)],8,["onClick"]);var r}}});let ay,sy=null;const ly=()=>{sy=null,eo((()=>{null==ay||ay.unmount(),ay=null}))},cy=Xd("previewImage",((e,{resolve:t})=>{sy?x(sy,e):(sy=mn(e),eo((()=>{ay=Kv(iy,sy,ly),ay.mount(Zv("u-a-p"))}))),t()}),0,ch);let uy=null;const dy=Xd("chooseVideo",(({sourceType:e,extension:t},{resolve:n,reject:o})=>{oc();const{t:r}=Jl();uy&&(document.body.removeChild(uy),uy=null),uy=qv({sourceType:e,extension:t,type:"video"}),document.body.appendChild(uy),uy.addEventListener("change",(function(e){const t=e.target.files[0];let o="";const r={tempFilePath:o,tempFile:t,size:t.size,duration:0,width:0,height:0,name:t.name};Object.defineProperty(r,"tempFilePath",{get(){return o=o||Gh(this.tempFile),o}});const i=document.createElement("video");if(void 0!==i.onloadedmetadata){const e=Gh(t);i.onloadedmetadata=function(){Jh(e),n(x(r,{duration:i.duration||0,width:i.videoWidth||0,height:i.videoHeight||0}))},setTimeout((()=>{i.onloadedmetadata=null,Jh(e),n(r)}),300),i.src=e}else n(r)})),uy.click(),Dp()||console.warn(r("uni.chooseFile.notUserActivation"))}),0,ih),fy=Ud("request",(({url:e,data:t,header:n,method:o,dataType:r,responseType:i,withCredentials:a,timeout:s=__uniConfig.networkTimeout.request},{resolve:l,reject:c})=>{let u=null;const d=function(e){const t=Object.keys(e).find((e=>"content-type"===e.toLowerCase()));if(!t)return;const n=e[t];if(0===n.indexOf("application/json"))return"json";if(0===n.indexOf("application/x-www-form-urlencoded"))return"urlencoded";return"string"}(n);if("GET"!==o)if(I(t)||t instanceof ArrayBuffer)u=t;else if("json"===d)try{u=JSON.stringify(t)}catch(g){u=t.toString()}else if("urlencoded"===d){const e=[];for(const n in t)S(t,n)&&e.push(encodeURIComponent(n)+"="+encodeURIComponent(t[n]));u=e.join("&")}else u=t.toString();const f=new XMLHttpRequest,h=new hy(f);f.open(o,e);for(const m in n)S(n,m)&&f.setRequestHeader(m,n[m]);const p=setTimeout((function(){f.onload=f.onabort=f.onerror=null,h.abort(),c("timeout")}),s);return f.responseType=i,f.onload=function(){clearTimeout(p);const e=f.status;let t="text"===i?f.responseText:f.response;if("text"===i&&"json"===r)try{t=JSON.parse(t)}catch(g){}l({data:t,statusCode:e,header:py(f.getAllResponseHeaders()),cookies:[]})},f.onabort=function(){clearTimeout(p),c("abort")},f.onerror=function(){clearTimeout(p),c()},f.withCredentials=a,f.send(u),h}),0,hh);class hy{constructor(e){this._xhr=e}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}function py(e){const t={};return e.split(Z).forEach((e=>{const n=e.match(/(\S+\s*):\s*(.*)/);n&&3===n.length&&(t[n[1]]=n[2])})),t}class gy{constructor(e){this._callbacks=[],this._xhr=e}onProgressUpdate(e){M(e)&&this._callbacks.push(e)}offProgressUpdate(e){const t=this._callbacks.indexOf(e);t>=0&&this._callbacks.splice(t,1)}abort(){this._isAbort=!0,this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}const my=Ud("uploadFile",(({url:e,file:t,filePath:n,name:o,files:r,header:i,formData:a,timeout:s=__uniConfig.networkTimeout.uploadFile},{resolve:l,reject:c})=>{var u=new gy;return C(r)&&r.length||(r=[{name:o,file:t,uri:n}]),Promise.all(r.map((({file:e,uri:t})=>e instanceof Blob?Promise.resolve(Xh(e)):Yh(t)))).then((function(t){var n,o=new XMLHttpRequest,d=new FormData;Object.keys(a).forEach((e=>{d.append(e,a[e])})),Object.values(r).forEach((({name:e},n)=>{const o=t[n];d.append(e||"file",o,o.name||`file-${Date.now()}`)})),o.open("POST",e),Object.keys(i).forEach((e=>{o.setRequestHeader(e,i[e])})),o.upload.onprogress=function(e){u._callbacks.forEach((t=>{var n=e.loaded,o=e.total;t({progress:Math.round(n/o*100),totalBytesSent:n,totalBytesExpectedToSend:o})}))},o.onerror=function(){clearTimeout(n),c()},o.onabort=function(){clearTimeout(n),c("abort")},o.onload=function(){clearTimeout(n);const e=o.status;l({statusCode:e,data:o.responseText||o.response})},u._isAbort?c("abort"):(n=setTimeout((function(){o.upload.onprogress=o.onload=o.onabort=o.onerror=null,u.abort(),c("timeout")}),s),o.send(d),u._xhr=o)})).catch((()=>{setTimeout((()=>{c("file error")}),0)})),u}),0,ph),vy=Xd("navigateBack",((e,{resolve:t,reject:n})=>{let o=!0;return!0===nu(pe,{from:e.from||"navigateBack"})&&(o=!1),o?(ov().$router.go(-e.delta),t()):n(pe)}),0,Sh);function yy({type:e,url:t,tabBarText:n,events:o},r){const i=ov().$router,{path:a,query:s}=function(e){const[t,n]=e.split("?",2);return{path:t,query:Ge(n||"")}}(t);return new Promise(((t,l)=>{const c=function(e,t){return{__id__:t||++Rm,__type__:e}}(e,r);i["navigateTo"===e?"push":"replace"]({path:a,query:s,state:c,force:!0}).then((r=>{if(sl(r))return l(r.message);if("switchTab"===e&&(i.currentRoute.value.meta.tabBarText=n),"navigateTo"===e){const e=i.currentRoute.value.meta;return e.eventChannel?o&&(Object.keys(o).forEach((t=>{e.eventChannel._addListener(t,"on",o[t])})),e.eventChannel._clearCache()):e.eventChannel=new Ke(c.__id__,o),t({eventChannel:e.eventChannel})}return t()}))}))}const by=Xd(mh,(({url:e,events:t},{resolve:n,reject:o})=>yy({type:mh,url:e,events:t}).then(n).catch(o)),0,wh);const _y=Xd(vh,(({url:e},{resolve:t,reject:n})=>(function(){const e=Jc();if(!e)return;const t=e.$page;Lm(Dm(t.path,t.id))}(),yy({type:vh,url:e}).then(t).catch(n))),0,xh);const wy=Xd(yh,(({url:e},{resolve:t,reject:n})=>(function(){const e=Pm().keys();for(const t of e)Lm(t)}(),yy({type:yh,url:e}).then(t).catch(n))),0,Th);function xy(e,t){return e===t.fullPath||"/"===e&&t.meta.isEntry}const Ty=Xd(bh,(({url:e,tabBarText:t},{resolve:n,reject:o})=>(function(){const e=Qc();if(!e)return;const t=Pm(),n=t.keys();for(const o of n){const e=t.get(o);e.$.__isTabBar?e.$.__isActive=!1:Lm(o)}e.$.__isTabBar&&(e.$.__isVisible=!1,nu(e,oe))}(),yy({type:bh,url:e,tabBarText:t},function(e){const t=Pm().values();for(const n of t){const t=n.$page;if(xy(e,t))return n.$.__isActive=!0,t.id}}(e)).then(n).catch(o))),0,kh);function ky(e){__uniConfig.darkmode&&Hb.on(ae,e)}function Sy(e){Hb.off(ae,e)}function Cy(e){let t={};return __uniConfig.darkmode&&(t=at(e,__uniConfig.themeConfig,_v())),__uniConfig.darkmode?t:e}const Ey={light:{cancelColor:"#000000"},dark:{cancelColor:"rgb(170, 170, 170)"}},Oy=Vo({props:{title:{type:String,default:""},content:{type:String,default:""},showCancel:{type:Boolean,default:!0},cancelText:{type:String,default:"Cancel"},cancelColor:{type:String,default:"#000000"},confirmText:{type:String,default:"OK"},confirmColor:{type:String,default:"#007aff"},visible:{type:Boolean},editable:{type:Boolean,default:!1},placeholderText:{type:String,default:""}},setup(e,{emit:t}){const n=An(""),o=()=>a.value=!1,r=()=>(o(),t("close","cancel")),i=()=>(o(),t("close","confirm",n.value)),a=Qv(e,{onEsc:r,onEnter:()=>{!e.editable&&i()}}),s=function(e){const t=An(e.cancelColor),n=({theme:e})=>{((e,t)=>{t.value=Ey[e].cancelColor})(e,t)};return ko((()=>{e.visible?(t.value=e.cancelColor,"#000"===e.cancelColor&&("dark"===_v()&&n({theme:"dark"}),ky(n))):Sy(n)})),t}(e);return()=>{const{title:t,content:o,showCancel:l,confirmText:c,confirmColor:u,editable:d,placeholderText:f}=e;return n.value=o,Ei(Oa,{name:"uni-fade"},{default:()=>[vr(Ei("uni-modal",{onTouchmove:Rc},[Jv,Ei("div",{class:"uni-modal"},[t&&Ei("div",{class:"uni-modal__hd"},[Ei("strong",{class:"uni-modal__title",textContent:t},null,8,["textContent"])]),d?Ei("textarea",{class:"uni-modal__textarea",rows:"1",placeholder:f,value:o,onInput:e=>n.value=e.target.value},null,40,["placeholder","value","onInput"]):Ei("div",{class:"uni-modal__bd",onTouchmovePassive:jc,textContent:o},null,40,["onTouchmovePassive","textContent"]),Ei("div",{class:"uni-modal__ft"},[l&&Ei("div",{style:{color:s.value},class:"uni-modal__btn uni-modal__btn_default",onClick:r},[e.cancelText],12,["onClick"]),Ei("div",{style:{color:u},class:"uni-modal__btn uni-modal__btn_primary",onClick:i},[c],12,["onClick"])])])],40,["onTouchmove"]),[[Wa,a.value]])]})}}});let My;const Iy=Be((()=>{Hb.on("onHidePopup",(()=>My.visible=!1))}));let Ay;function Py(e,t){const n="confirm"===e,o={confirm:n,cancel:"cancel"===e};n&&My.editable&&(o.content=t),Ay&&Ay(o)}const $y=Xd("showModal",((e,{resolve:t})=>{Iy(),Ay=t,My?(x(My,e),My.visible=!0):(My=mn(e),eo((()=>(Kv(Oy,My,Py).mount(Zv("u-a-m")),eo((()=>My.visible=!0))))))}),0,Bh),Ly={title:{type:String,default:""},icon:{default:"success",validator:e=>-1!==Dh.indexOf(e)},image:{type:String,default:""},duration:{type:Number,default:1500},mask:{type:Boolean,default:!1},visible:{type:Boolean}},Ry="uni-toast__icon",jy={light:"#fff",dark:"rgba(255,255,255,0.9)"},By=e=>jy[e],Dy=Vo({name:"Toast",props:Ly,setup(e){ec(),tc();const{Icon:t}=function(e){const t=An(By(_v())),n=({theme:e})=>t.value=By(e);ko((()=>{e.visible?ky(n):Sy(n)}));const o=Gi((()=>{switch(e.icon){case"success":return Ei(Yc(zc,t.value,38),{class:Ry});case"error":return Ei(Yc(Uc,t.value,38),{class:Ry});case"loading":return Ei("i",{class:[Ry,"uni-loading"]},null,2);default:return null}}));return{Icon:o}}(e),n=Qv(e,{});return()=>{const{mask:o,duration:r,title:i,image:a}=e;return Ei(Oa,{name:"uni-fade"},{default:()=>[vr(Ei("uni-toast",{"data-duration":r},[o?Ei("div",{class:"uni-mask",style:"background: transparent;",onTouchmove:Rc},null,40,["onTouchmove"]):"",a||t.value?Ei("div",{class:"uni-toast"},[a?Ei("img",{src:a,class:Ry},null,10,["src"]):t.value,Ei("p",{class:"uni-toast__content"},[i])]):Ei("div",{class:"uni-sample-toast"},[Ei("p",{class:"uni-simple-toast__text"},[i])])],8,["data-duration"]),[[Wa,n.value]])]})}}});let Ny,Fy,Vy="";const qy=ct();function Wy(e){Ny?x(Ny,e):(Ny=mn(x(e,{visible:!1})),eo((()=>{qy.run((()=>{Co([()=>Ny.visible,()=>Ny.duration],(([e,t])=>{if(e){if(Fy&&clearTimeout(Fy),"onShowLoading"===Vy)return;Fy=setTimeout((()=>{Xy("onHideToast")}),t)}else Fy&&clearTimeout(Fy)}))})),Hb.on("onHidePopup",(()=>Xy("onHidePopup"))),Kv(Dy,Ny,(()=>{})).mount(Zv("u-a-t"))}))),setTimeout((()=>{Ny.visible=!0}),10)}const Hy=Xd("showToast",((e,{resolve:t,reject:n})=>{Wy(e),Vy="onShowToast",t()}),0,Nh),zy={icon:"loading",duration:1e8,image:""},Uy=Xd("showLoading",((e,{resolve:t,reject:n})=>{x(e,zy),Wy(e),Vy="onShowLoading",t()}),0,jh),Yy=Xd("hideLoading",((e,{resolve:t,reject:n})=>{Xy("onHideLoading"),t()}));function Xy(e){const{t:t}=Jl();if(!Vy)return;let n="";if("onHideToast"===e&&"onShowToast"!==Vy?n=t("uni.showToast.unpaired"):"onHideLoading"===e&&"onShowLoading"!==Vy&&(n=t("uni.showLoading.unpaired")),n)return console.warn(n);Vy="",setTimeout((()=>{Ny.visible=!1}),10)}function Gy(e){const t=An(0),n=An(0),o=Gi((()=>t.value>=500&&n.value>=500)),r=Gi((()=>{const t={content:{transform:"",left:"",top:"",bottom:""},triangle:{left:"",top:"",bottom:"","border-width":"","border-color":""}},r=t.content,i=t.triangle,a=e.popover;function s(e){return Number(e)||0}if(o.value&&a){x(i,{position:"absolute",width:"0",height:"0","margin-left":"-6px","border-style":"solid"});const e=s(a.left),t=s(a.width),o=s(a.top),l=s(a.height),c=e+t/2;r.transform="none !important";const u=Math.max(0,c-150);r.left=`${u}px`;let d=Math.max(12,c-u);d=Math.min(288,d),i.left=`${d}px`;const f=n.value/2;o+l-f>f-o?(r.top="auto",r.bottom=n.value-o+6+"px",i.bottom="-6px",i["border-width"]="6px 6px 0 6px",i["border-color"]="#fcfcfd transparent transparent transparent"):(r.top=`${o+l+6}px`,i.top="-6px",i["border-width"]="0 6px 6px 6px",i["border-color"]="transparent transparent #fcfcfd transparent")}return t}));return lr((()=>{const e=()=>{const{windowWidth:e,windowHeight:o,windowTop:r}=Ov();t.value=e,n.value=o+(r||0)};window.addEventListener("resize",e),e(),fr((()=>{window.removeEventListener("resize",e)}))})),{isDesktop:o,popupStyle:r}}const Jy={light:{listItemColor:"#000000",cancelItemColor:"#000000"},dark:{listItemColor:"rgba(255, 255, 255, 0.8)",cancelItemColor:"rgba(255, 255, 255)"}};const Ky=Vo({name:"ActionSheet",props:{title:{type:String,default:""},itemList:{type:Array,default:()=>[]},itemColor:{type:String,default:"#000000"},popover:{type:Object,default:null},visible:{type:Boolean,default:!1}},emits:["close"],setup(e,{emit:t}){Ql();const n=An(260),o=An(0),r=An(0),i=An(0),a=An(0),s=An(null),l=An(null),{t:c}=Jl(),{_close:u}=function(e,t){function n(e){t("close",e)}const{key:o,disable:r}=Gv();return Co((()=>e.visible),(e=>r.value=!e)),ko((()=>{const{value:e}=o;"esc"===e&&n&&n(-1)})),{_close:n}}(e,t),{popupStyle:d}=Gy(e);let f;function h(e){const t=i.value+e.deltaY;Math.abs(t)>10?(a.value+=t/3,a.value=a.value>=o.value?o.value:a.value<=0?0:a.value,f.scrollTo(a.value)):i.value=t,e.preventDefault()}lr((()=>{const{scroller:e,handleTouchStart:t,handleTouchMove:n,handleTouchEnd:o}=Pg(s.value,{enableY:!0,friction:new Cg(1e-4),spring:new Mg(2,90,20),onScroll:e=>{a.value=e.target.scrollTop}});f=e,ig(s.value,(r=>{if(e)switch(r.detail.state){case"start":t(r);break;case"move":n(r);break;case"end":case"cancel":o(r)}}),!0)})),Co((()=>e.visible),(()=>{eo((()=>{e.title&&(r.value=document.querySelector(".uni-actionsheet__title").offsetHeight),f.update(),s.value&&(o.value=s.value.clientHeight-n.value),document.querySelectorAll(".uni-actionsheet__cell").forEach((e=>{!function(e){const t=20;let n=0,o=0;e.addEventListener("touchstart",(e=>{const t=e.changedTouches[0];n=t.clientX,o=t.clientY})),e.addEventListener("touchend",(e=>{const r=e.changedTouches[0];if(Math.abs(r.clientX-n)<t&&Math.abs(r.clientY-o)<t){const t=e.target,n=e.currentTarget,o=new CustomEvent("click",{bubbles:!0,cancelable:!0,target:t,currentTarget:n});["screenX","screenY","clientX","clientY","pageX","pageY"].forEach((e=>{o[e]=r[e]})),e.target.dispatchEvent(o)}}))}(e)}))}))}));const p=function(e){const t=mn({listItemColor:"#000",cancelItemColor:"#000"}),n=({theme:e})=>{!function(e,t){["listItemColor","cancelItemColor"].forEach((n=>{t[n]=Jy[e][n]}))}(e,t)};return ko((()=>{e.visible?(t.listItemColor=t.cancelItemColor=e.itemColor,"#000"===e.itemColor&&(n({theme:_v()}),ky(n))):Sy(n)})),t}(e);return()=>Ei("uni-actionsheet",{onTouchmove:Rc},[Ei(Oa,{name:"uni-fade"},{default:()=>[vr(Ei("div",{class:"uni-mask uni-actionsheet__mask",onClick:()=>u(-1)},null,8,["onClick"]),[[Wa,e.visible]])]}),Ei("div",{class:["uni-actionsheet",{"uni-actionsheet_toggle":e.visible}],style:d.value.content},[Ei("div",{ref:l,class:"uni-actionsheet__menu",onWheel:h},[e.title?Ei(ci,null,[Ei("div",{class:"uni-actionsheet__cell",style:{height:`${r.value}px`}},null),Ei("div",{class:"uni-actionsheet__title"},[e.title])]):"",Ei("div",{style:{maxHeight:`${n.value}px`,overflow:"hidden"}},[Ei("div",{ref:s},[e.itemList.map(((e,t)=>Ei("div",{key:t,style:{color:p.listItemColor},class:"uni-actionsheet__cell",onClick:()=>u(t)},[e],12,["onClick"])))],512)])],40,["onWheel"]),Ei("div",{class:"uni-actionsheet__action"},[Ei("div",{style:{color:p.cancelItemColor},class:"uni-actionsheet__cell",onClick:()=>u(-1)},[c("uni.showActionSheet.cancel")],12,["onClick"])]),Ei("div",{style:d.value.triangle},null,4)],6)],40,["onTouchmove"])}});let Zy,Qy,eb;const tb=Be((()=>{Hb.on("onHidePopup",(()=>eb.visible=!1))}));function nb(e){-1===e?Qy&&Qy("cancel"):Zy&&Zy({tapIndex:e})}const ob=Xd("showActionSheet",((e,{resolve:t,reject:n})=>{tb(),Zy=t,Qy=n,eb?(x(eb,e),eb.visible=!0):(eb=mn(e),eo((()=>(Kv(Ky,eb,nb).mount(Zv("u-s-a-s")),eo((()=>eb.visible=!0))))))}),0,Rh),rb=Xd("loadFontFace",(({family:e,source:t,desc:n},{resolve:o,reject:r})=>{(function(e,t,n){const o=document.fonts;if(o){const r=new FontFace(e,t,n);return r.load().then((()=>{o.add&&o.add(r)}))}return new Promise((o=>{const r=document.createElement("style"),i=[];if(n){const{style:e,weight:t,stretch:o,unicodeRange:r,variant:a,featureSettings:s}=n;e&&i.push(`font-style:${e}`),t&&i.push(`font-weight:${t}`),o&&i.push(`font-stretch:${o}`),r&&i.push(`unicode-range:${r}`),a&&i.push(`font-variant:${a}`),s&&i.push(`font-feature-settings:${s}`)}r.innerText=`@font-face{font-family:"${e}";src:${t};${i.join(";")}}`,document.head.appendChild(r),o()}))})(e,t,n).then((()=>{o()})).catch((e=>{r(`loadFontFace:fail ${e}`)}))}));function ib(e){function t(){var t;t=e.navigationBar.titleText,document.title=t,Hb.emit("onNavigationBarChange",{titleText:t})}ko(t),Jo(t)}function ab(e,t,n,o,r){if(!e)return r("page not found");const{navigationBar:i}=e;switch(t){case Ah:const{frontColor:e,backgroundColor:t,animation:o}=n,{duration:r,timingFunc:a}=o;e&&(i.titleColor="#000000"===e?"#000000":"#ffffff"),t&&(i.backgroundColor=t),i.duration=r+"ms",i.timingFunc=a;break;case"showNavigationBarLoading":i.loading=!0;break;case"hideNavigationBarLoading":i.loading=!1;break;case $h:const{title:s}=n;i.titleText=s}o()}const sb=Xd(Ah,((e,{resolve:t,reject:n})=>{ab(Kc(),Ah,e,t,n)}),0,Ph),lb=Xd($h,((e,{resolve:t,reject:n})=>{ab(Kc(),$h,e,t,n)})),cb=Xd("pageScrollTo",(({scrollTop:e,selector:t,duration:n},{resolve:o})=>{!function(e,t,n){if(I(e)){const t=document.querySelector(e);if(t){const{height:o,top:r}=t.getBoundingClientRect();e=r+window.pageYOffset,n&&(e-=o)}}e<0&&(e=0);const o=document.documentElement,{clientHeight:r,scrollHeight:i}=o;if(e=Math.min(e,i-r),0===t)return void(o.scrollTop=document.body.scrollTop=e);if(window.scrollY===e)return;const a=t=>{if(t<=0)return void window.scrollTo(0,e);const n=e-window.scrollY;requestAnimationFrame((function(){window.scrollTo(0,window.scrollY+n/t*10),a(t-10)}))};a(t)}(t||e||0,n,!0),o()}),0,Lh),ub=Xd(Fh,((e,{resolve:t})=>{Hb.invokeViewMethod(Fh,{},Zc()),t()})),db=["text","iconPath","iconfont","selectedIconPath","visible"],fb=["color","selectedColor","backgroundColor","borderStyle","midButton"],hb=["badge","redDot"];function pb(e,t,n){t.forEach((function(t){S(n,t)&&(e[t]=n[t])}))}function gb(e,t,n){const o=xm();switch(e){case"showTabBar":o.shown=!0;break;case Vh:o.shown=!1;break;case"setTabBarItem":const{index:e}=t,n=o.list[e],r=n.pagePath;pb(n,db,t);const{pagePath:i}=t;if(i){const t=Le(i);t!==r&&function(e,t,n){const o=lu(Le(t));if(o){const{meta:e}=o;delete e.tabBarIndex,e.isQuit=e.isTabBar=!1}const r=lu(Le(n));if(r){const{meta:t}=r;t.tabBarIndex=e,t.isQuit=t.isTabBar=!0;const o=__uniConfig.tabBar;o&&o.list&&o.list[e]&&(o.list[e].pagePath=Re(n))}}(e,r,t)}break;case"setTabBarStyle":pb(o,fb,t);break;case"showTabBarRedDot":pb(o.list[t.index],hb,{badge:"",redDot:!0});break;case"setTabBarBadge":pb(o.list[t.index],hb,{badge:t.text,redDot:!0});break;case"hideTabBarRedDot":case"removeTabBarBadge":pb(o.list[t.index],hb,{badge:"",redDot:!1})}n()}const mb=Xd(Vh,((e,{resolve:t})=>{gb(Vh,e||{},t)})),vb=ju({name:"TabBar",setup(){const e=An([]),t=xm(),n=mn(Cy(t));!function(e,t){function n(){let n=[];n=e.list.filter((e=>!1!==e.visible)),t.value=n}An(x({type:"midButton"},e.midButton)),ko(n)}(n,e),function(e){Co((()=>e.shown),(t=>{Vc({"--window-bottom":Mm(t?parseInt(e.height):0)})}))}(n);const o=function(e,t,n){return ko((()=>{const o=e.meta;if(o.isTabBar){const e=o.route,r=n.value.findIndex((t=>t.pagePath===e));t.selectedIndex=r}})),(t,n)=>()=>{const{pagePath:o,text:r}=t;let i=Le(o);i===__uniRoutes[0].alias&&(i="/"),e.path!==i?Ty({from:"tabBar",url:i,tabBarText:r}):nu("onTabItemTap",{index:n,text:r,pagePath:o})}}(Hl(),n,e),{style:r,borderStyle:i,placeholderStyle:a}=function(e){const t=Gi((()=>{let t=e.backgroundColor;const n=e.blurEffect;return t||Em&&n&&"none"!==n&&(t=wb[n]),{backgroundColor:t||yb,backdropFilter:"none"!==n?"blur(10px)":n}})),n=Gi((()=>{const{borderStyle:t}=e;return{backgroundColor:xb[t]||t}})),o=Gi((()=>({height:e.height})));return{style:t,borderStyle:n,placeholderStyle:o}}(n);return ky((()=>{const e=Cy(t);n.backgroundColor=e.backgroundColor,n.borderStyle=e.borderStyle,n.color=e.color,n.selectedColor=e.selectedColor,n.blurEffect=e.blurEffect,e.list&&e.list.length&&e.list.forEach(((e,t)=>{n.list[t].iconPath=e.iconPath,n.list[t].selectedIconPath=e.selectedIconPath}))})),lr((()=>{n.iconfontSrc&&rb({family:"UniTabbarIconFont",source:`url("${n.iconfontSrc}")`})})),()=>{const t=function(e,t,n){const{selectedIndex:o,selectedColor:r,color:i}=e;return n.value.map(((n,a)=>{const s=o===a;return function(e,t,n,o,r,i,a,s){return Ei("div",{key:a,class:"uni-tabbar__item",onClick:s(r,a)},[Tb(e,t||"",n,o,r,i)],8,["onClick"])}(s?r:i,s&&n.selectedIconPath||n.iconPath||"",n.iconfont?s&&n.iconfont.selectedText||n.iconfont.text:void 0,n.iconfont?s&&n.iconfont.selectedColor||n.iconfont.color:void 0,n,e,a,t)}))}(n,o,e);return Ei("uni-tabbar",{class:"uni-tabbar-"+n.position},[Ei("div",{class:"uni-tabbar",style:r.value},[Ei("div",{class:"uni-tabbar-border",style:i.value},null,4),t],4),Ei("div",{class:"uni-placeholder",style:a.value},null,4)],2)}}});const yb="#f7f7fa",bb="rgb(0, 0, 0, 0.8)",_b="rgb(250, 250, 250, 0.8)",wb={dark:bb,light:_b,extralight:_b},xb={white:"rgba(255, 255, 255, 0.33)",black:"rgba(0, 0, 0, 0.33)"};function Tb(e,t,n,o,r,i){const{height:a}=i;return Ei("div",{class:"uni-tabbar__bd",style:{height:a}},[n?Sb(n,o||bb,r,i):t&&kb(t,r,i),r.text&&Cb(e,r,i),r.redDot&&Eb(r.badge)],4)}function kb(e,t,n){const{type:o,text:r}=t,{iconWidth:i}=n;return Ei("div",{class:"uni-tabbar__icon"+(r?" uni-tabbar__icon__diff":""),style:{width:i,height:i}},["midButton"!==o&&Ei("img",{src:Qu(e)},null,8,["src"])],6)}function Sb(e,t,n,o){var r;const{type:i,text:a}=n,{iconWidth:s}=o,l="uni-tabbar__icon"+(a?" uni-tabbar__icon__diff":""),c={width:s,height:s},u={fontSize:(null==(r=n.iconfont)?void 0:r.fontSize)||s,color:t};return Ei("div",{class:l,style:c},["midButton"!==i&&Ei("div",{class:"uni-tabbar__iconfont",style:u},[e],4)],6)}function Cb(e,t,n){const{iconPath:o,text:r}=t,{fontSize:i,spacing:a}=n;return Ei("div",{class:"uni-tabbar__label",style:{color:e,fontSize:i,lineHeight:o?"normal":1.8,marginTop:o?a:"inherit"}},[r],4)}function Eb(e){return Ei("div",{class:"uni-tabbar__reddot"+(e?" uni-tabbar__badge":"")},[e],2)}const Ob="0px",Mb=ju({name:"Layout",setup(e,{emit:t}){const n=An(null);Fc({"--status-bar-height":Ob,"--top-window-height":Ob,"--window-left":Ob,"--window-right":Ob,"--window-margin":Ob,"--tab-bar-height":Ob});const o=function(){const e=Hl();return{routeKey:Gi((()=>Dm("/"+e.meta.route,_m()))),isTabBar:Gi((()=>e.meta.isTabBar)),routeCache:Fm}}(),{layoutState:r,windowState:i}=function(){bm();{const e=mn({marginWidth:0,leftWindowWidth:0,rightWindowWidth:0});return Co((()=>e.marginWidth),(e=>Fc({"--window-margin":e+"px"}))),Co((()=>e.leftWindowWidth+e.marginWidth),(e=>{Fc({"--window-left":e+"px"})})),Co((()=>e.rightWindowWidth+e.marginWidth),(e=>{Fc({"--window-right":e+"px"})})),{layoutState:e,windowState:Gi((()=>({})))}}}();!function(e,t){const n=bm();function o(){const o=document.body.clientWidth,r=$m();let i={};if(r.length>0){i=r[r.length-1].$page.meta}else{const e=lu(n.path,!0);e&&(i=e.meta)}const a=parseInt(String((S(i,"maxWidth")?i.maxWidth:__uniConfig.globalStyle.maxWidth)||Number.MAX_SAFE_INTEGER));let s=!1;s=o>a,s&&a?(e.marginWidth=(o-a)/2,eo((()=>{const e=t.value;e&&e.setAttribute("style","max-width:"+a+"px;margin:0 auto;")}))):(e.marginWidth=0,eo((()=>{const e=t.value;e&&e.removeAttribute("style")})))}Co([()=>n.path],o),lr((()=>{o(),window.addEventListener("resize",o)}))}(r,n);const a=function(){const e=bm(),t=xm(),n=Gi((()=>e.meta.isTabBar&&t.shown));return Fc({"--tab-bar-height":t.height}),n}(),s=function(e){const t=An(!1);return Gi((()=>({"uni-app--showtabbar":e&&e.value,"uni-app--maxwidth":t.value})))}(a);return()=>{const e=function(e){const t=function({routeKey:e,isTabBar:t,routeCache:n}){return Ei(ql,null,{default:mo((({Component:o})=>[(gi(),_i(Xo,{matchBy:"key",cache:n},[(gi(),_i(xr(o),{type:t.value?"tabBar":"",key:e.value}))],1032,["cache"]))])),_:1})}(e);return t}(o),t=function(e){return vr(Ei(vb,null,null,512),[[Wa,e.value]])}(a);return Ei("uni-app",{ref:n,class:s.value},[e,t],2)}}});const Ib="scanCode",Ab=Xd(Ib,function(e){return(t,{reject:n})=>n(function(e){return`method 'uni.${e}' not supported`}(e))}(Ib));function Pb(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!wi(e)}function $b(e){if(e.mode===jb.TIME)return"00:00";if(e.mode===jb.DATE){const t=(new Date).getFullYear()-150;switch(e.fields){case Bb.YEAR:return t.toString();case Bb.MONTH:return t+"-01";default:return t+"-01-01"}}return""}function Lb(e){if(e.mode===jb.TIME)return"23:59";if(e.mode===jb.DATE){const t=(new Date).getFullYear()+150;switch(e.fields){case Bb.YEAR:return t.toString();case Bb.MONTH:return t+"-12";default:return t+"-12-31"}}return""}function Rb(e,t,n,o){const r=e.mode===jb.DATE?"-":":",i=e.mode===jb.DATE?t.dateArray:t.timeArray;let a;if(e.mode===jb.TIME)a=2;else switch(e.fields){case Bb.YEAR:a=1;break;case Bb.MONTH:a=2;break;default:a=3}const s=String(n).split(r);let l=[];for(let c=0;c<a;c++){const e=s[c];l.push(i[c].indexOf(e))}return l.indexOf(-1)>=0&&(l=o?Rb(e,t,o):l.map((()=>0))),l}const jb={SELECTOR:"selector",MULTISELECTOR:"multiSelector",TIME:"time",DATE:"date"},Bb={YEAR:"year",MONTH:"month",DAY:"day"},Db={PICKER:"picker",SELECT:"select"},Nb=Ru({name:"Picker",compatConfig:{MODE:3},props:{name:{type:String,default:""},range:{type:Array,default:()=>[]},rangeKey:{type:String,default:""},value:{type:[Number,String,Array],default:0},mode:{type:String,default:jb.SELECTOR,validator:e=>Object.values(jb).includes(e)},fields:{type:String,default:""},start:{type:String,default:e=>$b(e)},end:{type:String,default:e=>Lb(e)},disabled:{type:[Boolean,String],default:!1},selectorType:{type:String,default:""}},emits:["change","cancel","columnchange"],setup(e,{emit:t,slots:n}){ic();const{t:o}=Jl(),r=An(null),i=An(null),a=An(null),s=An(null),l=An(!1),{state:c,rangeArray:u}=function(e){const t=mn({valueSync:void 0,visible:!1,contentVisible:!1,popover:null,valueChangeSource:"",timeArray:[],dateArray:[],valueArray:[],oldValueArray:[],isDesktop:!1,popupStyle:{content:{},triangle:{}}}),n=Gi((()=>{let n=e.range;switch(e.mode){case jb.SELECTOR:return[n];case jb.MULTISELECTOR:return n;case jb.TIME:return t.timeArray;case jb.DATE:{const n=t.dateArray;switch(e.fields){case Bb.YEAR:return[n[0]];case Bb.MONTH:return[n[0],n[1]];default:return[n[0],n[1],n[2]]}}}return[]}));return{state:t,rangeArray:n}}(e),d=Vu(r,t),{system:f,selectorTypeComputed:h,_show:p,_l10nColumn:g,_l10nItem:m,_input:v,_fixInputPosition:y,_pickerViewChange:b,_cancel:_,_change:w,_resetFormData:x,_getFormData:T,_createTime:k,_createDate:S,_setValueSync:E}=function(e,t,n,o,r,i,a){const s=function(){const e=An(!1);return e.value=Fb(),e}(),l=function(){const e=An("");return e.value=Vb(),e}(),c=Gi((()=>{const t=e.selectorType;return Object.values(Db).includes(t)?t:s.value?Db.PICKER:Db.SELECT})),u=Gi((()=>e.mode===jb.DATE&&!Object.values(Bb).includes(e.fields)&&t.isDesktop?l.value:"")),d=Gi((()=>Rb(e,t,e.start,$b(e)))),f=Gi((()=>Rb(e,t,e.end,Lb(e))));function h(n){if(e.disabled)return;t.valueChangeSource="";let o=r.value,i=n.currentTarget;o.remove(),(document.querySelector("uni-app")||document.body).appendChild(o),o.style.display="block";const a=i.getBoundingClientRect();t.popover={top:a.top,left:a.left,width:a.width,height:a.height},setTimeout((()=>{t.visible=!0}),20)}function p(){return{value:t.valueSync,key:e.name}}function g(){switch(e.mode){case jb.SELECTOR:t.valueSync=0;break;case jb.MULTISELECTOR:t.valueSync=e.value.map((e=>0));break;case jb.DATE:case jb.TIME:t.valueSync=""}}function m(){let e=[],n=[];for(let t=0;t<24;t++)e.push((t<10?"0":"")+t);for(let t=0;t<60;t++)n.push((t<10?"0":"")+t);t.timeArray.push(e,n)}function v(){let t=(new Date).getFullYear(),n=t-150,o=t+150;if(e.start){const t=new Date(e.start).getFullYear();!isNaN(t)&&t<n&&(n=t)}if(e.end){const t=new Date(e.end).getFullYear();!isNaN(t)&&t>o&&(o=t)}return{start:n,end:o}}function y(){let e=[];const n=v();for(let t=n.start,i=n.end;t<=i;t++)e.push(String(t));let o=[];for(let t=1;t<=12;t++)o.push((t<10?"0":"")+t);let r=[];for(let t=1;t<=31;t++)r.push((t<10?"0":"")+t);t.dateArray.push(e,o,r)}function b(e){return 60*e[0]+e[1]}function _(e){const t=31;return e[0]*t*12+(e[1]||0)*t+(e[2]||0)}function w(e,t){for(let n=0;n<e.length&&n<t.length;n++)e[n]=t[n]}function x(){let n=e.value;switch(e.mode){case jb.MULTISELECTOR:{C(n)||(n=t.valueArray),C(t.valueSync)||(t.valueSync=[]);const o=t.valueSync.length=Math.max(n.length,e.range.length);for(let r=0;r<o;r++){const o=Number(n[r]),i=Number(t.valueSync[r]),a=isNaN(o)?isNaN(i)?0:i:o,s=e.range[r]?e.range[r].length-1:0;t.valueSync.splice(r,1,a<0||a>s?0:a)}}break;case jb.TIME:case jb.DATE:t.valueSync=String(n);break;default:{const e=Number(n);t.valueSync=e<0?0:e;break}}}function T(){let n,o=t.valueSync;switch(e.mode){case jb.MULTISELECTOR:n=[...o];break;case jb.TIME:n=Rb(e,t,o,Ne({mode:jb.TIME}));break;case jb.DATE:n=Rb(e,t,o,Ne({mode:jb.DATE}));break;default:n=[o]}t.oldValueArray=[...n],t.valueArray=[...n]}function k(){let n=t.valueArray;switch(e.mode){case jb.SELECTOR:return n[0];case jb.MULTISELECTOR:return n.map((e=>e));case jb.TIME:return t.valueArray.map(((e,n)=>t.timeArray[n][e])).join(":");case jb.DATE:return t.valueArray.map(((e,n)=>t.dateArray[n][e])).join("-")}}function S(){O(),t.valueChangeSource="click";const e=k();t.valueSync=C(e)?e.map((e=>e)):e,n("change",{},{value:e})}function E(e){if("firefox"===u.value&&e){const{top:n,left:o,width:r,height:i}=t.popover,{pageX:a,pageY:s}=e;if(a>o&&a<o+r&&s>n&&s<n+i)return}O(),n("cancel",{},{})}function O(){t.visible=!1,setTimeout((()=>{let e=r.value;e.remove(),o.value.prepend(e),e.style.display="none"}),260)}function M(){e.mode===jb.SELECTOR&&c.value===Db.SELECT&&(i.value.scrollTop=34*t.valueArray[0])}function I(e){const n=e.target;t.valueSync=n.value,eo((()=>{S()}))}function A(e){if("chrome"===u.value){const t=o.value.getBoundingClientRect(),n=32;a.value.style.left=e.clientX-t.left-1.5*n+"px",a.value.style.top=e.clientY-t.top-.5*n+"px"}}function P(e){t.valueArray=$(e.detail.value,!0)}function $(t,n){const{getLocale:o}=Jl();if(e.mode===jb.DATE){const r=o();if(!r.startsWith("zh"))switch(e.fields){case Bb.YEAR:return t;case Bb.MONTH:return[t[1],t[0]];default:switch(r){case"es":case"fr":return[t[2],t[1],t[0]];default:return n?[t[2],t[0],t[1]]:[t[1],t[2],t[0]]}}}return t}function L(t,n){const{getLocale:o}=Jl();if(e.mode===jb.DATE){const r=o();if(r.startsWith("zh")){return t+["年","月","日"][n]}if(e.fields!==Bb.YEAR&&n===(e.fields===Bb.MONTH||"es"!==r&&"fr"!==r?0:1)){let e;switch(r){case"es":e=["enero","febrero","marzo","abril","mayo","junio","​​julio","agosto","septiembre","octubre","noviembre","diciembre"];break;case"fr":e=["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"];break;default:e=["January","February","March","April","May","June","July","August","September","October","November","December"]}return e[Number(t)-1]}}return t}return Co((()=>t.visible),(e=>{e?(clearTimeout(qb),t.contentVisible=e,M()):qb=setTimeout((()=>{t.contentVisible=e}),300)})),Co([()=>e.mode,()=>e.value,()=>e.range],x,{deep:!0}),Co((()=>t.valueSync),T,{deep:!0}),Co((()=>t.valueArray),(o=>{if(e.mode===jb.TIME||e.mode===jb.DATE){const n=e.mode===jb.TIME?b:_,o=t.valueArray,r=d.value,i=f.value;if(e.mode===jb.DATE){const e=t.dateArray,n=e[2].length,r=Number(e[2][o[2]])||1,i=new Date(`${e[0][o[0]]}/${e[1][o[1]]}/${r}`).getDate();i<r&&(o[2]-=i+n-r)}n(o)<n(r)?w(o,r):n(o)>n(i)&&w(o,i)}o.forEach(((o,r)=>{o!==t.oldValueArray[r]&&(t.oldValueArray[r]=o,e.mode===jb.MULTISELECTOR&&n("columnchange",{},{column:r,value:o}))}))})),{selectorTypeComputed:c,system:u,_show:h,_cancel:E,_change:S,_l10nColumn:$,_l10nItem:L,_input:I,_resetFormData:g,_getFormData:p,_createTime:m,_createDate:y,_setValueSync:x,_fixInputPosition:A,_pickerViewChange:P}}(e,c,d,r,i,a,s);!function(e,t,n){const{key:o,disable:r}=Gv();ko((()=>{r.value=!e.visible})),Co(o,(e=>{"esc"===e?t():"enter"===e&&n()}))}(c,_,w),function(e,t){const n=To(qu,!1);if(n){const o={reset:e,submit:()=>{const e=["",null],{key:n,value:o}=t();return""!==n&&(e[0]=n,e[1]=o),e}};n.addField(o),dr((()=>{n.removeField(o)}))}}(x,T),k(),S(),E();const O=Gy(c);return ko((()=>{c.isDesktop=O.isDesktop.value,c.popupStyle=O.popupStyle.value})),dr((()=>{i.value&&i.value.remove()})),lr((()=>{l.value=!0})),()=>{let t;const{visible:d,contentVisible:x,valueArray:T,popupStyle:k,valueSync:S}=c,{rangeKey:C,mode:E,start:O,end:M}=e,I=Nu(e,"disabled");return Ei("uni-picker",Li({ref:r},I,{onClick:Fu(p)}),[l.value?Ei("div",{ref:i,class:["uni-picker-container",`uni-${E}-${h.value}`],onWheel:Rc,onTouchmove:Rc},[Ei(Oa,{name:"uni-fade"},{default:()=>[vr(Ei("div",{class:"uni-mask uni-picker-mask",onClick:Fu(_),onMousemove:y},null,40,["onClick","onMousemove"]),[[Wa,d]])]}),f.value?null:Ei("div",{class:[{"uni-picker-toggle":d},"uni-picker-custom"],style:k.content},[Ei("div",{class:"uni-picker-header",onClick:jc},[Ei("div",{class:"uni-picker-action uni-picker-action-cancel",onClick:Fu(_)},[o("uni.picker.cancel")],8,["onClick"]),Ei("div",{class:"uni-picker-action uni-picker-action-confirm",onClick:w},[o("uni.picker.done")],8,["onClick"])],8,["onClick"]),x?Ei(Sg,{value:g(T),class:"uni-picker-content",onChange:b},Pb(t=Sr(g(u.value),((e,t)=>{let n;return Ei(Lg,{key:t},Pb(n=Sr(e,((e,n)=>Ei("div",{key:n,class:"uni-picker-item"},["object"==typeof e?e[C]||"":m(e,t)]))))?n:{default:()=>[n],_:1})})))?t:{default:()=>[t],_:1},8,["value","onChange"]):null,Ei("div",{ref:a,class:"uni-picker-select",onWheel:jc,onTouchmove:jc},[Sr(u.value[0],((e,t)=>Ei("div",{key:t,class:["uni-picker-item",{selected:T[0]===t}],onClick:()=>{T[0]=t,w()}},["object"==typeof e?e[C]||"":e],10,["onClick"])))],40,["onWheel","onTouchmove"]),Ei("div",{style:k.triangle},null,4)],6)],40,["onWheel","onTouchmove"]):null,Ei("div",null,[n.default&&n.default()]),f.value?Ei("div",{class:"uni-picker-system",onMousemove:Fu(y)},[Ei("input",{class:["uni-picker-system_input",f.value],ref:s,value:S,type:E,tabindex:"-1",min:O,max:M,onChange:e=>{v(e),jc(e)}},null,42,["value","type","min","max","onChange"])],40,["onMousemove"]):null],16,["onClick"])}}});const Fb=()=>0===String(navigator.vendor).indexOf("Apple")&&navigator.maxTouchPoints>0;const Vb=()=>{if(/win|mac/i.test(navigator.platform)){if("Google Inc."===navigator.vendor)return"chrome";if(/Firefox/.test(navigator.userAgent))return"firefox"}return""};let qb;const Wb=x(pc,{publishHandler(e,t,n){Hb.subscribeHandler(e,t,n)}}),Hb=x(xu,{publishHandler(e,t,n){Wb.subscribeHandler(e,t,n)}}),zb={name:"PageRefresh",setup(){const{pullToRefresh:e}=vm();return{offset:e.offset,color:e.color}}},Ub=(e,t)=>{const n=e.__vccOpts||e;for(const[o,r]of t)n[o]=r;return n},Yb={class:"uni-page-refresh-inner"},Xb=["fill"],Gb=[Ci("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null,-1),Ci("path",{d:"M0 0h24v24H0z",fill:"none"},null,-1)],Jb={class:"uni-page-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},Kb=["stroke"];const Zb=Ub(zb,[["render",function(e,t,n,o,r,a){return gi(),bi("uni-page-refresh",null,[Ci("div",{style:i({"margin-top":o.offset+"px"}),class:"uni-page-refresh"},[Ci("div",Yb,[(gi(),bi("svg",{fill:o.color,class:"uni-page-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},Gb,8,Xb)),(gi(),bi("svg",Jb,[Ci("circle",{stroke:o.color,class:"uni-page-refresh__path",cx:"50",cy:"50",r:"20",fill:"none","stroke-width":"4","stroke-miterlimit":"10"},null,8,Kb)]))])],4)])}]]);function Qb(e,t,n){const o=Array.prototype.slice.call(e.changedTouches).filter((e=>e.identifier===t))[0];return!!o&&(e.deltaY=o.pageY-n,!0)}const e_="pulling",t_="reached",n_="aborting",o_="refreshing",r_="restoring";function i_(e){const{id:t,pullToRefresh:n}=vm(),{range:o,height:r}=n;let i,a,s,l,c,u,d,f;sm((()=>{f||(f=o_,m(),setTimeout((()=>{w()}),50))}),"startPullDownRefresh",!1,t),sm((()=>{f===o_&&(v(),f=r_,m(),function(e){if(!a)return;s.transition="-webkit-transform 0.3s",s.transform+=" scale(0.01)";const t=function(){n&&clearTimeout(n),a.removeEventListener("webkitTransitionEnd",t),s.transition="",s.transform="translate3d(-50%, 0, 0)",e()};a.addEventListener("webkitTransitionEnd",t);const n=setTimeout(t,350)}((()=>{v(),f=h=p=null})))}),Fh,!1,t),lr((()=>{i=e.value.$el,a=i.querySelector(".uni-page-refresh"),s=a.style,l=a.querySelector(".uni-page-refresh-inner").style}));let h=null,p=null;function g(e){f&&i&&i.classList[e]("uni-page-refresh--"+f)}function m(){g("add")}function v(){g("remove")}const y=Fu((e=>{const t=e.changedTouches[0];c=t.identifier,u=t.pageY,d=!([n_,o_,r_].indexOf(f)>=0)})),b=Fu((e=>{if(!d)return;if(!Qb(e,c,u))return;let{deltaY:t}=e;if(0!==(document.documentElement.scrollTop||document.body.scrollTop))return void(c=null);if(t<0&&!f)return;e.preventDefault(),null===h&&(p=t,f=e_,m()),t-=p,t<0&&(t=0),h=t;(t>=o&&f!==t_||t<o&&f!==e_)&&(v(),f=f===t_?e_:t_,m()),function(e){if(!a)return;let t=e/o;t>1?t=1:t*=t*t;const n=Math.round(e/(o/r))||0;l.transform="rotate("+360*t+"deg)",s.clip="rect("+(45-n)+"px,45px,45px,-5px)",s.transform="translate3d(-50%, "+n+"px, 0)"}(t)})),_=Fu((e=>{Qb(e,c,u)&&null!==f&&(f===e_?(v(),f=n_,m(),function(e){if(!a)return;if(s.transform){s.transition="-webkit-transform 0.3s",s.transform="translate3d(-50%, 0, 0)";const t=function(){n&&clearTimeout(n),a.removeEventListener("webkitTransitionEnd",t),s.transition="",e()};a.addEventListener("webkitTransitionEnd",t);const n=setTimeout(t,350)}else e()}((()=>{v(),f=h=p=null}))):f===t_&&(v(),f=o_,m(),w()))}));function w(){a&&(s.transition="-webkit-transform 0.2s",s.transform="translate3d(-50%, "+r+"px, 0)",nu(t,ye))}return{onTouchstartPassive:y,onTouchmove:b,onTouchend:_,onTouchcancel:_}}const a_=ju({name:"PageBody",setup(e,t){const n=vm(),o=An(null),r=n.enablePullDownRefresh?i_(o):null;return()=>{const e=function(e,t){if(!t.enablePullDownRefresh)return null;return Ei(Zb,{ref:e},null,512)}(o,n);return Ei(ci,null,[e,Ei("uni-page-wrapper",r,[Ei("uni-page-body",null,[Cr(t.slots,"default")])],16)])}}});const s_=ju({name:"Page",setup(e,t){const n=ym(_m());return n.navigationBar,ib(n),()=>Ei("uni-page",{"data-page":n.route},[l_(t)])}});function l_(e){return gi(),_i(a_,{key:0},{default:mo((()=>[Cr(e.slots,"page")])),_:3})}const c_={loading:"AsyncLoading",error:"AsyncError",delay:200,timeout:6e4,suspensible:!0};window.uni={},window.wx={},window.rpx2px=of;const u_=Object.assign({}),d_=Object.assign;window.__uniConfig=d_({globalStyle:{backgroundColor:"#F8F8F8",pullToRefresh:{color:"#007aff"},navigationBar:{backgroundColor:"#FFFFFF",titleText:"运维平台",style:"custom",type:"default",titleColor:"#000000"},isNVue:!1},tabBar:{position:"bottom",color:"#999",selectedColor:"#007aff",borderStyle:"black",blurEffect:"none",fontSize:"10px",iconWidth:"20px",spacing:"3px",height:"50px",custom:!0,list:[{iconPath:"/static/images/tabbar/home.png",selectedIconPath:"/static/images/tabbar/home_s.png",pagePath:"pages/index/index",text:"首页"},{iconPath:"/static/images/tabbar/user.png",selectedIconPath:"/static/images/tabbar/user_s.png",pagePath:"pages/user/user",text:"我的"}],selectedIndex:0,shown:!0},easycom:{custom:{"router-navigate":"uniapp-router-next/components/router-navigate/router-navigate.vue","^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)":"z-paging/components/z-paging$1/z-paging$1.vue","^w-(.*)":"@/components/widgets/$1/$1.vue","^uni-(.*)":"@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"}},compilerVersion:"3.7.9"},{appId:"__UNI__300FAC8",appName:"汇发运维平台",appVersion:"1.0.0",appVersionCode:"100",async:c_,debug:!1,networkTimeout:{request:6e4,connectSocket:6e4,uploadFile:6e4,downloadFile:6e4},sdkConfigs:{},qqMapKey:void 0,googleMapKey:void 0,aMapKey:void 0,aMapSecurityJsCode:void 0,aMapServiceHost:void 0,nvue:{"flex-direction":"column"},locale:"",fallbackLocale:"",locales:Object.keys(u_).reduce(((e,t)=>{const n=t.replace(/\.\/locale\/(uni-app.)?(.*).json/,"$2");return d_(e[n]||(e[n]={}),u_[t].default),e}),{}),router:{mode:"history",base:"/mobile/",assets:"assets",routerBase:"/mobile/"},darkmode:!1,themeConfig:{}}),window.__uniLayout=window.__uniLayout||{};const f_={delay:c_.delay,timeout:c_.timeout,suspensible:c_.suspensible};c_.loading&&(f_.loadingComponent={name:"SystemAsyncLoading",render:()=>Ei(_r(c_.loading))}),c_.error&&(f_.errorComponent={name:"SystemAsyncError",render:()=>Ei(_r(c_.error))});const h_=()=>o((()=>import("./pages-index-index.71775572.js")),["assets/pages-index-index.71775572.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-search.13e7bc77.js","assets/u-icon.6d560ed4.js","assets/u-icon-25f21262.css","assets/u-search-7f865d2b.css","assets/u-image.b47882bd.js","assets/u-image-7673150b.css","assets/news-card.083f7e91.js","assets/icon_visit.713e13e8.js","assets/news-card-2c565105.css","assets/router-navigate.5e0be555.js","assets/u-popup.64944c14.js","assets/u-popup-3a489e4e.css","assets/tabbar.93221fe1.js","assets/u-badge.7c374814.js","assets/u-badge-971d8f79.css","assets/message.8e95ac30.js","assets/tabbar-48d7b6c4.css","assets/setcompany.0ffb3aee.js","assets/uni-easyinput.61f3681a.js","assets/uni-icons.8ecc47ca.js","assets/uni-icons-44bfd240.css","assets/uni-easyinput-0615f5e4.css","assets/company.9c51333f.js","assets/setcompany-872411ea.css","assets/MessageDetailPopupWrapper.deac022e.js","assets/MessageDetailPopup.62ca7e36.js","assets/uni-popup.b7176a8c.js","assets/uni-popup-5d02208b.css","assets/customFn.bcd5b981.js","assets/MessageDetailPopup-d46c1304.css","assets/MessageDetailPopupWrapper-e3b0c442.css","assets/index-5569f46f.css"]).then((e=>sv(e.default||e))),p_=Wo(d_({loader:h_},f_)),g_=()=>o((()=>import("./pages-news-news.84f4d9aa.js")),["assets/pages-news-news.84f4d9aa.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-search.13e7bc77.js","assets/u-icon.6d560ed4.js","assets/u-icon-25f21262.css","assets/u-search-7f865d2b.css","assets/u-badge.7c374814.js","assets/u-badge-971d8f79.css","assets/tabbar.93221fe1.js","assets/message.8e95ac30.js","assets/tabbar-48d7b6c4.css","assets/news-card.083f7e91.js","assets/u-image.b47882bd.js","assets/u-image-7673150b.css","assets/icon_visit.713e13e8.js","assets/news-card-2c565105.css","assets/z-paging.80d90434.js","assets/z-paging-fd0d435c.css","assets/news.70080b47.js","assets/news-61cb6646.css"]).then((e=>sv(e.default||e))),m_=Wo(d_({loader:g_},f_)),v_=()=>o((()=>import("./pages-user-user.9f09c72e.js")),["assets/pages-user-user.9f09c72e.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-avatar.29adff89.js","assets/u-icon.6d560ed4.js","assets/u-icon-25f21262.css","assets/u-avatar-4ce082fd.css","assets/u-image.b47882bd.js","assets/u-image-7673150b.css","assets/message.8e95ac30.js","assets/router-navigate.5e0be555.js","assets/tabbar.93221fe1.js","assets/u-badge.7c374814.js","assets/u-badge-971d8f79.css","assets/tabbar-48d7b6c4.css","assets/MessageDetailPopupWrapper.deac022e.js","assets/MessageDetailPopup.62ca7e36.js","assets/uni-icons.8ecc47ca.js","assets/uni-icons-44bfd240.css","assets/uni-popup.b7176a8c.js","assets/uni-popup-5d02208b.css","assets/customFn.bcd5b981.js","assets/MessageDetailPopup-d46c1304.css","assets/MessageDetailPopupWrapper-e3b0c442.css","assets/user-bc7b2bcd.css"]).then((e=>sv(e.default||e))),y_=Wo(d_({loader:v_},f_)),b_=()=>o((()=>import("./pages-login-login.6722d821.js")),["assets/pages-login-login.6722d821.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-button.380e06fb.js","assets/u-button-c14b4be4.css","assets/u-input.65530b3e.js","assets/u-icon.6d560ed4.js","assets/u-icon-25f21262.css","assets/emitter.1571a5d9.js","assets/u-input-10187c76.css","assets/u-verification-code.18d85e5e.js","assets/u-verification-code-e2ab8652.css","assets/u-modal.d0b4d5c1.js","assets/u-loading.fe9045d5.js","assets/u-loading-ae83b4a2.css","assets/u-popup.64944c14.js","assets/u-popup-3a489e4e.css","assets/u-modal-694490d3.css","assets/useLockFn.f0e9b0c0.js","assets/message.8e95ac30.js","assets/login-425ca7b7.css"]).then((e=>sv(e.default||e))),__=Wo(d_({loader:b_},f_)),w_=()=>o((()=>import("./pages-register-register.6780a5f3.js")),["assets/pages-register-register.6780a5f3.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-input.65530b3e.js","assets/u-icon.6d560ed4.js","assets/u-icon-25f21262.css","assets/emitter.1571a5d9.js","assets/u-input-10187c76.css","assets/router-navigate.5e0be555.js","assets/u-modal.d0b4d5c1.js","assets/u-loading.fe9045d5.js","assets/u-loading-ae83b4a2.css","assets/u-popup.64944c14.js","assets/u-popup-3a489e4e.css","assets/u-modal-694490d3.css","assets/u-button.380e06fb.js","assets/u-button-c14b4be4.css","assets/register-09ed971c.css"]).then((e=>sv(e.default||e))),x_=Wo(d_({loader:w_},f_)),T_=()=>o((()=>import("./pages-forget_pwd-forget_pwd.04d92a5f.js")),["assets/pages-forget_pwd-forget_pwd.04d92a5f.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-input.65530b3e.js","assets/u-icon.6d560ed4.js","assets/u-icon-25f21262.css","assets/emitter.1571a5d9.js","assets/u-input-10187c76.css","assets/u-form-item.e982dece.js","assets/u-form-item-d6a97f32.css","assets/u-verification-code.18d85e5e.js","assets/u-verification-code-e2ab8652.css","assets/u-form.3bf3c3be.js","assets/u-button.380e06fb.js","assets/u-button-c14b4be4.css","assets/forget_pwd-a5910266.css"]).then((e=>sv(e.default||e))),k_=Wo(d_({loader:T_},f_)),S_=()=>o((()=>import("./pages-customer_service-customer_service.c6c59aee.js")),["assets/pages-customer_service-customer_service.c6c59aee.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/ServiceComponents.vue_vue_type_script_setup_true_lang.a16681a4.js","assets/u-parse.5114f2e8.js","assets/u-parse-1e32a619.css"]).then((e=>sv(e.default||e))),C_=Wo(d_({loader:S_},f_)),E_=()=>o((()=>import("./pages-news_detail-news_detail.38c8435c.js")),["assets/pages-news_detail-news_detail.38c8435c.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-parse.5114f2e8.js","assets/u-parse-1e32a619.css","assets/u-icon.6d560ed4.js","assets/u-icon-25f21262.css","assets/icon_visit.713e13e8.js","assets/news.70080b47.js","assets/news_detail-d5422ab9.css"]).then((e=>sv(e.default||e))),O_=Wo(d_({loader:E_},f_)),M_=()=>o((()=>import("./pages-user_set-user_set.5a9451cd.js")),["assets/pages-user_set-user_set.5a9451cd.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-avatar.29adff89.js","assets/u-icon.6d560ed4.js","assets/u-icon-25f21262.css","assets/u-avatar-4ce082fd.css","assets/u-button.380e06fb.js","assets/u-button-c14b4be4.css","assets/u-popup.64944c14.js","assets/u-popup-3a489e4e.css","assets/message.8e95ac30.js","assets/useLockFn.f0e9b0c0.js","assets/user_set-81173d06.css"]).then((e=>sv(e.default||e))),I_=Wo(d_({loader:M_},f_)),A_=()=>o((()=>import("./pages-collection-collection.5cc5d053.js")),["assets/pages-collection-collection.5cc5d053.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/news-card.083f7e91.js","assets/u-image.b47882bd.js","assets/u-icon.6d560ed4.js","assets/u-icon-25f21262.css","assets/u-image-7673150b.css","assets/icon_visit.713e13e8.js","assets/news-card-2c565105.css","assets/z-paging.80d90434.js","assets/z-paging-fd0d435c.css","assets/news.70080b47.js","assets/collection-00d40f02.css"]).then((e=>sv(e.default||e))),P_=Wo(d_({loader:A_},f_)),$_=()=>o((()=>import("./pages-as_us-as_us.9b6b849d.js")),["assets/pages-as_us-as_us.9b6b849d.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/as_us-ad1e7d82.css"]).then((e=>sv(e.default||e))),L_=Wo(d_({loader:$_},f_)),R_=()=>o((()=>import("./pages-agreement-agreement.4e86f12b.js")),["assets/pages-agreement-agreement.4e86f12b.js","assets/u-parse.5114f2e8.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-parse-1e32a619.css"]).then((e=>sv(e.default||e))),j_=Wo(d_({loader:R_},f_)),B_=()=>o((()=>import("./pages-change_password-change_password.e339fdb1.js")),["assets/pages-change_password-change_password.e339fdb1.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-input.65530b3e.js","assets/u-icon.6d560ed4.js","assets/u-icon-25f21262.css","assets/emitter.1571a5d9.js","assets/u-input-10187c76.css","assets/u-form-item.e982dece.js","assets/u-form-item-d6a97f32.css","assets/u-form.3bf3c3be.js","assets/u-button.380e06fb.js","assets/u-button-c14b4be4.css","assets/change_password-e8a09978.css"]).then((e=>sv(e.default||e))),D_=Wo(d_({loader:B_},f_)),N_=()=>o((()=>import("./pages-user_data-user_data.558f59c1.js")),["assets/pages-user_data-user_data.558f59c1.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon.6d560ed4.js","assets/u-icon-25f21262.css","assets/_commonjsHelpers.02d3be64.js","assets/u-popup.64944c14.js","assets/u-popup-3a489e4e.css","assets/u-button.380e06fb.js","assets/u-button-c14b4be4.css","assets/u-form-item.e982dece.js","assets/emitter.1571a5d9.js","assets/u-form-item-d6a97f32.css","assets/u-input.65530b3e.js","assets/u-input-10187c76.css","assets/u-verification-code.18d85e5e.js","assets/u-verification-code-e2ab8652.css","assets/setcompany.0ffb3aee.js","assets/uni-easyinput.61f3681a.js","assets/uni-icons.8ecc47ca.js","assets/uni-icons-44bfd240.css","assets/uni-easyinput-0615f5e4.css","assets/company.9c51333f.js","assets/setcompany-872411ea.css","assets/user_data-405647ce.css"]).then((e=>sv(e.default||e))),F_=Wo(d_({loader:N_},f_)),V_=()=>o((()=>import("./pages-search-search.750165e6.js")),["assets/pages-search-search.750165e6.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-search.13e7bc77.js","assets/u-icon.6d560ed4.js","assets/u-icon-25f21262.css","assets/u-search-7f865d2b.css","assets/news-card.083f7e91.js","assets/u-image.b47882bd.js","assets/u-image-7673150b.css","assets/icon_visit.713e13e8.js","assets/news-card-2c565105.css","assets/z-paging.80d90434.js","assets/z-paging-fd0d435c.css","assets/news.70080b47.js","assets/search-8fccde5b.css"]).then((e=>sv(e.default||e))),q_=Wo(d_({loader:V_},f_)),W_=()=>o((()=>import("./pages-webview-webview.3f3e08a2.js")),[]).then((e=>sv(e.default||e))),H_=Wo(d_({loader:W_},f_)),z_=()=>o((()=>import("./pages-bind_mobile-bind_mobile.031066c4.js")),["assets/pages-bind_mobile-bind_mobile.031066c4.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-input.65530b3e.js","assets/u-icon.6d560ed4.js","assets/u-icon-25f21262.css","assets/emitter.1571a5d9.js","assets/u-input-10187c76.css","assets/u-verification-code.18d85e5e.js","assets/u-verification-code-e2ab8652.css","assets/u-button.380e06fb.js","assets/u-button-c14b4be4.css","assets/bind_mobile-32de960c.css"]).then((e=>sv(e.default||e))),U_=Wo(d_({loader:z_},f_)),Y_=()=>o((()=>import("./pages-empty-empty.8daa3fbb.js")),["assets/pages-empty-empty.8daa3fbb.js","assets/_plugin-vue_export-helper.1b428a4d.js"]).then((e=>sv(e.default||e))),X_=Wo(d_({loader:Y_},f_)),G_=()=>o((()=>import("./pages-payment_result-payment_result.7f4241f9.js")),["assets/pages-payment_result-payment_result.7f4241f9.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-empty.f93c1ca8.js","assets/u-icon.6d560ed4.js","assets/u-icon-25f21262.css","assets/u-empty-d9a13c75.css","assets/u-image.b47882bd.js","assets/u-image-7673150b.css","assets/u-button.380e06fb.js","assets/u-button-c14b4be4.css","assets/pay.de8bc32a.js","assets/u-loading.fe9045d5.js","assets/u-loading-ae83b4a2.css","assets/pay-381545af.css","assets/payment_result-b802ebea.css"]).then((e=>sv(e.default||e))),J_=Wo(d_({loader:G_},f_)),K_=()=>o((()=>import("./uni_modules-vk-uview-ui-components-u-avatar-cropper-u-avatar-cropper.49b211db.js")),["assets/uni_modules-vk-uview-ui-components-u-avatar-cropper-u-avatar-cropper.49b211db.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-avatar-cropper-a78b55d6.css"]).then((e=>sv(e.default||e))),Z_=Wo(d_({loader:K_},f_)),Q_=()=>o((()=>import("./packages-pages-ticket-device-index.fe9084e9.js")),["assets/packages-pages-ticket-device-index.fe9084e9.js","assets/uni-search-bar.f1ab1fca.js","assets/uni-icons.8ecc47ca.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/uni-icons-44bfd240.css","assets/uni-search-bar-992221f5.css","assets/uni-data-select.8d04263d.js","assets/uni-cloud.es.050f64ca.js","assets/uni-data-select-14c510e7.css","assets/index-45f71990.css"]).then((e=>sv(e.default||e))),ew=Wo(d_({loader:Q_},f_)),tw=()=>o((()=>import("./packages-pages-ticket-device-detail.ab0d0767.js")),["assets/packages-pages-ticket-device-detail.ab0d0767.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon.6d560ed4.js","assets/u-icon-25f21262.css","assets/product.37a3d4dc.js","assets/uni-icons.8ecc47ca.js","assets/uni-icons-44bfd240.css","assets/uni-popup.b7176a8c.js","assets/uni-popup-5d02208b.css","assets/ServiceComponents.vue_vue_type_script_setup_true_lang.a16681a4.js","assets/u-parse.5114f2e8.js","assets/u-parse-1e32a619.css","assets/detail-f70adaef.css"]).then((e=>sv(e.default||e))),nw=Wo(d_({loader:tw},f_)),ow=()=>o((()=>import("./packages-pages-message-index.8648c77b.js")),["assets/packages-pages-message-index.8648c77b.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/uni-search-bar.f1ab1fca.js","assets/uni-icons.8ecc47ca.js","assets/uni-icons-44bfd240.css","assets/uni-search-bar-992221f5.css","assets/message.8e95ac30.js","assets/MessageDetailPopup.62ca7e36.js","assets/uni-popup.b7176a8c.js","assets/uni-popup-5d02208b.css","assets/customFn.bcd5b981.js","assets/MessageDetailPopup-d46c1304.css","assets/empty.dfd7c9cf.js","assets/index-315a1ea9.css"]).then((e=>sv(e.default||e))),rw=Wo(d_({loader:ow},f_)),iw=()=>o((()=>import("./packages-pages-ticket-order-create.2d6abb72.js")),["assets/packages-pages-ticket-order-create.2d6abb72.js","assets/uni-forms.6d5d1004.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/uni-forms-0fd558fe.css","assets/uni-data-select.8d04263d.js","assets/uni-cloud.es.050f64ca.js","assets/uni-icons.8ecc47ca.js","assets/uni-icons-44bfd240.css","assets/uni-data-select-14c510e7.css","assets/uni-easyinput.61f3681a.js","assets/uni-easyinput-0615f5e4.css","assets/index.150c4cb8.js","assets/uni-data-checkbox.1ac05949.js","assets/uni-data-checkbox-ffbd19db.css","assets/uni-search-bar.f1ab1fca.js","assets/uni-search-bar-992221f5.css","assets/u-popup.64944c14.js","assets/u-icon.6d560ed4.js","assets/u-icon-25f21262.css","assets/u-popup-3a489e4e.css","assets/company.9c51333f.js","assets/index-b1195611.css","assets/ticket.f414548f.js","assets/product.37a3d4dc.js","assets/customFn.bcd5b981.js","assets/useDictOptions.a4153ed6.js","assets/index.vue_vue_type_script_setup_true_lang.40aaf37a.js","assets/setcompany.0ffb3aee.js","assets/setcompany-872411ea.css","assets/create-567fea83.css"]).then((e=>sv(e.default||e))),aw=Wo(d_({loader:iw},f_)),sw=()=>o((()=>import("./packages-pages-ticket-order-list.b81573da.js")),["assets/packages-pages-ticket-order-list.b81573da.js","assets/u-icon.6d560ed4.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon-25f21262.css","assets/TicketStatusSelector.c5403fbf.js","assets/uni-data-checkbox.1ac05949.js","assets/uni-cloud.es.050f64ca.js","assets/uni-data-checkbox-ffbd19db.css","assets/u-popup.64944c14.js","assets/u-popup-3a489e4e.css","assets/TicketStatusSelector-22bfae90.css","assets/tabbar.93221fe1.js","assets/u-badge.7c374814.js","assets/u-badge-971d8f79.css","assets/message.8e95ac30.js","assets/tabbar-48d7b6c4.css","assets/ticket.f414548f.js","assets/customFn.bcd5b981.js","assets/useDictOptions.a4153ed6.js","assets/index.vue_vue_type_script_setup_true_lang.40aaf37a.js","assets/MessageDetailPopupWrapper.deac022e.js","assets/MessageDetailPopup.62ca7e36.js","assets/uni-icons.8ecc47ca.js","assets/uni-icons-44bfd240.css","assets/uni-popup.b7176a8c.js","assets/uni-popup-5d02208b.css","assets/MessageDetailPopup-d46c1304.css","assets/MessageDetailPopupWrapper-e3b0c442.css","assets/empty.dfd7c9cf.js","assets/list-339de190.css"]).then((e=>sv(e.default||e))),lw=Wo(d_({loader:sw},f_)),cw=()=>o((()=>import("./packages-pages-ticket-order-staff_list.a94a266b.js")),["assets/packages-pages-ticket-order-staff_list.a94a266b.js","assets/u-icon.6d560ed4.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon-25f21262.css","assets/TicketStatusSelector.c5403fbf.js","assets/uni-data-checkbox.1ac05949.js","assets/uni-cloud.es.050f64ca.js","assets/uni-data-checkbox-ffbd19db.css","assets/u-popup.64944c14.js","assets/u-popup-3a489e4e.css","assets/TicketStatusSelector-22bfae90.css","assets/uni-easyinput.61f3681a.js","assets/uni-icons.8ecc47ca.js","assets/uni-icons-44bfd240.css","assets/uni-easyinput-0615f5e4.css","assets/ticket.f414548f.js","assets/customFn.bcd5b981.js","assets/useDictOptions.a4153ed6.js","assets/index.vue_vue_type_script_setup_true_lang.40aaf37a.js","assets/single.8ed39644.js","assets/uni-search-bar.f1ab1fca.js","assets/uni-search-bar-992221f5.css","assets/single-4b812c3a.css","assets/MessageDetailPopupWrapper.deac022e.js","assets/MessageDetailPopup.62ca7e36.js","assets/uni-popup.b7176a8c.js","assets/uni-popup-5d02208b.css","assets/message.8e95ac30.js","assets/MessageDetailPopup-d46c1304.css","assets/MessageDetailPopupWrapper-e3b0c442.css","assets/empty.dfd7c9cf.js","assets/staff_list-5df50777.css"]).then((e=>sv(e.default||e))),uw=Wo(d_({loader:cw},f_)),dw=()=>o((()=>import("./packages-pages-ticket-order-staff_detail.bbab8d8b.js")),["assets/packages-pages-ticket-order-staff_detail.bbab8d8b.js","assets/TicketStatusSelector.c5403fbf.js","assets/u-icon.6d560ed4.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon-25f21262.css","assets/uni-data-checkbox.1ac05949.js","assets/uni-cloud.es.050f64ca.js","assets/uni-data-checkbox-ffbd19db.css","assets/u-popup.64944c14.js","assets/u-popup-3a489e4e.css","assets/TicketStatusSelector-22bfae90.css","assets/uni-easyinput.61f3681a.js","assets/uni-icons.8ecc47ca.js","assets/uni-icons-44bfd240.css","assets/uni-easyinput-0615f5e4.css","assets/uni-forms.6d5d1004.js","assets/uni-forms-0fd558fe.css","assets/ticket.f414548f.js","assets/customFn.bcd5b981.js","assets/useDictOptions.a4153ed6.js","assets/index.vue_vue_type_script_setup_true_lang.40aaf37a.js","assets/single.8ed39644.js","assets/uni-search-bar.f1ab1fca.js","assets/uni-search-bar-992221f5.css","assets/single-4b812c3a.css","assets/AiAnalysisResult.76c138e0.js","assets/AiAnalysisResult-fb71a89c.css","assets/MessageDetailPopupWrapper.deac022e.js","assets/MessageDetailPopup.62ca7e36.js","assets/uni-popup.b7176a8c.js","assets/uni-popup-5d02208b.css","assets/message.8e95ac30.js","assets/MessageDetailPopup-d46c1304.css","assets/MessageDetailPopupWrapper-e3b0c442.css","assets/empty.dfd7c9cf.js","assets/staff_detail-11d8fa8b.css"]).then((e=>sv(e.default||e))),fw=Wo(d_({loader:dw},f_)),hw=()=>o((()=>import("./packages-pages-ticket-order-detail.b3d17352.js")),["assets/packages-pages-ticket-order-detail.b3d17352.js","assets/TicketStatusSelector.c5403fbf.js","assets/u-icon.6d560ed4.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon-25f21262.css","assets/uni-data-checkbox.1ac05949.js","assets/uni-cloud.es.050f64ca.js","assets/uni-data-checkbox-ffbd19db.css","assets/u-popup.64944c14.js","assets/u-popup-3a489e4e.css","assets/TicketStatusSelector-22bfae90.css","assets/uni-easyinput.61f3681a.js","assets/uni-icons.8ecc47ca.js","assets/uni-icons-44bfd240.css","assets/uni-easyinput-0615f5e4.css","assets/uni-forms.6d5d1004.js","assets/uni-forms-0fd558fe.css","assets/ticket.f414548f.js","assets/customFn.bcd5b981.js","assets/useDictOptions.a4153ed6.js","assets/index.vue_vue_type_script_setup_true_lang.40aaf37a.js","assets/MessageDetailPopupWrapper.deac022e.js","assets/MessageDetailPopup.62ca7e36.js","assets/uni-popup.b7176a8c.js","assets/uni-popup-5d02208b.css","assets/message.8e95ac30.js","assets/MessageDetailPopup-d46c1304.css","assets/MessageDetailPopupWrapper-e3b0c442.css","assets/AiAnalysisResult.76c138e0.js","assets/AiAnalysisResult-fb71a89c.css","assets/empty.dfd7c9cf.js","assets/detail-e59b4540.css"]).then((e=>sv(e.default||e))),pw=Wo(d_({loader:hw},f_)),gw=()=>o((()=>import("./packages-pages-ticket-hall.c34647ad.js")),["assets/packages-pages-ticket-hall.c34647ad.js","assets/u-icon.6d560ed4.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon-25f21262.css","assets/useDictOptions.a4153ed6.js","assets/index.vue_vue_type_script_setup_true_lang.40aaf37a.js","assets/ticket.f414548f.js","assets/hall-779a5eb3.css"]).then((e=>sv(e.default||e))),mw=Wo(d_({loader:gw},f_)),vw=()=>o((()=>import("./packages-pages-ticket-hall_detail.99ae6485.js")),["assets/packages-pages-ticket-hall_detail.99ae6485.js","assets/ticket.f414548f.js","assets/useDictOptions.a4153ed6.js","assets/AiAnalysisResult.76c138e0.js","assets/uni-icons.8ecc47ca.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/uni-icons-44bfd240.css","assets/AiAnalysisResult-fb71a89c.css","assets/hall_detail-24a0a47f.css"]).then((e=>sv(e.default||e))),yw=Wo(d_({loader:vw},f_)),bw=()=>o((()=>import("./packages-pages-ticket-index.b8d10652.js")),["assets/packages-pages-ticket-index.b8d10652.js","assets/u-icon.6d560ed4.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon-25f21262.css","assets/router-navigate.5e0be555.js","assets/tabbar.93221fe1.js","assets/u-badge.7c374814.js","assets/u-badge-971d8f79.css","assets/message.8e95ac30.js","assets/tabbar-48d7b6c4.css","assets/ticket.f414548f.js","assets/MessageDetailPopupWrapper.deac022e.js","assets/MessageDetailPopup.62ca7e36.js","assets/uni-icons.8ecc47ca.js","assets/uni-icons-44bfd240.css","assets/uni-popup.b7176a8c.js","assets/uni-popup-5d02208b.css","assets/customFn.bcd5b981.js","assets/MessageDetailPopup-d46c1304.css","assets/MessageDetailPopupWrapper-e3b0c442.css","assets/empty-data.b3ff4905.js","assets/index-0620ba70.css"]).then((e=>sv(e.default||e))),_w=Wo(d_({loader:bw},f_)),ww=()=>o((()=>import("./packages-pages-ticket-user.1291cabe.js")),["assets/packages-pages-ticket-user.1291cabe.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon.6d560ed4.js","assets/u-icon-25f21262.css","assets/router-navigate.5e0be555.js","assets/tabbar.93221fe1.js","assets/u-badge.7c374814.js","assets/u-badge-971d8f79.css","assets/message.8e95ac30.js","assets/tabbar-48d7b6c4.css","assets/ticket.f414548f.js","assets/MessageDetailPopupWrapper.deac022e.js","assets/MessageDetailPopup.62ca7e36.js","assets/uni-icons.8ecc47ca.js","assets/uni-icons-44bfd240.css","assets/uni-popup.b7176a8c.js","assets/uni-popup-5d02208b.css","assets/customFn.bcd5b981.js","assets/MessageDetailPopup-d46c1304.css","assets/MessageDetailPopupWrapper-e3b0c442.css","assets/empty-data.b3ff4905.js","assets/user-1e0e38dd.css"]).then((e=>sv(e.default||e))),xw=Wo(d_({loader:ww},f_)),Tw=()=>o((()=>import("./packages-pages-ticket-aingdesk-chat.09dde3f4.js")),["assets/packages-pages-ticket-aingdesk-chat.09dde3f4.js","assets/uni-icons.8ecc47ca.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/uni-icons-44bfd240.css","assets/uni-popup.b7176a8c.js","assets/uni-popup-5d02208b.css","assets/uni-easyinput.61f3681a.js","assets/uni-easyinput-0615f5e4.css","assets/uni-forms.6d5d1004.js","assets/uni-forms-0fd558fe.css","assets/uni-data-select.8d04263d.js","assets/uni-cloud.es.050f64ca.js","assets/uni-data-select-14c510e7.css","assets/index.150c4cb8.js","assets/uni-data-checkbox.1ac05949.js","assets/uni-data-checkbox-ffbd19db.css","assets/uni-search-bar.f1ab1fca.js","assets/uni-search-bar-992221f5.css","assets/u-popup.64944c14.js","assets/u-icon.6d560ed4.js","assets/u-icon-25f21262.css","assets/u-popup-3a489e4e.css","assets/company.9c51333f.js","assets/index-b1195611.css","assets/ticket.f414548f.js","assets/aingdesk-chat-7b46d3a2.css"]).then((e=>sv(e.default||e))),kw=Wo(d_({loader:Tw},f_)),Sw=()=>o((()=>import("./packages-pages-ticket-order-template.b54a6a03.js")),["assets/packages-pages-ticket-order-template.b54a6a03.js","assets/uni-search-bar.f1ab1fca.js","assets/uni-icons.8ecc47ca.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/uni-icons-44bfd240.css","assets/uni-search-bar-992221f5.css","assets/template-8a67f0bc.css"]).then((e=>sv(e.default||e))),Cw=Wo(d_({loader:Sw},f_)),Ew=()=>o((()=>import("./packages-pages-web-view-index.41bbbaf9.js")),["assets/packages-pages-web-view-index.41bbbaf9.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/index-8ca226df.css"]).then((e=>sv(e.default||e))),Ow=Wo(d_({loader:Ew},f_)),Mw=()=>o((()=>import("./packages-pages-404-404.ff8394a0.js")),["assets/packages-pages-404-404.ff8394a0.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-empty.f93c1ca8.js","assets/u-icon.6d560ed4.js","assets/u-icon-25f21262.css","assets/u-empty-d9a13c75.css","assets/router-navigate.5e0be555.js"]).then((e=>sv(e.default||e))),Iw=Wo(d_({loader:Mw},f_)),Aw=()=>o((()=>import("./packages-pages-user_wallet-user_wallet.b955b3be.js")),["assets/packages-pages-user_wallet-user_wallet.b955b3be.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-badge.7c374814.js","assets/u-badge-971d8f79.css","assets/z-paging.80d90434.js","assets/z-paging-fd0d435c.css","assets/recharge.3780b718.js","assets/user_wallet-477c9a51.css"]).then((e=>sv(e.default||e))),Pw=Wo(d_({loader:Aw},f_)),$w=()=>o((()=>import("./packages-pages-recharge-recharge.4f6cbf26.js")),["assets/packages-pages-recharge-recharge.4f6cbf26.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-button.380e06fb.js","assets/u-button-c14b4be4.css","assets/u-empty.f93c1ca8.js","assets/u-icon.6d560ed4.js","assets/u-icon-25f21262.css","assets/u-empty-d9a13c75.css","assets/emitter.1571a5d9.js","assets/pay.de8bc32a.js","assets/u-loading.fe9045d5.js","assets/u-loading-ae83b4a2.css","assets/pay-381545af.css","assets/u-popup.64944c14.js","assets/u-popup-3a489e4e.css","assets/useLockFn.f0e9b0c0.js","assets/recharge.3780b718.js","assets/recharge-9244ea37.css"]).then((e=>sv(e.default||e))),Lw=Wo(d_({loader:$w},f_)),Rw=()=>o((()=>import("./packages-pages-recharge_record-recharge_record.89e564d2.js")),["assets/packages-pages-recharge_record-recharge_record.89e564d2.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/z-paging.80d90434.js","assets/z-paging-fd0d435c.css","assets/recharge.3780b718.js"]).then((e=>sv(e.default||e))),jw=Wo(d_({loader:Rw},f_)),Bw=()=>o((()=>import("./packages-pages-user-data_analysis.149e97f6.js")),["assets/packages-pages-user-data_analysis.149e97f6.js","assets/page-meta.91a1b429.js","assets/_plugin-vue_export-helper.1b428a4d.js","assets/u-icon.6d560ed4.js","assets/u-icon-25f21262.css","assets/data_analysis-e2bb58d6.css"]).then((e=>sv(e.default||e))),Dw=Wo(d_({loader:Bw},f_));function Nw(e,t){return gi(),_i(s_,null,{page:mo((()=>[Ei(e,d_({},t,{ref:"page"}),null,512)])),_:1})}function Fw(e,t){return I(e)?t:e}window.__uniRoutes=[{path:"/",alias:"/pages/index/index",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(p_,t)}},loader:h_,meta:{isQuit:!0,isEntry:!0,isTabBar:!0,tabBarIndex:0,navigationBar:{titleText:"首页",type:"default"},isNVue:!1}},{path:"/pages/news/news",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(m_,t)}},loader:g_,meta:{disableScroll:!0,navigationBar:{titleText:"资讯",type:"default"},isNVue:!1}},{path:"/pages/user/user",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(y_,t)}},loader:v_,meta:{isQuit:!0,isTabBar:!0,tabBarIndex:1,navigationBar:{titleText:"个人中心",type:"default"},isNVue:!1}},{path:"/pages/login/login",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(__,t)}},loader:b_,meta:{navigationBar:{titleText:"登录",type:"default"},isNVue:!1}},{path:"/pages/register/register",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(x_,t)}},loader:w_,meta:{navigationBar:{titleText:"注册",type:"default"},isNVue:!1}},{path:"/pages/forget_pwd/forget_pwd",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(k_,t)}},loader:T_,meta:{navigationBar:{titleText:"忘记密码",type:"default"},isNVue:!1}},{path:"/pages/customer_service/customer_service",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(C_,t)}},loader:S_,meta:{navigationBar:{titleText:"联系客服",type:"default"},isNVue:!1}},{path:"/pages/news_detail/news_detail",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(O_,t)}},loader:E_,meta:{navigationBar:{titleText:"详情",type:"default"},isNVue:!1}},{path:"/pages/user_set/user_set",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(I_,t)}},loader:M_,meta:{navigationBar:{titleText:"个人设置",type:"default"},isNVue:!1}},{path:"/pages/collection/collection",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(P_,t)}},loader:A_,meta:{navigationBar:{titleText:"我的收藏",type:"default"},isNVue:!1}},{path:"/pages/as_us/as_us",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(L_,t)}},loader:$_,meta:{navigationBar:{titleText:"关于我们",type:"default"},isNVue:!1}},{path:"/pages/agreement/agreement",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(j_,t)}},loader:R_,meta:{navigationBar:{titleText:"协议",type:"default"},isNVue:!1}},{path:"/pages/change_password/change_password",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(D_,t)}},loader:B_,meta:{navigationBar:{titleText:"修改密码",type:"default"},isNVue:!1}},{path:"/pages/user_data/user_data",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(F_,t)}},loader:N_,meta:{navigationBar:{titleText:"个人资料",type:"default"},isNVue:!1}},{path:"/pages/search/search",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(q_,t)}},loader:V_,meta:{navigationBar:{titleText:"搜索",type:"default"},isNVue:!1}},{path:"/pages/webview/webview",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(H_,t)}},loader:W_,meta:{navigationBar:{},isNVue:!1}},{path:"/pages/bind_mobile/bind_mobile",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(U_,t)}},loader:z_,meta:{navigationBar:{titleText:"绑定手机号",type:"default"},isNVue:!1}},{path:"/pages/empty/empty",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(X_,t)}},loader:Y_,meta:{navigationBar:{style:"custom",type:"default"},isNVue:!1}},{path:"/pages/payment_result/payment_result",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(J_,t)}},loader:G_,meta:{navigationBar:{titleText:"支付结果",type:"default"},isNVue:!1}},{path:"/uni_modules/vk-uview-ui/components/u-avatar-cropper/u-avatar-cropper",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(Z_,t)}},loader:K_,meta:{navigationBar:{backgroundColor:"#000000",titleText:"头像裁剪",type:"default"},isNVue:!1}},{path:"/packages/pages/ticket/device/index",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(ew,t)}},loader:Q_,meta:{navigationBar:{titleText:"设备档案",type:"default"},isNVue:!1}},{path:"/packages/pages/ticket/device/detail",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(nw,t)}},loader:tw,meta:{navigationBar:{titleText:"产品详细",type:"default"},isNVue:!1}},{path:"/packages/pages/message/index",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(rw,t)}},loader:ow,meta:{navigationBar:{titleText:"消息中心",type:"default"},isNVue:!1}},{path:"/packages/pages/ticket/order/create",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(aw,t)}},loader:iw,meta:{navigationBar:{titleText:"创建工单",type:"default"},isNVue:!1}},{path:"/packages/pages/ticket/order/list",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(lw,t)}},loader:sw,meta:{navigationBar:{titleText:"工单列表",type:"default"},isNVue:!1}},{path:"/packages/pages/ticket/order/staff_list",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(uw,t)}},loader:cw,meta:{navigationBar:{titleText:"技术员工单列表",type:"default"},isNVue:!1}},{path:"/packages/pages/ticket/order/staff_detail",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(fw,t)}},loader:dw,meta:{navigationBar:{titleText:"技术员工单详情",type:"default"},isNVue:!1}},{path:"/packages/pages/ticket/order/detail",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(pw,t)}},loader:hw,meta:{navigationBar:{titleText:"工单详情",type:"default"},isNVue:!1}},{path:"/packages/pages/ticket/hall",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(mw,t)}},loader:gw,meta:{enablePullDownRefresh:!0,navigationBar:{titleText:"抢单大厅",type:"default"},isNVue:!1}},{path:"/packages/pages/ticket/hall_detail",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(yw,t)}},loader:vw,meta:{navigationBar:{titleText:"抢单详情",type:"default"},isNVue:!1}},{path:"/packages/pages/ticket/index",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(_w,t)}},loader:bw,meta:{navigationBar:{titleText:"工单中心",type:"default"},isNVue:!1}},{path:"/packages/pages/ticket/user",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(xw,t)}},loader:ww,meta:{navigationBar:{titleText:"我的工单",type:"default"},isNVue:!1}},{path:"/packages/pages/ticket/aingdesk-chat",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(kw,t)}},loader:Tw,meta:{bounce:"none",scrollIndicator:"none",navigationBar:{titleText:"AI助手对话",type:"default"},isNVue:!1}},{path:"/packages/pages/ticket/order/template",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(Cw,t)}},loader:Sw,meta:{navigationBar:{titleText:"工单模板",type:"default"},isNVue:!1}},{path:"/packages/pages/web-view/index",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(Ow,t)}},loader:Ew,meta:{navigationBar:{titleText:"网页浏览",type:"default"},isNVue:!1}},{path:"/packages/pages/404/404",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(Iw,t)}},loader:Mw,meta:{navigationBar:{titleText:"404",type:"default"},isNVue:!1}},{path:"/packages/pages/user_wallet/user_wallet",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(Pw,t)}},loader:Aw,meta:{navigationBar:{titleText:"我的钱包",type:"default"},isNVue:!1}},{path:"/packages/pages/recharge/recharge",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(Lw,t)}},loader:$w,meta:{navigationBar:{titleText:"充值",type:"default"},isNVue:!1}},{path:"/packages/pages/recharge_record/recharge_record",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(jw,t)}},loader:Rw,meta:{navigationBar:{titleText:"充值记录",type:"default"},isNVue:!1}},{path:"/packages/pages/user/data_analysis",component:{setup(){const e=ov(),t=e&&e.$route&&e.$route.query||{};return()=>Nw(Dw,t)}},loader:Bw,meta:{navigationBar:{titleText:"数据分析",type:"default"},isNVue:!1}}].map((e=>(e.meta.route=(e.alias||e.path).slice(1),e)));const Vw=e=>(t,n=Ni())=>{!Hi&&ir(e,t,n)},qw=Vw(ne),Ww=Vw(re),Hw=Vw(ce),zw=Vw(ue),Uw=Vw(ge),Yw=Vw(ye);
/*!
  * pinia v2.0.20
  * (c) 2022 Eduardo San Martin Morote
  * @license MIT
  */
let Xw;const Gw=e=>Xw=e,Jw=Symbol();function Kw(e){return e&&"object"==typeof e&&"[object Object]"===Object.prototype.toString.call(e)&&"function"!=typeof e.toJSON}var Zw,Qw;(Qw=Zw||(Zw={})).direct="direct",Qw.patchObject="patch object",Qw.patchFunction="patch function";const ex=()=>{};function tx(e,t,n,o=ex){e.push(t);const r=()=>{const n=e.indexOf(t);n>-1&&(e.splice(n,1),o())};return!n&&Ni()&&fr(r),r}function nx(e,...t){e.slice().forEach((e=>{e(...t)}))}function ox(e,t){for(const n in t){if(!t.hasOwnProperty(n))continue;const o=t[n],r=e[n];Kw(r)&&Kw(o)&&e.hasOwnProperty(n)&&!In(o)&&!_n(o)?e[n]=ox(r,o):e[n]=o}return e}const rx=Symbol();const{assign:ix}=Object;function ax(e,t,n,o){const{state:r,actions:i,getters:a}=t,s=n.state.value[e];let l;return l=sx(e,(function(){s||(n.state.value[e]=r?r():{});const t=function(e){const t=C(e)?new Array(e.length):{};for(const n in e)t[n]=Nn(e,n);return t}(n.state.value[e]);return ix(t,i,Object.keys(a||{}).reduce(((t,o)=>(t[o]=Sn(Gi((()=>{Gw(n);const t=n._s.get(e);return a[o].call(t,t)}))),t)),{}))}),t,n,o,!0),l.$reset=function(){const e=r?r():{};this.$patch((t=>{ix(t,e)}))},l}function sx(e,t,n={},o,r,i){let a;const s=ix({actions:{}},n),l={deep:!0};let c,u,d,f=Sn([]),h=Sn([]);const p=o.state.value[e];let g;function m(t){let n;c=u=!1,"function"==typeof t?(t(o.state.value[e]),n={type:Zw.patchFunction,storeId:e,events:d}):(ox(o.state.value[e],t),n={type:Zw.patchObject,payload:t,storeId:e,events:d});const r=g=Symbol();eo().then((()=>{g===r&&(c=!0)})),u=!0,nx(f,n,o.state.value[e])}i||p||(o.state.value[e]={}),An({});const v=ex;function y(t,n){return function(){Gw(o);const r=Array.from(arguments),i=[],a=[];let s;nx(h,{args:r,name:t,store:_,after:function(e){i.push(e)},onError:function(e){a.push(e)}});try{s=n.apply(this&&this.$id===e?this:_,r)}catch(l){throw nx(a,l),l}return s instanceof Promise?s.then((e=>(nx(i,e),e))).catch((e=>(nx(a,e),Promise.reject(e)))):(nx(i,s),s)}}const b={_p:o,$id:e,$onAction:tx.bind(null,h),$patch:m,$reset:v,$subscribe(t,n={}){const r=tx(f,t,n.detached,(()=>i())),i=a.run((()=>Co((()=>o.state.value[e]),(o=>{("sync"===n.flush?u:c)&&t({storeId:e,type:Zw.direct,events:d},o)}),ix({},l,n))));return r},$dispose:function(){a.stop(),f=[],h=[],o._s.delete(e)}},_=mn(ix({},b));o._s.set(e,_);const w=o._e.run((()=>(a=ct(),a.run((()=>t())))));for(const k in w){const t=w[k];if(In(t)&&(!In(T=t)||!T.effect)||_n(t))i||(!p||Kw(x=t)&&x.hasOwnProperty(rx)||(In(t)?t.value=p[k]:ox(t,p[k])),o.state.value[e][k]=t);else if("function"==typeof t){const e=y(k,t);w[k]=e,s.actions[k]=t}}var x,T;return ix(_,w),ix(kn(_),w),Object.defineProperty(_,"$state",{get:()=>o.state.value[e],set:e=>{m((t=>{ix(t,e)}))}}),o._p.forEach((e=>{ix(_,a.run((()=>e({store:_,app:o._a,pinia:o,options:s}))))})),p&&i&&n.hydrate&&n.hydrate(_.$state,p),c=!0,u=!0,_}function lx(e,t,n){let o,r;const i="function"==typeof t;function a(e,n){const a=Ni();(e=e||a&&To(Jw))&&Gw(e),(e=Xw)._s.has(o)||(i?sx(o,t,r,e):ax(o,r,e));return e._s.get(o)}return"string"==typeof e?(o=e,r=i?n:t):(r=e,o=e.id),a.$id=o,a}function cx(e){{e=kn(e);const t={};for(const n in e){const o=e[n];(In(o)||_n(o))&&(t[n]=Nn(e,n))}return t}}const ux="object"==typeof global&&global&&global.Object===Object&&global;var dx="object"==typeof self&&self&&self.Object===Object&&self;const fx=ux||dx||Function("return this")();const hx=fx.Symbol;var px=Object.prototype,gx=px.hasOwnProperty,mx=px.toString,vx=hx?hx.toStringTag:void 0;var yx=Object.prototype.toString;var bx=hx?hx.toStringTag:void 0;function _x(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":bx&&bx in Object(e)?function(e){var t=gx.call(e,vx),n=e[vx];try{e[vx]=void 0;var o=!0}catch(zE){}var r=mx.call(e);return o&&(t?e[vx]=n:delete e[vx]),r}(e):function(e){return yx.call(e)}(e)}function xx(e){return null!=e&&"object"==typeof e}const Tx=Array.isArray;function kx(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function Sx(e){return e}function Cx(e){if(!kx(e))return!1;var t=_x(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}const Ex=fx["__core-js_shared__"];var Ox,Mx=(Ox=/[^.]+$/.exec(Ex&&Ex.keys&&Ex.keys.IE_PROTO||""))?"Symbol(src)_1."+Ox:"";var Ix=Function.prototype.toString;var Ax=/^\[object .+?Constructor\]$/,Px=Function.prototype,$x=Object.prototype,Lx=Px.toString,Rx=$x.hasOwnProperty,jx=RegExp("^"+Lx.call(Rx).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function Bx(e){return!(!kx(e)||(t=e,Mx&&Mx in t))&&(Cx(e)?jx:Ax).test(function(e){if(null!=e){try{return Ix.call(e)}catch(zE){}try{return e+""}catch(zE){}}return""}(e));var t}function Dx(e,t){var n=function(e,t){return null==e?void 0:e[t]}(e,t);return Bx(n)?n:void 0}var Nx=Object.create;const Fx=function(){function e(){}return function(t){if(!kx(t))return{};if(Nx)return Nx(t);e.prototype=t;var n=new e;return e.prototype=void 0,n}}();var Vx=Date.now;var qx=function(){try{var e=Dx(Object,"defineProperty");return e({},"",{}),e}catch(zE){}}();const Wx=qx;var Hx=Wx?function(e,t){return Wx(e,"toString",{configurable:!0,enumerable:!1,value:(n=t,function(){return n}),writable:!0});var n}:Sx;var zx,Ux,Yx;const Xx=(zx=Hx,Ux=0,Yx=0,function(){var e=Vx(),t=16-(e-Yx);if(Yx=e,t>0){if(++Ux>=800)return arguments[0]}else Ux=0;return zx.apply(void 0,arguments)});var Gx=/^(?:0|[1-9]\d*)$/;function Jx(e,t){var n=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==n||"symbol"!=n&&Gx.test(e))&&e>-1&&e%1==0&&e<t}function Kx(e,t,n){"__proto__"==t&&Wx?Wx(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function Zx(e,t){return e===t||e!=e&&t!=t}var Qx=Object.prototype.hasOwnProperty;function eT(e,t,n){var o=e[t];Qx.call(e,t)&&Zx(o,n)&&(void 0!==n||t in e)||Kx(e,t,n)}var tT=Math.max;function nT(e,t){return Xx(function(e,t,n){return t=tT(void 0===t?e.length-1:t,0),function(){for(var o=arguments,r=-1,i=tT(o.length-t,0),a=Array(i);++r<i;)a[r]=o[t+r];r=-1;for(var s=Array(t+1);++r<t;)s[r]=o[r];return s[t]=n(a),function(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}(e,this,s)}}(e,t,Sx),e+"")}function oT(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}function rT(e){return null!=e&&oT(e.length)&&!Cx(e)}var iT=Object.prototype;function aT(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||iT)}function sT(e){return xx(e)&&"[object Arguments]"==_x(e)}var lT=Object.prototype,cT=lT.hasOwnProperty,uT=lT.propertyIsEnumerable;const dT=sT(function(){return arguments}())?sT:function(e){return xx(e)&&cT.call(e,"callee")&&!uT.call(e,"callee")};var fT="object"==typeof exports&&exports&&!exports.nodeType&&exports,hT=fT&&"object"==typeof module&&module&&!module.nodeType&&module,pT=hT&&hT.exports===fT?fx.Buffer:void 0;const gT=(pT?pT.isBuffer:void 0)||function(){return!1};var mT={};mT["[object Float32Array]"]=mT["[object Float64Array]"]=mT["[object Int8Array]"]=mT["[object Int16Array]"]=mT["[object Int32Array]"]=mT["[object Uint8Array]"]=mT["[object Uint8ClampedArray]"]=mT["[object Uint16Array]"]=mT["[object Uint32Array]"]=!0,mT["[object Arguments]"]=mT["[object Array]"]=mT["[object ArrayBuffer]"]=mT["[object Boolean]"]=mT["[object DataView]"]=mT["[object Date]"]=mT["[object Error]"]=mT["[object Function]"]=mT["[object Map]"]=mT["[object Number]"]=mT["[object Object]"]=mT["[object RegExp]"]=mT["[object Set]"]=mT["[object String]"]=mT["[object WeakMap]"]=!1;var vT="object"==typeof exports&&exports&&!exports.nodeType&&exports,yT=vT&&"object"==typeof module&&module&&!module.nodeType&&module,bT=yT&&yT.exports===vT&&ux.process,_T=function(){try{var e=yT&&yT.require&&yT.require("util").types;return e||bT&&bT.binding&&bT.binding("util")}catch(zE){}}();var wT=_T&&_T.isTypedArray,xT=wT?function(e){return function(t){return e(t)}}(wT):function(e){return xx(e)&&oT(e.length)&&!!mT[_x(e)]};const TT=xT;var kT=Object.prototype.hasOwnProperty;function ST(e,t){var n=Tx(e),o=!n&&dT(e),r=!n&&!o&&gT(e),i=!n&&!o&&!r&&TT(e),a=n||o||r||i,s=a?function(e,t){for(var n=-1,o=Array(e);++n<e;)o[n]=t(n);return o}(e.length,String):[],l=s.length;for(var c in e)!t&&!kT.call(e,c)||a&&("length"==c||r&&("offset"==c||"parent"==c)||i&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||Jx(c,l))||s.push(c);return s}var CT=Object.prototype.hasOwnProperty;function ET(e){if(!kx(e))return function(e){var t=[];if(null!=e)for(var n in Object(e))t.push(n);return t}(e);var t=aT(e),n=[];for(var o in e)("constructor"!=o||!t&&CT.call(e,o))&&n.push(o);return n}function OT(e){return rT(e)?ST(e,!0):ET(e)}const MT=Dx(Object,"create");var IT=Object.prototype.hasOwnProperty;var AT=Object.prototype.hasOwnProperty;function PT(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}function $T(e,t){for(var n=e.length;n--;)if(Zx(e[n][0],t))return n;return-1}PT.prototype.clear=function(){this.__data__=MT?MT(null):{},this.size=0},PT.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},PT.prototype.get=function(e){var t=this.__data__;if(MT){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return IT.call(t,e)?t[e]:void 0},PT.prototype.has=function(e){var t=this.__data__;return MT?void 0!==t[e]:AT.call(t,e)},PT.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=MT&&void 0===t?"__lodash_hash_undefined__":t,this};var LT=Array.prototype.splice;function RT(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}RT.prototype.clear=function(){this.__data__=[],this.size=0},RT.prototype.delete=function(e){var t=this.__data__,n=$T(t,e);return!(n<0)&&(n==t.length-1?t.pop():LT.call(t,n,1),--this.size,!0)},RT.prototype.get=function(e){var t=this.__data__,n=$T(t,e);return n<0?void 0:t[n][1]},RT.prototype.has=function(e){return $T(this.__data__,e)>-1},RT.prototype.set=function(e,t){var n=this.__data__,o=$T(n,e);return o<0?(++this.size,n.push([e,t])):n[o][1]=t,this};const jT=Dx(fx,"Map");function BT(e,t){var n,o,r=e.__data__;return("string"==(o=typeof(n=t))||"number"==o||"symbol"==o||"boolean"==o?"__proto__"!==n:null===n)?r["string"==typeof t?"string":"hash"]:r.map}function DT(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}DT.prototype.clear=function(){this.size=0,this.__data__={hash:new PT,map:new(jT||RT),string:new PT}},DT.prototype.delete=function(e){var t=BT(this,e).delete(e);return this.size-=t?1:0,t},DT.prototype.get=function(e){return BT(this,e).get(e)},DT.prototype.has=function(e){return BT(this,e).has(e)},DT.prototype.set=function(e,t){var n=BT(this,e),o=n.size;return n.set(e,t),this.size+=n.size==o?0:1,this};var NT=function(e,t){return function(n){return e(t(n))}}(Object.getPrototypeOf,Object);const FT=NT;var VT=Function.prototype,qT=Object.prototype,WT=VT.toString,HT=qT.hasOwnProperty,zT=WT.call(Object);function UT(e){var t=this.__data__=new RT(e);this.size=t.size}UT.prototype.clear=function(){this.__data__=new RT,this.size=0},UT.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},UT.prototype.get=function(e){return this.__data__.get(e)},UT.prototype.has=function(e){return this.__data__.has(e)},UT.prototype.set=function(e,t){var n=this.__data__;if(n instanceof RT){var o=n.__data__;if(!jT||o.length<199)return o.push([e,t]),this.size=++n.size,this;n=this.__data__=new DT(o)}return n.set(e,t),this.size=n.size,this};var YT="object"==typeof exports&&exports&&!exports.nodeType&&exports,XT=YT&&"object"==typeof module&&module&&!module.nodeType&&module,GT=XT&&XT.exports===YT?fx.Buffer:void 0,JT=GT?GT.allocUnsafe:void 0;const KT=fx.Uint8Array;function ZT(e,t){var n,o,r=t?(n=e.buffer,o=new n.constructor(n.byteLength),new KT(o).set(new KT(n)),o):e.buffer;return new e.constructor(r,e.byteOffset,e.length)}var QT,ek=function(e,t,n){for(var o=-1,r=Object(e),i=n(e),a=i.length;a--;){var s=i[QT?a:++o];if(!1===t(r[s],s,r))break}return e};const tk=ek;function nk(e,t,n){(void 0!==n&&!Zx(e[t],n)||void 0===n&&!(t in e))&&Kx(e,t,n)}function ok(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}function rk(e){return function(e,t,n,o){var r=!n;n||(n={});for(var i=-1,a=t.length;++i<a;){var s=t[i],l=o?o(n[s],e[s],s,n,e):void 0;void 0===l&&(l=e[s]),r?Kx(n,s,l):eT(n,s,l)}return n}(e,OT(e))}function ik(e,t,n,o,r,i,a){var s=ok(e,n),l=ok(t,n),c=a.get(l);if(c)nk(e,n,c);else{var u,d=i?i(s,l,n+"",e,t,a):void 0,f=void 0===d;if(f){var h=Tx(l),p=!h&&gT(l),g=!h&&!p&&TT(l);d=l,h||p||g?Tx(s)?d=s:xx(u=s)&&rT(u)?d=function(e,t){var n=-1,o=e.length;for(t||(t=Array(o));++n<o;)t[n]=e[n];return t}(s):p?(f=!1,d=function(e,t){if(t)return e.slice();var n=e.length,o=JT?JT(n):new e.constructor(n);return e.copy(o),o}(l,!0)):g?(f=!1,d=ZT(l,!0)):d=[]:function(e){if(!xx(e)||"[object Object]"!=_x(e))return!1;var t=FT(e);if(null===t)return!0;var n=HT.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&WT.call(n)==zT}(l)||dT(l)?(d=s,dT(s)?d=rk(s):kx(s)&&!Cx(s)||(d=function(e){return"function"!=typeof e.constructor||aT(e)?{}:Fx(FT(e))}(l))):f=!1}f&&(a.set(l,d),r(d,l,o,i,a),a.delete(l)),nk(e,n,d)}}function ak(e,t,n,o,r){e!==t&&tk(t,(function(i,a){if(r||(r=new UT),kx(i))ik(e,t,a,n,ak,o,r);else{var s=o?o(ok(e,a),i,a+"",e,t,r):void 0;void 0===s&&(s=i),nk(e,a,s)}}),OT)}var sk;const lk=(sk=function(e,t,n){ak(e,t,n)},nT((function(e,t){var n=-1,o=t.length,r=o>1?t[o-1]:void 0,i=o>2?t[2]:void 0;for(r=sk.length>3&&"function"==typeof r?(o--,r):void 0,i&&function(e,t,n){if(!kx(n))return!1;var o=typeof t;return!!("number"==o?rT(n)&&Jx(t,n.length):"string"==o&&t in n)&&Zx(n[t],e)}(t[0],t[1],i)&&(r=o<3?void 0:r,o=1),e=Object(e);++n<o;){var a=t[n];a&&sk(e,a,n,r)}return e})));var ck=(e=>(e.GET="GET",e.POST="POST",e))(ck||{}),uk=(e=>(e[e.SUCCESS=1]="SUCCESS",e[e.FAILED=0]="FAILED",e[e.TOKEN_INVALID=-1]="TOKEN_INVALID",e))(uk||{}),dk=(e=>(e.ABORT="request:fail abort",e.TIMEOUT="request:fail timeout",e))(dk||{});const fk=new Map,hk=class{static createInstance(){return this.instance??(this.instance=new hk)}add(e,t){this.remove(e),fk.has(e)&&fk.delete(e),fk.set(e,t)}remove(e){if(fk.has(e)){const t=fk.get(e);t&&t.abort(),fk.delete(e)}}};let pk=hk;t(pk,"instance");const gk=pk.createInstance();class mk{constructor(e){t(this,"options"),this.options=e}retryRequest(e,t){var n;const{retryCount:o,retryTimeout:r}=t;return o&&(null==(n=e.method)?void 0:n.toUpperCase())!=ck.POST?(Uy({title:"加载中..."}),t.hasRetryCount=t.hasRetryCount??0,t.hasRetryCount>=o?Promise.reject():(t.hasRetryCount++,t.requestHooks.requestInterceptorsHook=e=>e,new Promise((e=>setTimeout(e,r))).then((()=>this.request(e,t))).finally((()=>Yy())))):Promise.reject()}get(e,t){return this.request({...e,method:ck.GET},t)}post(e,t){return this.request({...e,method:ck.POST},t)}uploadFile(e,t){let n=lk({},this.options.requestOptions,e);const o=lk({},this.options,t),{requestInterceptorsHook:r,responseInterceptorsHook:i,responseInterceptorsCatchHook:a}=o.requestHooks||{};return r&&M(r)&&(n=r(n,o)),new Promise(((e,t)=>{my({...n,success:async n=>{if(200==n.statusCode){if(n.data=JSON.parse(n.data),i&&M(i)){try{n=await i(n,o),e(n)}catch(r){t(r)}return}e(n)}},fail:async e=>{a&&M(a)?t(await a(n,e)):t(e)}})}))}async request(e,t){let n=lk({},this.options.requestOptions,e);const o=lk({},this.options,t),{requestInterceptorsHook:r,responseInterceptorsHook:i,responseInterceptorsCatchHook:a}=o.requestHooks||{};return r&&M(r)&&(n=r(n,o)),new Promise(((t,r)=>{const s=fy({...n,async success(e){if(i&&M(i))try{e=await i(e,o),t(e)}catch(n){r(n)}else t(e)},fail:async e=>{e.errMsg!=dk.TIMEOUT?a&&M(a)?r(await a(n,e)):r(e):this.retryRequest(n,o).then((e=>t(e))).catch((e=>r(e)))},complete(t){t.errMsg!==dk.ABORT&&gk.remove(e.url)}}),{ignoreCancel:l}=o;!l&&gk.add(e.url,s)}))}}const vk="token",yk="history",bk="back_url",_k={key:"app_",set(e,t,n){e=this.getKey(e);let o={expire:n?this.time()+n:"",value:t};"object"==typeof o&&(o=JSON.stringify(o));try{Pv(e,o)}catch(zE){return null}},get(e){e=this.getKey(e);try{const t=Rv(e);if(!t)return null;const{value:n,expire:o}=JSON.parse(t);return o&&o<this.time()?(Bv(e),null):n}catch(zE){return null}},time:()=>Math.round((new Date).getTime()/1e3),remove(e){e=this.getKey(e),Bv(e)},getKey(e){return this.key+e}};function wk(){return _k.get(vk)}function xk(){return Ak.get({url:"/user/info"},{isAuth:!0})}function Tk(e){return Ak.post({url:"/user/setInfo",data:e},{isAuth:!0})}function kk(e,t){return Ak.post({url:"/user/bindMobile",data:e,header:t},{isAuth:!0})}function Sk(e){return Ak.post({url:"/user/changePassword",data:e},{isAuth:!0})}function Ck(e){return Ak.post({url:"/user/resetPassword",data:e})}function Ek(e){return Ak.get({url:"/account_log/lists",data:e})}function Ok(){return Ak.get({url:"/user/analysisData"},{isAuth:!0})}const Mk=lx({id:"userStore",state:()=>({userInfo:{},token:_k.get(vk)||null,temToken:null}),getters:{isLogin:e=>!!e.token},actions:{async getUser(){const e=await(t={token:this.token||this.temToken},Ak.get({url:"/user/center",header:t},{ignoreCancel:!0}));var t;this.userInfo=e},login(e){this.token=e,_k.set(vk,e)},logout(){this.token="",this.userInfo={},_k.remove(vk)}}});const Ik={version:"1.9.0",baseUrl:"http://movie.imdo.co/",urlPrefix:"api",timeout:6e4};const Ak=new mk(lk({requestOptions:{timeout:Ik.timeout},baseUrl:Ik.baseUrl,isReturnDefaultResponse:!1,isTransformResponse:!0,urlPrefix:"api",ignoreCancel:!1,withToken:!0,isAuth:!1,retryCount:2,retryTimeout:1e3,requestHooks:{requestInterceptorsHook(e,t){const{urlPrefix:n,baseUrl:o,withToken:r,isAuth:i}=t;e.header=e.header??{},n&&(e.url=`${n}${e.url}`),o&&(e.url=`${o}${e.url}`);const a=wk();return r&&!e.header.token&&(e.header.token=a),e.header.version=Ik.version,e},async responseInterceptorsHook(e,t){const{isTransformResponse:n,isReturnDefaultResponse:o,isAuth:r}=t;if(o)return e;if(!n)return e.data;const{logout:i}=Mk(),{code:a,data:s,msg:l,show:c}=e.data;switch(a){case uk.SUCCESS:return l&&c&&uni.$u.toast(l),s;case uk.FAILED:return uni.$u.toast(l),Promise.reject(l);case uk.TOKEN_INVALID:return i(),r&&!wk()&&by({url:"/pages/login/login"}),Promise.reject(l);default:return s}},async responseInterceptorsCatchHook(e,t){var n;return(null==(n=e.method)?void 0:n.toUpperCase())==ck.POST&&uni.$u.toast("请求失败，请重试"),Promise.reject(t)}}},Pk||{}));var Pk;function $k(e){return Ak.post({url:"/sms/sendCode",data:e})}function Lk(e){return Ak.get({url:"/index/policy",data:e})}function Rk(e,t){return Ak.uploadFile({url:"/upload/image",filePath:e,name:"file",header:{token:t},fileType:"image"})}function jk(e){return Ak.get({url:"/index/dict",data:e})}function Bk(e){return Ak.get({url:"/district/trees",data:e})}const Dk=lx({id:"appStore",state:()=>({config:{}}),getters:{getWebsiteConfig:e=>e.config.website||{},getLoginConfig:e=>e.config.login||{},getTabbarConfig:e=>e.config.tabbar||[],getStyleConfig:e=>e.config.style||{},getH5Config:e=>e.config.webPage||{},getCopyrightConfig:e=>e.config.copyright||[]},actions:{getImageUrl(e){return e.indexOf("http")?`${this.config.domain}${e}`:e},async getConfig(){const e=await Ak.get({url:"/index/config"});this.config=e}}});function Nk(){return Ak.get({url:"/index/index"})}function Fk(e){return Ak.get({url:"/index/decorate",data:e},{ignoreCancel:!0})}function Vk(){return Ak.get({url:"/search/hotLists"})}var qk,Wk,Hk={},zk=function(e,t,n){for(var o=0,r={},i=0;i<n.length;i++)if(e==n.substr(i,e.length))"start"in r||(r.start=i),o++;else if(t==n.substr(i,t.length)&&"start"in r&&! --o)return r.end=i,r.pre=n.substr(0,r.start),r.body=r.end-r.start>1?n.substring(r.start+e.length,r.end):"",r.post=n.slice(r.end+t.length),r},Uk={};qk={get exports(){return Uk},set exports(e){Uk=e}},Wk=function(){function e(t,o,r,i){"object"==typeof o&&(r=o.depth,i=o.prototype,o.filter,o=o.circular);var a=[],s=[],l="undefined"!=typeof Buffer;return void 0===o&&(o=!0),void 0===r&&(r=1/0),function t(r,c){if(null===r)return null;if(0==c)return r;var u,d;if("object"!=typeof r)return r;if(e.__isArray(r))u=[];else if(e.__isRegExp(r))u=new RegExp(r.source,n(r)),r.lastIndex&&(u.lastIndex=r.lastIndex);else if(e.__isDate(r))u=new Date(r.getTime());else{if(l&&Buffer.isBuffer(r))return u=Buffer.allocUnsafe?Buffer.allocUnsafe(r.length):new Buffer(r.length),r.copy(u),u;void 0===i?(d=Object.getPrototypeOf(r),u=Object.create(d)):(u=Object.create(i),d=i)}if(o){var f=a.indexOf(r);if(-1!=f)return s[f];a.push(r),s.push(u)}for(var h in r){var p;d&&(p=Object.getOwnPropertyDescriptor(d,h)),p&&null==p.set||(u[h]=t(r[h],c-1))}return u}(t,r)}function t(e){return Object.prototype.toString.call(e)}function n(e){var t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),t}return e.clonePrototype=function(e){if(null===e)return null;var t=function(){};return t.prototype=e,new t},e.__objToStr=t,e.__isDate=function(e){return"object"==typeof e&&"[object Date]"===t(e)},e.__isArray=function(e){return"object"==typeof e&&"[object Array]"===t(e)},e.__isRegExp=function(e){return"object"==typeof e&&"[object RegExp]"===t(e)},e.__getRegExpFlags=n,e}(),qk.exports&&(qk.exports=Wk);var Yk={},Xk={get exports(){return Yk},set exports(e){Yk=e}},Gk={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},Jk=Gk,Kk={};for(var Zk in Jk)Jk.hasOwnProperty(Zk)&&(Kk[Jk[Zk]]=Zk);var Qk=Xk.exports={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};for(var eS in Qk)if(Qk.hasOwnProperty(eS)){if(!("channels"in Qk[eS]))throw new Error("missing channels property: "+eS);if(!("labels"in Qk[eS]))throw new Error("missing channel labels property: "+eS);if(Qk[eS].labels.length!==Qk[eS].channels)throw new Error("channel and label counts mismatch: "+eS);var tS=Qk[eS].channels,nS=Qk[eS].labels;delete Qk[eS].channels,delete Qk[eS].labels,Object.defineProperty(Qk[eS],"channels",{value:tS}),Object.defineProperty(Qk[eS],"labels",{value:nS})}Qk.rgb.hsl=function(e){var t,n,o=e[0]/255,r=e[1]/255,i=e[2]/255,a=Math.min(o,r,i),s=Math.max(o,r,i),l=s-a;return s===a?t=0:o===s?t=(r-i)/l:r===s?t=2+(i-o)/l:i===s&&(t=4+(o-r)/l),(t=Math.min(60*t,360))<0&&(t+=360),n=(a+s)/2,[t,100*(s===a?0:n<=.5?l/(s+a):l/(2-s-a)),100*n]},Qk.rgb.hsv=function(e){var t,n,o,r,i,a=e[0]/255,s=e[1]/255,l=e[2]/255,c=Math.max(a,s,l),u=c-Math.min(a,s,l),d=function(e){return(c-e)/6/u+.5};return 0===u?r=i=0:(i=u/c,t=d(a),n=d(s),o=d(l),a===c?r=o-n:s===c?r=1/3+t-o:l===c&&(r=2/3+n-t),r<0?r+=1:r>1&&(r-=1)),[360*r,100*i,100*c]},Qk.rgb.hwb=function(e){var t=e[0],n=e[1],o=e[2];return[Qk.rgb.hsl(e)[0],100*(1/255*Math.min(t,Math.min(n,o))),100*(o=1-1/255*Math.max(t,Math.max(n,o)))]},Qk.rgb.cmyk=function(e){var t,n=e[0]/255,o=e[1]/255,r=e[2]/255;return[100*((1-n-(t=Math.min(1-n,1-o,1-r)))/(1-t)||0),100*((1-o-t)/(1-t)||0),100*((1-r-t)/(1-t)||0),100*t]},Qk.rgb.keyword=function(e){var t=Kk[e];if(t)return t;var n,o,r,i=1/0;for(var a in Jk)if(Jk.hasOwnProperty(a)){var s=Jk[a],l=(o=e,r=s,Math.pow(o[0]-r[0],2)+Math.pow(o[1]-r[1],2)+Math.pow(o[2]-r[2],2));l<i&&(i=l,n=a)}return n},Qk.keyword.rgb=function(e){return Jk[e]},Qk.rgb.xyz=function(e){var t=e[0]/255,n=e[1]/255,o=e[2]/255;return[100*(.4124*(t=t>.04045?Math.pow((t+.055)/1.055,2.4):t/12.92)+.3576*(n=n>.04045?Math.pow((n+.055)/1.055,2.4):n/12.92)+.1805*(o=o>.04045?Math.pow((o+.055)/1.055,2.4):o/12.92)),100*(.2126*t+.7152*n+.0722*o),100*(.0193*t+.1192*n+.9505*o)]},Qk.rgb.lab=function(e){var t=Qk.rgb.xyz(e),n=t[0],o=t[1],r=t[2];return o/=100,r/=108.883,n=(n/=95.047)>.008856?Math.pow(n,1/3):7.787*n+16/116,[116*(o=o>.008856?Math.pow(o,1/3):7.787*o+16/116)-16,500*(n-o),200*(o-(r=r>.008856?Math.pow(r,1/3):7.787*r+16/116))]},Qk.hsl.rgb=function(e){var t,n,o,r,i,a=e[0]/360,s=e[1]/100,l=e[2]/100;if(0===s)return[i=255*l,i,i];t=2*l-(n=l<.5?l*(1+s):l+s-l*s),r=[0,0,0];for(var c=0;c<3;c++)(o=a+1/3*-(c-1))<0&&o++,o>1&&o--,i=6*o<1?t+6*(n-t)*o:2*o<1?n:3*o<2?t+(n-t)*(2/3-o)*6:t,r[c]=255*i;return r},Qk.hsl.hsv=function(e){var t=e[0],n=e[1]/100,o=e[2]/100,r=n,i=Math.max(o,.01);return n*=(o*=2)<=1?o:2-o,r*=i<=1?i:2-i,[t,100*(0===o?2*r/(i+r):2*n/(o+n)),100*((o+n)/2)]},Qk.hsv.rgb=function(e){var t=e[0]/60,n=e[1]/100,o=e[2]/100,r=Math.floor(t)%6,i=t-Math.floor(t),a=255*o*(1-n),s=255*o*(1-n*i),l=255*o*(1-n*(1-i));switch(o*=255,r){case 0:return[o,l,a];case 1:return[s,o,a];case 2:return[a,o,l];case 3:return[a,s,o];case 4:return[l,a,o];case 5:return[o,a,s]}},Qk.hsv.hsl=function(e){var t,n,o,r=e[0],i=e[1]/100,a=e[2]/100,s=Math.max(a,.01);return o=(2-i)*a,n=i*s,[r,100*(n=(n/=(t=(2-i)*s)<=1?t:2-t)||0),100*(o/=2)]},Qk.hwb.rgb=function(e){var t,n,o,r,i,a,s,l=e[0]/360,c=e[1]/100,u=e[2]/100,d=c+u;switch(d>1&&(c/=d,u/=d),o=6*l-(t=Math.floor(6*l)),1&t&&(o=1-o),r=c+o*((n=1-u)-c),t){default:case 6:case 0:i=n,a=r,s=c;break;case 1:i=r,a=n,s=c;break;case 2:i=c,a=n,s=r;break;case 3:i=c,a=r,s=n;break;case 4:i=r,a=c,s=n;break;case 5:i=n,a=c,s=r}return[255*i,255*a,255*s]},Qk.cmyk.rgb=function(e){var t=e[0]/100,n=e[1]/100,o=e[2]/100,r=e[3]/100;return[255*(1-Math.min(1,t*(1-r)+r)),255*(1-Math.min(1,n*(1-r)+r)),255*(1-Math.min(1,o*(1-r)+r))]},Qk.xyz.rgb=function(e){var t,n,o,r=e[0]/100,i=e[1]/100,a=e[2]/100;return n=-.9689*r+1.8758*i+.0415*a,o=.0557*r+-.204*i+1.057*a,t=(t=3.2406*r+-1.5372*i+-.4986*a)>.0031308?1.055*Math.pow(t,1/2.4)-.055:12.92*t,n=n>.0031308?1.055*Math.pow(n,1/2.4)-.055:12.92*n,o=o>.0031308?1.055*Math.pow(o,1/2.4)-.055:12.92*o,[255*(t=Math.min(Math.max(0,t),1)),255*(n=Math.min(Math.max(0,n),1)),255*(o=Math.min(Math.max(0,o),1))]},Qk.xyz.lab=function(e){var t=e[0],n=e[1],o=e[2];return n/=100,o/=108.883,t=(t/=95.047)>.008856?Math.pow(t,1/3):7.787*t+16/116,[116*(n=n>.008856?Math.pow(n,1/3):7.787*n+16/116)-16,500*(t-n),200*(n-(o=o>.008856?Math.pow(o,1/3):7.787*o+16/116))]},Qk.lab.xyz=function(e){var t,n,o,r=e[0];t=e[1]/500+(n=(r+16)/116),o=n-e[2]/200;var i=Math.pow(n,3),a=Math.pow(t,3),s=Math.pow(o,3);return n=i>.008856?i:(n-16/116)/7.787,t=a>.008856?a:(t-16/116)/7.787,o=s>.008856?s:(o-16/116)/7.787,[t*=95.047,n*=100,o*=108.883]},Qk.lab.lch=function(e){var t,n=e[0],o=e[1],r=e[2];return(t=360*Math.atan2(r,o)/2/Math.PI)<0&&(t+=360),[n,Math.sqrt(o*o+r*r),t]},Qk.lch.lab=function(e){var t,n=e[0],o=e[1];return t=e[2]/360*2*Math.PI,[n,o*Math.cos(t),o*Math.sin(t)]},Qk.rgb.ansi16=function(e){var t=e[0],n=e[1],o=e[2],r=1 in arguments?arguments[1]:Qk.rgb.hsv(e)[2];if(0===(r=Math.round(r/50)))return 30;var i=30+(Math.round(o/255)<<2|Math.round(n/255)<<1|Math.round(t/255));return 2===r&&(i+=60),i},Qk.hsv.ansi16=function(e){return Qk.rgb.ansi16(Qk.hsv.rgb(e),e[2])},Qk.rgb.ansi256=function(e){var t=e[0],n=e[1],o=e[2];return t===n&&n===o?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(n/255*5)+Math.round(o/255*5)},Qk.ansi16.rgb=function(e){var t=e%10;if(0===t||7===t)return e>50&&(t+=3.5),[t=t/10.5*255,t,t];var n=.5*(1+~~(e>50));return[(1&t)*n*255,(t>>1&1)*n*255,(t>>2&1)*n*255]},Qk.ansi256.rgb=function(e){if(e>=232){var t=10*(e-232)+8;return[t,t,t]}var n;return e-=16,[Math.floor(e/36)/5*255,Math.floor((n=e%36)/6)/5*255,n%6/5*255]},Qk.rgb.hex=function(e){var t=(((255&Math.round(e[0]))<<16)+((255&Math.round(e[1]))<<8)+(255&Math.round(e[2]))).toString(16).toUpperCase();return"000000".substring(t.length)+t},Qk.hex.rgb=function(e){var t=e.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!t)return[0,0,0];var n=t[0];3===t[0].length&&(n=n.split("").map((function(e){return e+e})).join(""));var o=parseInt(n,16);return[o>>16&255,o>>8&255,255&o]},Qk.rgb.hcg=function(e){var t,n=e[0]/255,o=e[1]/255,r=e[2]/255,i=Math.max(Math.max(n,o),r),a=Math.min(Math.min(n,o),r),s=i-a;return t=s<=0?0:i===n?(o-r)/s%6:i===o?2+(r-n)/s:4+(n-o)/s+4,t/=6,[360*(t%=1),100*s,100*(s<1?a/(1-s):0)]},Qk.hsl.hcg=function(e){var t=e[1]/100,n=e[2]/100,o=1,r=0;return(o=n<.5?2*t*n:2*t*(1-n))<1&&(r=(n-.5*o)/(1-o)),[e[0],100*o,100*r]},Qk.hsv.hcg=function(e){var t=e[1]/100,n=e[2]/100,o=t*n,r=0;return o<1&&(r=(n-o)/(1-o)),[e[0],100*o,100*r]},Qk.hcg.rgb=function(e){var t=e[0]/360,n=e[1]/100,o=e[2]/100;if(0===n)return[255*o,255*o,255*o];var r,i=[0,0,0],a=t%1*6,s=a%1,l=1-s;switch(Math.floor(a)){case 0:i[0]=1,i[1]=s,i[2]=0;break;case 1:i[0]=l,i[1]=1,i[2]=0;break;case 2:i[0]=0,i[1]=1,i[2]=s;break;case 3:i[0]=0,i[1]=l,i[2]=1;break;case 4:i[0]=s,i[1]=0,i[2]=1;break;default:i[0]=1,i[1]=0,i[2]=l}return r=(1-n)*o,[255*(n*i[0]+r),255*(n*i[1]+r),255*(n*i[2]+r)]},Qk.hcg.hsv=function(e){var t=e[1]/100,n=t+e[2]/100*(1-t),o=0;return n>0&&(o=t/n),[e[0],100*o,100*n]},Qk.hcg.hsl=function(e){var t=e[1]/100,n=e[2]/100*(1-t)+.5*t,o=0;return n>0&&n<.5?o=t/(2*n):n>=.5&&n<1&&(o=t/(2*(1-n))),[e[0],100*o,100*n]},Qk.hcg.hwb=function(e){var t=e[1]/100,n=t+e[2]/100*(1-t);return[e[0],100*(n-t),100*(1-n)]},Qk.hwb.hcg=function(e){var t=e[1]/100,n=1-e[2]/100,o=n-t,r=0;return o<1&&(r=(n-o)/(1-o)),[e[0],100*o,100*r]},Qk.apple.rgb=function(e){return[e[0]/65535*255,e[1]/65535*255,e[2]/65535*255]},Qk.rgb.apple=function(e){return[e[0]/255*65535,e[1]/255*65535,e[2]/255*65535]},Qk.gray.rgb=function(e){return[e[0]/100*255,e[0]/100*255,e[0]/100*255]},Qk.gray.hsl=Qk.gray.hsv=function(e){return[0,0,e[0]]},Qk.gray.hwb=function(e){return[0,100,e[0]]},Qk.gray.cmyk=function(e){return[0,0,0,e[0]]},Qk.gray.lab=function(e){return[e[0],0,0]},Qk.gray.hex=function(e){var t=255&Math.round(e[0]/100*255),n=((t<<16)+(t<<8)+t).toString(16).toUpperCase();return"000000".substring(n.length)+n},Qk.rgb.gray=function(e){return[(e[0]+e[1]+e[2])/3/255*100]};var oS=Yk;function rS(e){var t=function(){for(var e={},t=Object.keys(oS),n=t.length,o=0;o<n;o++)e[t[o]]={distance:-1,parent:null};return e}(),n=[e];for(t[e].distance=0;n.length;)for(var o=n.pop(),r=Object.keys(oS[o]),i=r.length,a=0;a<i;a++){var s=r[a],l=t[s];-1===l.distance&&(l.distance=t[o].distance+1,l.parent=o,n.unshift(s))}return t}function iS(e,t){return function(n){return t(e(n))}}function aS(e,t){for(var n=[t[e].parent,e],o=oS[t[e].parent][e],r=t[e].parent;t[r].parent;)n.unshift(t[r].parent),o=iS(oS[t[r].parent][r],o),r=t[r].parent;return o.conversion=n,o}var sS=Yk,lS=function(e){for(var t=rS(e),n={},o=Object.keys(t),r=o.length,i=0;i<r;i++){var a=o[i];null!==t[a].parent&&(n[a]=aS(a,t))}return n},cS={};Object.keys(sS).forEach((function(e){cS[e]={},Object.defineProperty(cS[e],"channels",{value:sS[e].channels}),Object.defineProperty(cS[e],"labels",{value:sS[e].labels});var t=lS(e);Object.keys(t).forEach((function(n){var o=t[n];cS[e][n]=function(e){var t=function(t){if(null==t)return t;arguments.length>1&&(t=Array.prototype.slice.call(arguments));var n=e(t);if("object"==typeof n)for(var o=n.length,r=0;r<o;r++)n[r]=Math.round(n[r]);return n};return"conversion"in e&&(t.conversion=e.conversion),t}(o),cS[e][n].raw=function(e){var t=function(t){return null==t?t:(arguments.length>1&&(t=Array.prototype.slice.call(arguments)),e(t))};return"conversion"in e&&(t.conversion=e.conversion),t}(o)}))}));var uS=cS,dS=Gk,fS={getRgba:hS,getHsla:pS,getRgb:function(e){var t=hS(e);return t&&t.slice(0,3)},getHsl:function(e){var t=pS(e);return t&&t.slice(0,3)},getHwb:gS,getAlpha:function(e){var t=hS(e);if(t)return t[3];if(t=pS(e))return t[3];if(t=gS(e))return t[3]},hexString:function(e){return"#"+_S(e[0])+_S(e[1])+_S(e[2])},rgbString:function(e,t){if(t<1||e[3]&&e[3]<1)return mS(e,t);return"rgb("+e[0]+", "+e[1]+", "+e[2]+")"},rgbaString:mS,percentString:function(e,t){if(t<1||e[3]&&e[3]<1)return vS(e,t);var n=Math.round(e[0]/255*100),o=Math.round(e[1]/255*100),r=Math.round(e[2]/255*100);return"rgb("+n+"%, "+o+"%, "+r+"%)"},percentaString:vS,hslString:function(e,t){if(t<1||e[3]&&e[3]<1)return yS(e,t);return"hsl("+e[0]+", "+e[1]+"%, "+e[2]+"%)"},hslaString:yS,hwbString:function(e,t){void 0===t&&(t=void 0!==e[3]?e[3]:1);return"hwb("+e[0]+", "+e[1]+"%, "+e[2]+"%"+(void 0!==t&&1!==t?", "+t:"")+")"},keyword:function(e){return wS[e.slice(0,3)]}};function hS(e){if(e){var t=[0,0,0],n=1,o=e.match(/^#([a-fA-F0-9]{3})$/);if(o){o=o[1];for(var r=0;r<t.length;r++)t[r]=parseInt(o[r]+o[r],16)}else if(o=e.match(/^#([a-fA-F0-9]{6})$/)){o=o[1];for(r=0;r<t.length;r++)t[r]=parseInt(o.slice(2*r,2*r+2),16)}else if(o=e.match(/^rgba?\(\s*([+-]?\d+)\s*,\s*([+-]?\d+)\s*,\s*([+-]?\d+)\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)$/)){for(r=0;r<t.length;r++)t[r]=parseInt(o[r+1]);n=parseFloat(o[4])}else if(o=e.match(/^rgba?\(\s*([+-]?[\d\.]+)\%\s*,\s*([+-]?[\d\.]+)\%\s*,\s*([+-]?[\d\.]+)\%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)$/)){for(r=0;r<t.length;r++)t[r]=Math.round(2.55*parseFloat(o[r+1]));n=parseFloat(o[4])}else if(o=e.match(/(\D+)/)){if("transparent"==o[1])return[0,0,0,0];if(!(t=dS[o[1]]))return}for(r=0;r<t.length;r++)t[r]=bS(t[r],0,255);return n=n||0==n?bS(n,0,1):1,t[3]=n,t}}function pS(e){if(e){var t=e.match(/^hsla?\(\s*([+-]?\d+)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)/);if(t){var n=parseFloat(t[4]);return[bS(parseInt(t[1]),0,360),bS(parseFloat(t[2]),0,100),bS(parseFloat(t[3]),0,100),bS(isNaN(n)?1:n,0,1)]}}}function gS(e){if(e){var t=e.match(/^hwb\(\s*([+-]?\d+)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)/);if(t){var n=parseFloat(t[4]);return[bS(parseInt(t[1]),0,360),bS(parseFloat(t[2]),0,100),bS(parseFloat(t[3]),0,100),bS(isNaN(n)?1:n,0,1)]}}}function mS(e,t){return void 0===t&&(t=void 0!==e[3]?e[3]:1),"rgba("+e[0]+", "+e[1]+", "+e[2]+", "+t+")"}function vS(e,t){return"rgba("+Math.round(e[0]/255*100)+"%, "+Math.round(e[1]/255*100)+"%, "+Math.round(e[2]/255*100)+"%, "+(t||e[3]||1)+")"}function yS(e,t){return void 0===t&&(t=void 0!==e[3]?e[3]:1),"hsla("+e[0]+", "+e[1]+"%, "+e[2]+"%, "+t+")"}function bS(e,t,n){return Math.min(Math.max(t,e),n)}function _S(e){var t=e.toString(16).toUpperCase();return t.length<2?"0"+t:t}var wS={};for(var xS in dS)wS[dS[xS]]=xS;var TS=Uk,kS=uS,SS=fS,CS=function(e){if(e instanceof CS)return e;if(!(this instanceof CS))return new CS(e);var t;if(this.values={rgb:[0,0,0],hsl:[0,0,0],hsv:[0,0,0],hwb:[0,0,0],cmyk:[0,0,0,0],alpha:1},"string"==typeof e)if(t=SS.getRgba(e))this.setValues("rgb",t);else if(t=SS.getHsla(e))this.setValues("hsl",t);else{if(!(t=SS.getHwb(e)))throw new Error('Unable to parse color from string "'+e+'"');this.setValues("hwb",t)}else if("object"==typeof e)if(void 0!==(t=e).r||void 0!==t.red)this.setValues("rgb",t);else if(void 0!==t.l||void 0!==t.lightness)this.setValues("hsl",t);else if(void 0!==t.v||void 0!==t.value)this.setValues("hsv",t);else if(void 0!==t.w||void 0!==t.whiteness)this.setValues("hwb",t);else{if(void 0===t.c&&void 0===t.cyan)throw new Error("Unable to parse color from object "+JSON.stringify(e));this.setValues("cmyk",t)}};CS.prototype={rgb:function(){return this.setSpace("rgb",arguments)},hsl:function(){return this.setSpace("hsl",arguments)},hsv:function(){return this.setSpace("hsv",arguments)},hwb:function(){return this.setSpace("hwb",arguments)},cmyk:function(){return this.setSpace("cmyk",arguments)},rgbArray:function(){return this.values.rgb},hslArray:function(){return this.values.hsl},hsvArray:function(){return this.values.hsv},hwbArray:function(){return 1!==this.values.alpha?this.values.hwb.concat([this.values.alpha]):this.values.hwb},cmykArray:function(){return this.values.cmyk},rgbaArray:function(){return this.values.rgb.concat([this.values.alpha])},rgbaArrayNormalized:function(){for(var e=this.values.rgb,t=[],n=0;n<3;n++)t[n]=e[n]/255;return t.push(this.values.alpha),t},hslaArray:function(){return this.values.hsl.concat([this.values.alpha])},alpha:function(e){return void 0===e?this.values.alpha:(this.setValues("alpha",e),this)},red:function(e){return this.setChannel("rgb",0,e)},green:function(e){return this.setChannel("rgb",1,e)},blue:function(e){return this.setChannel("rgb",2,e)},hue:function(e){return e&&(e=(e%=360)<0?360+e:e),this.setChannel("hsl",0,e)},saturation:function(e){return this.setChannel("hsl",1,e)},lightness:function(e){return this.setChannel("hsl",2,e)},saturationv:function(e){return this.setChannel("hsv",1,e)},whiteness:function(e){return this.setChannel("hwb",1,e)},blackness:function(e){return this.setChannel("hwb",2,e)},value:function(e){return this.setChannel("hsv",2,e)},cyan:function(e){return this.setChannel("cmyk",0,e)},magenta:function(e){return this.setChannel("cmyk",1,e)},yellow:function(e){return this.setChannel("cmyk",2,e)},black:function(e){return this.setChannel("cmyk",3,e)},hexString:function(){return SS.hexString(this.values.rgb)},rgbString:function(){return SS.rgbString(this.values.rgb,this.values.alpha)},rgbaString:function(){return SS.rgbaString(this.values.rgb,this.values.alpha)},percentString:function(){return SS.percentString(this.values.rgb,this.values.alpha)},hslString:function(){return SS.hslString(this.values.hsl,this.values.alpha)},hslaString:function(){return SS.hslaString(this.values.hsl,this.values.alpha)},hwbString:function(){return SS.hwbString(this.values.hwb,this.values.alpha)},keyword:function(){return SS.keyword(this.values.rgb,this.values.alpha)},rgbNumber:function(){return this.values.rgb[0]<<16|this.values.rgb[1]<<8|this.values.rgb[2]},luminosity:function(){for(var e=this.values.rgb,t=[],n=0;n<e.length;n++){var o=e[n]/255;t[n]=o<=.03928?o/12.92:Math.pow((o+.055)/1.055,2.4)}return.2126*t[0]+.7152*t[1]+.0722*t[2]},contrast:function(e){var t=this.luminosity(),n=e.luminosity();return t>n?(t+.05)/(n+.05):(n+.05)/(t+.05)},level:function(e){var t=this.contrast(e);return t>=7.1?"AAA":t>=4.5?"AA":""},dark:function(){var e=this.values.rgb;return(299*e[0]+587*e[1]+114*e[2])/1e3<128},light:function(){return!this.dark()},negate:function(){for(var e=[],t=0;t<3;t++)e[t]=255-this.values.rgb[t];return this.setValues("rgb",e),this},lighten:function(e){return this.values.hsl[2]+=this.values.hsl[2]*e,this.setValues("hsl",this.values.hsl),this},darken:function(e){return this.values.hsl[2]-=this.values.hsl[2]*e,this.setValues("hsl",this.values.hsl),this},saturate:function(e){return this.values.hsl[1]+=this.values.hsl[1]*e,this.setValues("hsl",this.values.hsl),this},desaturate:function(e){return this.values.hsl[1]-=this.values.hsl[1]*e,this.setValues("hsl",this.values.hsl),this},whiten:function(e){return this.values.hwb[1]+=this.values.hwb[1]*e,this.setValues("hwb",this.values.hwb),this},blacken:function(e){return this.values.hwb[2]+=this.values.hwb[2]*e,this.setValues("hwb",this.values.hwb),this},greyscale:function(){var e=this.values.rgb,t=.3*e[0]+.59*e[1]+.11*e[2];return this.setValues("rgb",[t,t,t]),this},clearer:function(e){return this.setValues("alpha",this.values.alpha-this.values.alpha*e),this},opaquer:function(e){return this.setValues("alpha",this.values.alpha+this.values.alpha*e),this},rotate:function(e){var t=this.values.hsl[0];return t=(t=(t+e)%360)<0?360+t:t,this.values.hsl[0]=t,this.setValues("hsl",this.values.hsl),this},mix:function(e,t){var n=this,o=e,r=void 0===t?.5:t,i=2*r-1,a=n.alpha()-o.alpha(),s=((i*a===-1?i:(i+a)/(1+i*a))+1)/2,l=1-s;return this.rgb(s*n.red()+l*o.red(),s*n.green()+l*o.green(),s*n.blue()+l*o.blue()).alpha(n.alpha()*r+o.alpha()*(1-r))},toJSON:function(){return this.rgb()},clone:function(){var e=new CS;return e.values=TS(this.values),e}},CS.prototype.getValues=function(e){for(var t={},n=0;n<e.length;n++)t[e.charAt(n)]=this.values[e][n];return 1!==this.values.alpha&&(t.a=this.values.alpha),t},CS.prototype.setValues=function(e,t){var n,o,r={rgb:["red","green","blue"],hsl:["hue","saturation","lightness"],hsv:["hue","saturation","value"],hwb:["hue","whiteness","blackness"],cmyk:["cyan","magenta","yellow","black"]},i={rgb:[255,255,255],hsl:[360,100,100],hsv:[360,100,100],hwb:[360,100,100],cmyk:[100,100,100,100]},a=1;if("alpha"===e)a=t;else if(t.length)this.values[e]=t.slice(0,e.length),a=t[e.length];else if(void 0!==t[e.charAt(0)]){for(n=0;n<e.length;n++)this.values[e][n]=t[e.charAt(n)];a=t.a}else if(void 0!==t[r[e][0]]){var s=r[e];for(n=0;n<e.length;n++)this.values[e][n]=t[s[n]];a=t.alpha}if(this.values.alpha=Math.max(0,Math.min(1,void 0===a?this.values.alpha:a)),"alpha"===e)return!1;for(n=0;n<e.length;n++)o=Math.max(0,Math.min(i[e][n],this.values[e][n])),this.values[e][n]=Math.round(o);for(var l in r)for(l!==e&&(this.values[l]=kS[e][l](this.values[e])),n=0;n<l.length;n++)o=Math.max(0,Math.min(i[l][n],this.values[l][n])),this.values[l][n]=Math.round(o);return!0},CS.prototype.setSpace=function(e,t){var n=t[0];return void 0===n?this.getValues(e):("number"==typeof n&&(n=Array.prototype.slice.call(t)),this.setValues(e,n),this)},CS.prototype.setChannel=function(e,t,n){return void 0===n?this.values[e][t]:(n===this.values[e][t]||(this.values[e][t]=n,this.setValues(e,this.values[e])),this)};var ES,OS,MS=CS,IS={};function AS(){if(OS)return ES;OS=1;var e=1e3,t=60*e,n=60*t,o=24*n,r=7*o,i=365.25*o;function a(e,t,n,o){var r=t>=1.5*n;return Math.round(e/n)+" "+o+(r?"s":"")}return ES=function(s,l){l=l||{};var c=typeof s;if("string"===c&&s.length>0)return function(a){if((a=String(a)).length>100)return;var s=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(a);if(!s)return;var l=parseFloat(s[1]);switch((s[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return l*i;case"weeks":case"week":case"w":return l*r;case"days":case"day":case"d":return l*o;case"hours":case"hour":case"hrs":case"hr":case"h":return l*n;case"minutes":case"minute":case"mins":case"min":case"m":return l*t;case"seconds":case"second":case"secs":case"sec":case"s":return l*e;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return l;default:return}}(s);if("number"===c&&isFinite(s))return l.long?function(r){var i=Math.abs(r);if(i>=o)return a(r,i,o,"day");if(i>=n)return a(r,i,n,"hour");if(i>=t)return a(r,i,t,"minute");if(i>=e)return a(r,i,e,"second");return r+" ms"}(s):function(r){var i=Math.abs(r);if(i>=o)return Math.round(r/o)+"d";if(i>=n)return Math.round(r/n)+"h";if(i>=t)return Math.round(r/t)+"m";if(i>=e)return Math.round(r/e)+"s";return r+"ms"}(s);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(s))}}var PS=function(e){function t(e){for(var t=0,o=0;o<e.length;o++)t=(t<<5)-t+e.charCodeAt(o),t|=0;return n.colors[Math.abs(t)%n.colors.length]}function n(e){var i;function a(){if(a.enabled){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];var r=a,s=Number(new Date),l=s-(i||s);r.diff=l,r.prev=i,r.curr=s,i=s,t[0]=n.coerce(t[0]),"string"!=typeof t[0]&&t.unshift("%O");var c=0;t[0]=t[0].replace(/%([a-zA-Z%])/g,(function(e,o){if("%%"===e)return e;c++;var i=n.formatters[o];if("function"==typeof i){var a=t[c];e=i.call(r,a),t.splice(c,1),c--}return e})),n.formatArgs.call(r,t),(r.log||n.log).apply(r,t)}}return a.namespace=e,a.enabled=n.enabled(e),a.useColors=n.useColors(),a.color=t(e),a.destroy=o,a.extend=r,"function"==typeof n.init&&n.init(a),n.instances.push(a),a}function o(){var e=n.instances.indexOf(this);return-1!==e&&(n.instances.splice(e,1),!0)}function r(e,t){return n(this.namespace+(void 0===t?":":t)+e)}return n.debug=n,n.default=n,n.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},n.disable=function(){n.enable("")},n.enable=function(e){var t;n.save(e),n.names=[],n.skips=[];var o=("string"==typeof e?e:"").split(/[\s,]+/),r=o.length;for(t=0;t<r;t++)o[t]&&("-"===(e=o[t].replace(/\*/g,".*?"))[0]?n.skips.push(new RegExp("^"+e.substr(1)+"$")):n.names.push(new RegExp("^"+e+"$")));for(t=0;t<n.instances.length;t++){var i=n.instances[t];i.enabled=n.enabled(i.namespace)}},n.enabled=function(e){if("*"===e[e.length-1])return!0;var t,o;for(t=0,o=n.skips.length;t<o;t++)if(n.skips[t].test(e))return!1;for(t=0,o=n.names.length;t<o;t++)if(n.names[t].test(e))return!0;return!1},n.humanize=AS(),Object.keys(e).forEach((function(t){n[t]=e[t]})),n.instances=[],n.names=[],n.skips=[],n.formatters={},n.selectColor=t,n.enable(n.load()),n};!function(e,t){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}t.log=function(){var e;return"object"===("undefined"==typeof console?"undefined":n(console))&&console.log&&(e=console).log.apply(e,arguments)},t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;var n="color: "+this.color;t.splice(1,0,n,"color: inherit");var o=0,r=0;t[0].replace(/%[a-zA-Z%]/g,(function(e){"%%"!==e&&(o++,"%c"===e&&(r=o))})),t.splice(r,0,n)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(n){}},t.load=function(){var e;try{e=t.storage.getItem("debug")}catch(n){}!e&&"undefined"!=typeof process&&"env"in process&&(e={}.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],e.exports=PS(t),e.exports.formatters.j=function(e){try{return JSON.stringify(e)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}}}({get exports(){return IS},set exports(e){IS=e}},IS);var $S=zk,LS=IS("css-color-function:parse"),RS=function(e){"string"!=typeof e&&(e=e.toString());function t(t){var n=t.exec(e);if(n)return e=e.slice(n[0].length),n.slice(1)}function n(){t(/^\s+/)}function o(){var e=t(/^([\+\-\*])/);if(e){var n={type:"modifier"};return n.value=e[0],LS("modifier %o",n),n}}function r(){var e=t(/^([^\)\s]+)/);if(e){var n={type:"number"};return n.value=e[0],LS("number %o",n),n}}function i(){var e=t(/^(\w+)\(/);if(e){n();var i={type:"function"};return i.name=e[0],i.arguments=function(){for(var e,t=[];e=o()||s()||r();)t.push(e),n();return LS("args %o",t),t}(),function(){var e=t(/^\)/);if(e)LS("rparen")}(),LS("adjuster %o",i),i}}function a(){var e={type:"color"},o=t(/([^\)\s]+)/)[0];-1!=o.indexOf("(")&&(o+=t(/([^\)]*?\))/)[0]);return e.value=o,n(),e}function s(){if(e.match(/^color\(/)){var t=$S("(",")",e);if(!t)throw new SyntaxError("Missing closing parenthese for '"+e+"'");if(""===t.body)throw new SyntaxError("color() function cannot be empty");e=t.body,n();var o,r={};for(r.type="function",r.name="color",r.arguments=[s()||a()],LS("function arguments %o",r.arguments);o=i();)r.arguments.push(o),n();return e=t.post,n(),LS("function %o",r),r}}return LS("string %s",e),s()};var jS={};!function(e){var t=MS;function n(e){return function(t,n){var o;"modifier"==n[0].type&&(o=n.shift().value);var i=n[0].value;-1!=i.indexOf("%")?(i=parseInt(i,10)/100,o?"*"!=o&&(i=t[e]()*i):i*="alpha"==e?1:255):i=Number(i),t[e](r(t[e](),i,o))}}function o(e){return function(t,n){var o;"modifier"==n[0].type&&(o=n.shift().value);var i=parseFloat(n[0].value,10);t[e](r(t[e](),i,o))}}function r(e,t,n){switch(n){case"+":return e+t;case"-":return e-t;case"*":return e*t;default:return t}}e.red=n("red"),e.blue=n("blue"),e.green=n("green"),e.alpha=e.a=n("alpha"),e.rgb=function(){},e.hue=e.h=o("hue"),e.saturation=e.s=o("saturation"),e.lightness=e.l=o("lightness"),e.whiteness=e.w=o("whiteness"),e.blackness=e.b=o("blackness"),e.blend=function(e,n){var o=e.alpha();e.alpha(1);var r=new t(n[0].value),i=1-parseInt(n[1].value,10)/100;e.mix(r,i).alpha(o)},e.tint=function(t,n){n.unshift({type:"argument",value:"white"}),e.blend(t,n)},e.shade=function(t,n){n.unshift({type:"argument",value:"black"}),e.blend(t,n)},e.contrast=function(e,n){0==n.length&&n.push({type:"argument",value:"100%"});var o=1-parseInt(n[0].value,10)/100,r=e.luminosity()<.5?new t({h:e.hue(),w:100,b:0}):new t({h:e.hue(),w:0,b:100}),i=r;if(e.contrast(r)>4.5){i=function(e,t,n){t.hue();var o=t.clone(),r=t.whiteness(),i=t.blackness(),a=n.whiteness(),s=n.blackness();for(;Math.abs(r-a)>1||Math.abs(i-s)>1;){var l=Math.round((a+r)/2),c=Math.round((s+i)/2);o.whiteness(l),o.blackness(c),o.contrast(t)>e?(a=l,s=c):(r=l,i=c)}return o}(4.5,e,r);var a=i.alpha();i.alpha(1),i.mix(r,o).alpha(a)}e.hwb(i.hwb())}}(jS);var BS=zk,DS=MS,NS=RS,FS=jS,VS=function e(t){var n=t.indexOf("color(");if(-1==n)return t;if(t=t.slice(n),!(t=BS("(",")",t)))throw new SyntaxError("Missing closing parenthese for '"+t+"'");return qS(NS("color("+t.body+")"))+e(t.post)};function qS(e){var t=new DS("function"==e.arguments[0].type?qS(e.arguments[0]):e.arguments[0].value);return e.arguments.slice(1).forEach((function(e){var n=e.name;if(!FS[n])throw new Error("Unknown <color-adjuster> '"+n+"'");e.arguments.forEach((function(e){"function"==e.type&&"color"==e.name&&(e.value=qS(e),e.type="color",delete e.name)})),FS[n](t,e.arguments)})),t.rgbString()}var WS=VS,HS=RS;Hk.convert=WS,Hk.parse=HS;const zS={"dark-2":"shade(20%)","light-3":"tint(30%)","light-5":"tint(50%)","light-7":"tint(70%)","light-9":"tint(90%)"},US={"light-3":"shade(20%)","light-5":"shade(30%)","light-7":"shade(50%)","light-9":"shade(70%)","dark-2":"tint(20%)"},YS=(e,t={},n=!1)=>{const o=Object.keys(e).reduce(((t,o)=>Object.assign(t,((e,t="primary",n=!1)=>{const o={[`--color-${t}`]:e},r=n?US:zS;for(const i in r)o[`--color-${t}-${i}`]=`color(${e} ${r[i]})`;return o})(e[o],o,n))),t),r=Object.keys(o).reduce(((e,t)=>`${e}${t}:${Hk.convert(o[t])};`),"");return r},XS=lx({id:"themeStore",state:()=>({primaryColor:"",minorColor:"",btnColor:"white",navColor:"#000000",navBgColor:"#ffffff",vars:""}),actions:{async getTheme(){const e=await Fk({type:5}),{themeColor1:t,themeColor2:n,buttonColor:o,navigationBarColor:r,topTextColor:i}=JSON.parse(e.data);this.primaryColor=t,this.minorColor=n,this.btnColor=o,this.navColor="white"===i?"#ffffff":"#000000",this.navBgColor=r||t,this.vars=YS({primary:t},{"--color-minor":n,"--color-btn-text":o})},setTheme(e){this.primaryColor=e}}});function GS(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e,reset:function(){e=[]}}}const JS=/#/g,KS=/&/g,ZS=/\+/g,QS=/%5B/g,eC=/%5D/g,tC=/%5E/g,nC=/%60/g,oC=/%7B/g,rC=/%7C/g,iC=/%7D/g,aC=/%20/g;function sC(e){return function(e){return encodeURI(`${e}`).replace(rC,"|").replace(QS,"[").replace(eC,"]")}(e).replace(ZS,"%2B").replace(aC,"+").replace(JS,"%23").replace(KS,"%26").replace(nC,"`").replace(oC,"{").replace(iC,"}").replace(tC,"^")}function lC(e){try{return decodeURIComponent(`${e}`)}catch(t){cC(`Error decoding "${e}". Using original value`)}return`${e}`}function cC(e,t=!1,...n){t&&console.warn(`[uni-router warn]: ${e}`,...n)}const uC=Symbol("navigation failure"),dC={1:({location:e})=>`Navigation ${"string"==typeof e?e:JSON.stringify(e)} is not found`,2:({from:e,to:t})=>`Redirected from "${JSON.stringify(e)}" to "${JSON.stringify(t)}" via a navigation guard.`,4:({from:e,to:t})=>`Navigation aborted from "${JSON.stringify(e)}" to "${JSON.stringify(t)}" via a navigation guard.`,8:({from:e,to:t})=>`Navigation cancelled from "${JSON.stringify(e)}" to "${JSON.stringify(t)}" with a new navigation.`,16:({from:e,to:t})=>`Avoided redundant navigation to current location: "${JSON.stringify(e)}".`};function fC(e,t){return e instanceof Error&&uC in e&&(null==t||!!(e.type&t))}function hC(e,t){return Object.assign(new Error(dC[e](t)),{type:e,[uC]:!0},t)}const pC=Array.isArray,gC=e=>"string"==typeof e,mC=/(^mp-weixin$)|(^mp-baidu$)|(^mp-alipay$)|(^mp-toutiao$)|(^mp-qq$)|(^mp-360$)/g,vC=["navigateTo","redirectTo","reLaunch","switchTab","navigateBack"];var yC,bC;(bC=yC||(yC={})).navigate="navigateTo",bC.redirect="redirectTo",bC.reLaunch="reLaunch",bC.switchTab="switchTab",bC.navigateBack="navigateBack";const _C={path:"/",name:"",query:{},fullPath:"/",meta:{}},wC=Symbol(),xC=Symbol();function TC(){return To(wC)}function kC(){return To(xC)}const SC={navigateTo:by,redirectTo:_y,reLaunch:wy,switchTab:Ty,navigateBack:vy};function CC(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace("+"," "),r=e.indexOf("="),i=lC(r<0?e:e.slice(0,r)),a=r<0?null:lC(e.slice(r+1));t[i]=a}return t}function EC(e){for(const t in e){const n=e[t];null!=n&&(e[t]=lC(n))}return e}function OC(e){let t="";for(let n in e){const o=e[n];n=sC(n),null!=o?void 0!==o&&(t+=(t.length?"&":"")+n,null!=o&&(t+=`=${sC(o)}`)):void 0!==o&&(t+=(t.length?"&":"")+n)}return t}function MC(e,t,n="/"){if("*"===t)return{path:t,query:{}};let o,r={},i="";const a=t.indexOf("?");return a>-1?(o=t.slice(0,a),i=t.slice(a+1),r=e(i)):o=t,o=function(e,t){if(e.startsWith("/"))return e;if(!t.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/"),r=o[o.length-1];".."!==r&&"."!==r||o.push("");let i,a,s=n.length-1;for(i=0;i<o.length;i++)if(a=o[i],"."!==a){if(".."!==a)break;s>1&&s--}return`${n.slice(0,s).join("/")}/${o.slice(i-(i===o.length?1:0)).join("/")}`}(null!=o?o:t,n),{path:o,query:r}}function IC(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n}function AC(e){const t=new Map,n=new Map,o=new Map;return e.routes.forEach((r=>function(r){let{path:i,aliasPath:a,name:s}=r;const l=JSON.stringify(r);null!=i&&void 0!==i||cC(`当前路由对象route：${l}不规范，必须含有\`path\``,e.debug),0!==i.indexOf("/")&&"*"!==i&&cC(`当前路由对象route：${l} \`path\`缺少前缀 ‘/’`,e.debug),a=a||i,n.set(i,r),t.set(a,r),s&&(o.has(s)&&cC(`当前路由对象route：${l} 的\`name\`已存在路由表中，将会覆盖旧值`,e.debug),o.set(s,r))}(r))),{getRouteByAliasPath:function(e){return t.get(e)},getRouteByPath:function(e){return n.get(e)},getRouteByName:function(e){return o.get(e)}}}function PC(e,t){var n;let{fullPath:o,path:r,name:i,query:a,meta:s}=t;const{getRouteByAliasPath:l,getRouteByPath:c}=e.routeMatcher,u=Object.assign({},_C);if("h5"===e.options.platform){const u="/"===r?l(r):c(r);a=t.query=EC(null===(n=MC(e.parseQuery,o))||void 0===n?void 0:n.query),o=IC(e.stringifyQuery,t),s=Object.assign({},null==u?void 0:u.meta,s),i=null==u?void 0:u.name}return u.fullPath=o,u.meta=s,u.path=r,u.name=i,u.query=a,u}function $C(e){return"string"==typeof e||e&&"object"==typeof e}function LC(e,t){return!(!e.fullPath&&!t.fullPath)&&e.fullPath===t.fullPath}function RC(e,t){const n=e.resolve("*");if(!n||void 0===n.redirect)throw cC("未匹配到*通配符路径，或者*通配符必须配合 redirect 使用。redirect: string | Location",e.options.debug),hC(1,{location:t});let o;o="function"==typeof n.redirect?n.redirect(t):n.redirect;if(void 0===e.resolve(o))throw cC(`无法解析解析出redirect：${JSON.stringify(o)}中的内容，`,e.options.debug),hC(1,{location:t});return hC(2,{to:o,from:t})}function jC(e){return e.reduce(((e,t)=>e.then((()=>t()))),Promise.resolve())}function BC(e,t,n){return()=>new Promise(((o,r)=>{const i=e=>{!1===e?r(hC(4,{to:t,from:n})):e instanceof Error?r(e):$C(e)?r(hC(2,{to:e,from:t})):o()},a=e(t,n,i);let s=Promise.resolve(a);"object"==typeof a&&"then"in a?s=s.then(i):void 0!==a&&i(a),s.catch((e=>r(e)))}))}function DC(e,t){let n=e.$scope.route;n=n.startsWith("/")?n:`/${n}`;return{path:n,query:e.$scope.options||{}}}function NC(e,t,n,o){return(...r)=>{t.call(n,r,(t=>{e.apply(n,t)}),o)}}const FC={onLoad([e],t,n){t([EC(e)])},onShow(e,t,n){console.log(e);const o=$m().length;let r,i;if(n.fromRoute)r=n.currentRoute.value,i=n.fromRoute,n.fromRoute=void 0;else{const o=DC(this);if(r=PC(n,n.resolve(o)),i=n.currentRoute.value,LC(r,i))return t(e);n.currentRoute.value=r}const a=[];for(const s of n.guards.afterGuards.list())a.push((()=>{return e=this,t=null,n=function*(){s(r,i)},new Promise(((o,r)=>{var i=e=>{try{s(n.next(e))}catch(zE){r(zE)}},a=e=>{try{s(n.throw(e))}catch(zE){r(zE)}},s=e=>e.done?o(e.value):Promise.resolve(e.value).then(i,a);s((n=n.apply(e,t)).next())}));var e,t,n}));jC(a),n.level=o,t(e)}};function VC(e,t){return{path:e.$page.path,query:e.$page.options||{}}}const qC={onShow(e){const t=$m().length;let n,o;if(e.fromRoute)n=e.currentRoute.value,o=e.fromRoute,e.fromRoute=void 0;else{const t=VC(this);if(n=PC(e,e.resolve(t)),o=e.currentRoute.value,LC(n,o))return;e.currentRoute.value=n}const r=[];for(const i of e.guards.afterGuards.list())r.push((()=>{return e=this,t=null,r=function*(){i(n,o)},new Promise(((n,o)=>{var i=e=>{try{s(r.next(e))}catch(zE){o(zE)}},a=e=>{try{s(r.throw(e))}catch(zE){o(zE)}},s=e=>e.done?n(e.value):Promise.resolve(e.value).then(i,a);s((r=r.apply(e,t)).next())}));var e,t,r}));jC(r),e.level=t}};function WC(e,t){const n=function(e){let t=e.options.platform;mC.test(t)&&(t="applets");const n={app:{beforeCreate(){"page"===this.$mpType&&function(e,t){if(qC&&e.$)for(const n in qC){const o=qC[n],r=e.$[n];pC(r)?r.unshift(o.bind(e,t)):e.$[n]=[o.bind(e,t)]}}(this,e)}},h5:{},applets:{beforeCreate(){"page"===this.$mpType&&function(e,t){if(console.log(e),FC&&e.$scope)for(const n in FC){const o=FC[n],r=e.$scope[n];r&&(e.$scope[n]=NC(r,o,e,t))}}(this,e)}}};return n[t]||{}}(t);e.mixin(n)}function HC(e,t,n){var o;const r={beforeGuards:()=>{var t;null===(t=e.vueRouter)||void 0===t||t.beforeEach(((t,o,r)=>{const i=e.resolve(t),a=PC(e,t),s=PC(e,o);let l;void 0===i&&(l=RC(e,a));const c=t=>{if(!$C(t)||t instanceof Error)r(t);else if(gC(t)||!t.navType){const n=e.resolve(t);n&&r({path:n.path,query:n.query})}else{const n=t.navType;e.navigate(t,n)}};if(fC(l,2))return void e.redirectTo(null==l?void 0:l.to);const u=n(a,s,c);"object"==typeof u&&"then"in u?u.then(c).catch((()=>{c(!1)})):void 0!==u&&c(u)}))},afterGuards:()=>{var t;null===(t=e.vueRouter)||void 0===t||t.afterEach(((t,o)=>{const r=PC(e,t),i=PC(e,o);n(r,i)}))}};null===(o=r[t])||void 0===o||o.call(r)}var zC=Object.defineProperty,UC=Object.defineProperties,YC=Object.getOwnPropertyDescriptors,XC=Object.getOwnPropertySymbols,GC=Object.prototype.hasOwnProperty,JC=Object.prototype.propertyIsEnumerable,KC=(e,t,n)=>t in e?zC(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,ZC=(e,t)=>{for(var n in t||(t={}))GC.call(t,n)&&KC(e,n,t[n]);if(XC)for(var n of XC(t))JC.call(t,n)&&KC(e,n,t[n]);return e},QC=(e,t)=>UC(e,YC(t));var eE={resolve:function(e,t,n="navigateTo"){const{getRouteByName:o,getRouteByPath:r,getRouteByAliasPath:i}=e.routeMatcher;if(gC(t)&&(t={path:t}),Reflect.has(t,"delta")&&"navigateBack"===n||"backbutton"===t.from)return t;Reflect.has(t,"url")&&(t=QC(ZC({},t),{path:t.url}));const a=e.currentRoute.value.path,s="/"===a?i(a):r(a);if(Reflect.has(t,"path")){const n=MC(e.parseQuery,t.path,null==s?void 0:s.path);let o=r(n.path);if(void 0===o&&(o=i(n.path)),void 0===o)return;const a=Object.assign({},n.query,null==t?void 0:t.query),l=IC(e.stringifyQuery,{path:o.path,query:a});return QC(ZC({},t),{path:o.path,meta:o.meta||{},name:o.name,redirect:o.redirect,fullPath:l,query:a})}if(Reflect.has(t,"name")){let n=o(t.name);if(void 0===n)return void(n=r("*"));const i=Object.assign({},t.query),a=IC(e.stringifyQuery,{path:n.path,query:i});return QC(ZC({},t),{path:n.path,meta:n.meta||{},name:n.name,redirect:n.redirect,fullPath:a,query:i})}},mount:function(e,t){!function(e,t){const{h5:n}=t.options,o=e.config.globalProperties.$router,r=o.options.scrollBehavior;Object.assign(o.options,n),o.options.scrollBehavior=function(e,t,o){return(null==n?void 0:n.scrollBehavior)?null==n?void 0:n.scrollBehavior(e,t,o):r(e,t,o)},t.vueRouter=o;for(const[i,a]of Object.entries(t.guards))a.list().forEach((e=>{HC(t,i,e)}));o.afterEach((e=>{t.currentRoute.value=PC(t,e)}))}(e,t)},navigate:function(e,t,n="navigateTo",o){let r,i=e.resolve(t,n);void 0===i&&(r=RC(e,t));const a=e.currentRoute.value,s=$m();return"navigateBack"===n&&i.delta>=s.length&&(i=e.resolve("/","reLaunch"),n="reLaunch"),(r?Promise.resolve(r):e.jump(i,n)).catch((e=>fC(e,2)?e:Promise.reject(e))).then((t=>{if(t){if(fC(t,2)){const n=e.resolve(null==t?void 0:t.to);return i&&LC(i,n)&&o&&(o._count=o._count?o._count+1:1)>30?(a.fullPath,i.fullPath,e.options.debug,Promise.reject(new Error("Infinite redirect in navigation guard"))):e.navigate(n,n.navType,o||i)}return Promise.resolve(t)}}))},jump:function(e,t,n){return new Promise(((e,o)=>{SC[n](QC(ZC({},t),{url:t.fullPath,success(n){var o;e(n),null===(o=t.success)||void 0===o||o.call(t,n)},fail(e){var n;o(e),null===(n=t.fail)||void 0===n||n.call(t,e)},complete(e){var n;null===(n=t.complete)||void 0===n||n.call(t,e)}}))}))},forceGuardEach:function(e){return t=this,n=null,o=function*(){throw new Error("在h5端上使用：forceGuardEach 是无意义的，目前 forceGuardEach 仅支持在非h5端上使用")},new Promise(((e,r)=>{var i=e=>{try{s(o.next(e))}catch(zE){r(zE)}},a=e=>{try{s(o.throw(e))}catch(zE){r(zE)}},s=t=>t.done?e(t.value):Promise.resolve(t.value).then(i,a);s((o=o.apply(t,n)).next())}));var t,n,o}},tE=Object.defineProperty,nE=Object.defineProperties,oE=Object.getOwnPropertyDescriptors,rE=Object.getOwnPropertySymbols,iE=Object.prototype.hasOwnProperty,aE=Object.prototype.propertyIsEnumerable,sE=(e,t,n)=>t in e?tE(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,lE=(e,t)=>{for(var n in t||(t={}))iE.call(t,n)&&sE(e,n,t[n]);if(rE)for(var n of rE(t))aE.call(t,n)&&sE(e,n,t[n]);return e},cE=(e,t)=>nE(e,oE(t)),uE=(e,t,n)=>new Promise(((o,r)=>{var i=e=>{try{s(n.next(e))}catch(zE){r(zE)}},a=e=>{try{s(n.throw(e))}catch(zE){r(zE)}},s=e=>e.done?o(e.value):Promise.resolve(e.value).then(i,a);s((n=n.apply(e,t)).next())}));var dE={resolve:function(e,t,n="navigateTo"){const{getRouteByName:o,getRouteByPath:r,getRouteByAliasPath:i}=e.routeMatcher;if(gC(t)&&(t={path:t}),Reflect.has(t,"delta")&&"navigateBack"===n||"backbutton"===t.from){t.delta=t.delta||1;const n=$m();let o,r=0;n.length>t.delta&&(r=n.length-1-t.delta),o="app"===e.options.platform?VC(n[r]):DC(n[r].$vm),t=cE(lE(lE({},o),t),{force:"backbutton"===t.from})}Reflect.has(t,"url")&&(t=cE(lE({},t),{path:t.url}));const a=e.currentRoute.value.path,s="/"===a?i(a):r(a);if(Reflect.has(t,"path")){const n=MC(e.parseQuery,t.path,null==s?void 0:s.path);let o=r(n.path);if(void 0===o&&(o=i(n.path)),void 0===o)return;const a=Object.assign({},n.query,null==t?void 0:t.query),l=IC(e.stringifyQuery,{path:o.path,query:a});return cE(lE({},t),{path:o.path,meta:o.meta||{},name:o.name,redirect:o.redirect,fullPath:l,query:a})}if(Reflect.has(t,"name")){const n=o(t.name);if(void 0===n)return;const r=Object.assign({},t.query),i=IC(e.stringifyQuery,{path:n.path,query:r});return cE(lE({},t),{path:n.path,meta:n.meta||{},name:n.name,redirect:n.redirect,fullPath:i,query:r})}},mount:function(e,t){t.forceGuardEach()},navigate:function(e,t,n="navigateTo",o){try{const r=e.resolve(t,n),i=null==r?void 0:r.force;if(e.lock&&!i)return Promise.resolve();e.lock=!0;const a=e.currentRoute.value;let s;if(void 0===r)s=RC(e,t);else if(!i&&LC(r,a)){s=hC(16,{to:PC(e,r),from:a})}return(s?Promise.resolve(s):e.jump(r,n)).catch((e=>fC(e,2)?e:Promise.reject(e))).then((t=>{if(t){if(fC(t,2)){const n=e.resolve(null==t?void 0:t.to);return r&&LC(r,n)&&o&&(o._count=o._count?o._count+1:1)>30?(cC(`检测到从“${a.fullPath}”到“${r.fullPath}”时导航守卫中可能存在无限重定向。中止以避免堆栈溢出。\n            是否总是在导航防护中返回新位置？这将导致此错误。仅在重定向或中止时返回，这应该可以解决此问题。如果未修复，这可能会在生产中中断`,e.options.debug),Promise.reject(new Error("Infinite redirect in navigation guard"))):(e.lock=!1,e.navigate(n,n.navType,o||r))}return Promise.resolve(t)}})).finally((()=>{e.lock=!1}))}catch(r){return e.lock=!1,Promise.reject(r)}},jump:function(e,t,n){return new Promise(((o,r)=>{const i=PC(e,t);Promise.resolve().then((()=>{const t=[];for(const n of e.guards.beforeGuards.list())t.push(BC(n,i,e.currentRoute.value));return jC(t)})).then((()=>{e.fromRoute=e.currentRoute.value,e.currentRoute.value=i,SC[n](cE(lE({},t),{url:t.fullPath,success(e){var n;null===(n=t.success)||void 0===n||n.call(t,e),o(e)},fail(e){var n;null===(n=t.fail)||void 0===n||n.call(t,e),r(e)},complete(e){var n;null===(n=t.complete)||void 0===n||n.call(t,e)}}))})).catch(r)}))},forceGuardEach:function(e){return uE(this,null,(function*(){const t=function(){const e=nh();return{path:`/${e.path}`,query:e.query||{}}}(),n=e.resolve(t);let o;if(void 0===n)o=RC(e,t);else{const t=PC(e,n),i=[];for(const n of e.guards.beforeGuards.list())i.push(BC(n,t,_C));try{yield jC(i),e.currentRoute.value=t;const n=[];for(const o of e.guards.afterGuards.list())n.push((()=>uE(this,null,(function*(){o(t,_C)}))));yield jC(n)}catch(r){o=r}}if(fC(o,2))return e.reLaunch(null==o?void 0:o.to)}))}};const fE=Vo({__name:"App",setup(e){const t=Dk(),{getUser:n}=Mk(),{getTheme:o}=XS(),r=TC(),i=kC(),a=()=>{const e=t.getWebsiteConfig;let n=document.querySelector('link[rel="icon"]');n?n.href=e.h5_favicon:(n=document.createElement("link"),n.rel="icon",n.href=e.h5_favicon,document.head.appendChild(n))};return Ww((async()=>{o(),(async()=>{await t.getConfig(),a();const{status:e,page_status:n,page_url:o}=t.getH5Config;if(!i.meta.webview&&0==e){if(1==n)return location.href=o;r.reLaunch("/pages/empty/empty")}})(),a(),await n()})),()=>{}}});av(fE,{init:rv,setup(e){const t=bm(),n=()=>{var n;n=e,Object.keys(th).forEach((e=>{th[e].forEach((t=>{ir(e,t,n)}))}));const{onLaunch:o,onShow:r,onPageNotFound:i}=e,a=function({path:e,query:t}){return x(Kh,{path:e,query:t}),x(Zh,Kh),x({},Kh)}({path:t.path.slice(1)||__uniRoutes[0].meta.route,query:Ye(t.query)});if(o&&Y(o,a),r&&Y(r,a),!t.matched.length){const e={notFound:!0,openType:"appLaunch",path:t.path,query:{},scene:1001};i&&Y(i,e)}};return To(Il).isReady().then(n),lr((()=>{window.addEventListener("resize",Je(lv,50,{setTimeout:setTimeout,clearTimeout:clearTimeout})),window.addEventListener("message",cv),document.addEventListener("visibilitychange",uv),function(){let e=null;try{e=window.matchMedia("(prefers-color-scheme: dark)")}catch(t){}e&&e.addEventListener("change",(e=>{Hb.emit(ae,{theme:e.matches?"dark":"light"})}))}()})),t.query},before(e){e.mpType="app";const{setup:t}=e,n=()=>(gi(),_i(Mb));e.setup=(e,o)=>{const r=t&&t(e,o);return M(r)?n:r},e.render=n}});const hE=function(){const e=ct(!0),t=e.run((()=>An({})));let n=[],o=[];const r=Sn({install(e){Gw(r),r._a=e,e.provide(Jw,r),e.config.globalProperties.$pinia=r,o.forEach((e=>n.push(e))),o=[]},use(e){return this._a?n.push(e):o.push(e),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}(),pE=Object.freeze(Object.defineProperty({__proto__:null,default:e=>{e.use(hE)}},Symbol.toStringTag,{value:"Module"})),gE={data:()=>({}),onLoad(){this.$u.getRect=this.$uGetRect},methods:{$uGetRect(e,t){return new Promise((n=>{Uf().in(this)[t?"selectAll":"select"](e).boundingClientRect((e=>{t&&Array.isArray(e)&&e.length&&n(e),!t&&e&&n(e)})).exec()}))},getParentData(e=""){this.parent||(this.parent=!1),this.parent=this.$u.$parent.call(this,e),this.parent&&(Object.keys(this.parentData).map((e=>{this.parentData[e]=this.parent[e]})),this.parentData.value=this.parent.modelValue)},preventEvent(e){e&&e.stopPropagation&&e.stopPropagation()}},onReachBottom(){uf("uOnReachBottom")},beforeUnmount(){if(this.parent&&uni.$u.test.array(this.parent.children)){const e=this.parent.children;e.map(((t,n)=>{t===this&&e.splice(n,1)}))}}};function mE(e){if([null,void 0,NaN,!1].includes(e))return e;if("object"!=typeof e&&"function"!=typeof e)return e;var t,n=(t=e,"[object Array]"===Object.prototype.toString.call(t)?[]:{});for(let o in e)e.hasOwnProperty(o)&&(n[o]="object"==typeof e[o]?mE(e[o]):e[o]);return n}function vE(e={},t={}){if("object"!=typeof(e=mE(e))||"object"!=typeof t)return!1;for(var n in t)t.hasOwnProperty(n)&&(n in e?"object"!=typeof e[n]||"object"!=typeof t[n]?e[n]=t[n]:e[n].concat&&t[n].concat?e[n]=e[n].concat(t[n]):e[n]=vE(e[n],t[n]):e[n]=t[n]);return e}function yE(e){switch(typeof e){case"undefined":return!0;case"string":if(0==e.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g,"").length)return!0;break;case"boolean":if(!e)return!0;break;case"number":if(0===e||isNaN(e))return!0;break;case"object":if(null===e||0===e.length)return!0;for(var t in e)return!1;return!0}return!1}const bE={email:function(e){return/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(e)},mobile:function(e){return/^1[23456789]\d{9}$/.test(e)},url:function(e){return/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w-.\/?%&=]*)?/.test(e)},date:function(e){return!/Invalid|NaN/.test(new Date(e).toString())},dateISO:function(e){return/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(e)},number:function(e){return/^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(e)},digits:function(e){return/^\d+$/.test(e)},idCard:function(e){return/^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(e)},carNo:function(e){const t=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/,n=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;return 7===e.length?n.test(e):8===e.length&&t.test(e)},amount:function(e){return/^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/.test(e)},chinese:function(e){return/^[\u4e00-\u9fa5]+$/gi.test(e)},letter:function(e){return/^[a-zA-Z]*$/.test(e)},enOrNum:function(e){return/^[0-9a-zA-Z]*$/g.test(e)},contains:function(e,t){return e.indexOf(t)>=0},range:function(e,t){return e>=t[0]&&e<=t[1]},rangeLength:function(e,t){return e.length>=t[0]&&e.length<=t[1]},empty:yE,isEmpty:yE,jsonString:function(e){if("string"==typeof e)try{var t=JSON.parse(e);return!("object"!=typeof t||!t)}catch(zE){return!1}return!1},landline:function(e){return/^\d{3,4}-\d{7,8}(-\d{3,4})?$/.test(e)},object:function(e){return"[object Object]"===Object.prototype.toString.call(e)},array:function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===Object.prototype.toString.call(e)},code:function(e,t=6){return new RegExp(`^\\d{${t}}$`).test(e)}};const _E=new class{setConfig(e){this.config=vE(this.config,e)}request(e={}){if(this.interceptor.request&&"function"==typeof this.interceptor.request){let t=this.interceptor.request(e);if(!1===t)return new Promise((()=>{}));this.options=t}return e.dataType=e.dataType||this.config.dataType,e.responseType=e.responseType||this.config.responseType,e.url=e.url||"",e.params=e.params||{},e.header=Object.assign({},this.config.header,e.header),e.method=e.method||this.config.method,new Promise(((t,n)=>{e.complete=e=>{if(Yy(),clearTimeout(this.config.timer),this.config.timer=null,this.config.originalData)if(this.interceptor.response&&"function"==typeof this.interceptor.response){let o=this.interceptor.response(e);!1!==o?t(o):n(e)}else t(e);else if(200==e.statusCode)if(this.interceptor.response&&"function"==typeof this.interceptor.response){let o=this.interceptor.response(e.data);!1!==o?t(o):n(e.data)}else t(e.data);else n(e)},e.url=bE.url(e.url)?e.url:this.config.baseUrl+(0==e.url.indexOf("/")?e.url:"/"+e.url),this.config.showLoading&&!this.config.timer&&(this.config.timer=setTimeout((()=>{Uy({title:this.config.loadingText,mask:this.config.loadingMask}),this.config.timer=null}),this.config.loadingTime)),fy(e)}))}constructor(){this.config={baseUrl:"",header:{},method:"POST",dataType:"json",responseType:"text",showLoading:!0,loadingText:"请求中...",loadingTime:800,timer:null,originalData:!1,loadingMask:!0},this.interceptor={request:null,response:null},this.get=(e,t={},n={})=>this.request({method:"GET",url:e,header:n,data:t}),this.post=(e,t={},n={})=>this.request({url:e,method:"POST",header:n,data:t}),this.put=(e,t={},n={})=>this.request({url:e,method:"PUT",header:n,data:t}),this.delete=(e,t={},n={})=>this.request({url:e,method:"DELETE",header:n,data:t})}};const wE=(new class{constructor(){this.config={type:"navigateTo",url:"",delta:1,params:{},animationType:"pop-in",animationDuration:300,intercept:!1},this.route=this.route.bind(this)}addRootPath(e){return"/"===e[0]?e:`/${e}`}mixinParam(e,t){e=e&&this.addRootPath(e);let n="";return/.*\/.*\?.*=.*/.test(e)?(n=uni.$u.queryParams(t,!1),e+"&"+n):(n=uni.$u.queryParams(t),e+n)}async route(e={},t={}){let n={};if("string"==typeof e?(n.url=this.mixinParam(e,t),n.type="navigateTo"):(n=uni.$u.deepClone(e,this.config),n.url=this.mixinParam(e.url,e.params)),t.intercept&&(this.config.intercept=t.intercept),n.params=t,n=uni.$u.deepMerge(this.config,n),"function"==typeof uni.$u.routeIntercept){await new Promise(((e,t)=>{uni.$u.routeIntercept(n,e)}))&&this.openPage(n)}else this.openPage(n)}openPage(e){const{url:t,type:n,delta:o,animationType:r,animationDuration:i}=e;"navigateTo"!=e.type&&"to"!=e.type||by({url:t,animationType:r,animationDuration:i}),"redirectTo"!=e.type&&"redirect"!=e.type||_y({url:t}),"switchTab"!=e.type&&"tab"!=e.type||Ty({url:t}),"reLaunch"!=e.type&&"launch"!=e.type||wy({url:t}),"navigateBack"!=e.type&&"back"!=e.type||vy({delta:o})}}).route;function xE(e=null,t="yyyy-mm-dd"){e||(e=Number(new Date)),10==e.toString().length&&(e*=1e3);let n,o=new Date(e),r={"y+":o.getFullYear().toString(),"m+":(o.getMonth()+1).toString(),"d+":o.getDate().toString(),"h+":o.getHours().toString(),"M+":o.getMinutes().toString(),"s+":o.getSeconds().toString()};for(let i in r)n=new RegExp("("+i+")").exec(t),n&&(t=t.replace(n[1],1==n[1].length?r[i]:r[i].padStart(n[1].length,"0")));return t}function TE(e,t=!0){if((e=e.toLowerCase())&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(e)){if(4===e.length){let t="#";for(let n=1;n<4;n+=1)t+=e.slice(n,n+1).concat(e.slice(n,n+1));e=t}let n=[];for(let t=1;t<7;t+=2)n.push(parseInt("0x"+e.slice(t,t+2)));return t?`rgb(${n[0]},${n[1]},${n[2]})`:n}if(/^(rgb|RGB)/.test(e)){return e.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",").map((e=>Number(e)))}return e}function kE(e){let t=e;if(/^(rgb|RGB)/.test(t)){let e=t.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(","),n="#";for(let t=0;t<e.length;t++){let o=Number(e[t]).toString(16);o=1==String(o).length?"0"+o:o,"0"===o&&(o+=o),n+=o}return 7!==n.length&&(n=t),n}if(!/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(t))return t;{let e=t.replace(/#/,"").split("");if(6===e.length)return t;if(3===e.length){let t="#";for(let n=0;n<e.length;n+=1)t+=e[n]+e[n];return t}}}String.prototype.padStart||(String.prototype.padStart=function(e,t=" "){if("[object String]"!==Object.prototype.toString.call(t))throw new TypeError("fillString must be String");let n=this;if(n.length>=e)return String(n);let o=e-n.length,r=Math.ceil(o/t.length);for(;r>>=1;)t+=t,1===r&&(t+=t);return t.slice(0,o)+n});const SE={colorGradient:function(e="rgb(0, 0, 0)",t="rgb(255, 255, 255)",n=10){let o=TE(e,!1),r=o[0],i=o[1],a=o[2],s=TE(t,!1),l=(s[0]-r)/n,c=(s[1]-i)/n,u=(s[2]-a)/n,d=[];for(let f=0;f<n;f++){let e=kE("rgb("+Math.round(l*f+r)+","+Math.round(c*f+i)+","+Math.round(u*f+a)+")");d.push(e)}return d},hexToRgb:TE,rgbToHex:kE,colorToRgba:function(e,t=.3){let n=(e=kE(e)).toLowerCase();if(n&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(n)){if(4===n.length){var o="#";for(let e=1;e<4;e+=1)o+=n.slice(e,e+1).concat(n.slice(e,e+1));n=o}var r=[];for(let e=1;e<7;e+=2)r.push(parseInt("0x"+n.slice(e,e+2)));return"rgba("+r.join(",")+","+t+")"}return n}};let CE=null;let EE=[],OE=[];let ME="1.10.1";const IE={v:ME,version:ME,type:["primary","success","info","error","warning"]};const AE={queryParams:function(e={},t=!0,n="brackets"){let o=t?"?":"",r=[];-1==["indices","brackets","repeat","comma"].indexOf(n)&&(n="brackets");for(let i in e){let t=e[i];if(!(["",void 0,null].indexOf(t)>=0))if(t.constructor===Array)switch(n){case"indices":for(let n=0;n<t.length;n++)r.push(i+"["+n+"]="+t[n]);break;case"brackets":default:t.forEach((e=>{r.push(i+"[]="+e)}));break;case"repeat":t.forEach((e=>{r.push(i+"="+e)}));break;case"comma":let e="";t.forEach((t=>{e+=(e?",":"")+t})),r.push(i+"="+e)}else r.push(i+"="+t)}return r.length?o+r.join("&"):""},route:wE,timeFormat:xE,date:xE,timeFrom:function(e=null,t="yyyy-mm-dd"){e||(e=Number(new Date)),10==e.toString().length&&(e*=1e3);let n=+new Date(Number(e)),o=(Number(new Date)-n)/1e3,r="";switch(!0){case o<300:r="刚刚";break;case o>=300&&o<3600:r=parseInt(o/60)+"分钟前";break;case o>=3600&&o<86400:r=parseInt(o/3600)+"小时前";break;case o>=86400&&o<2592e3:r=parseInt(o/86400)+"天前";break;default:r=!1===t?o>=2592e3&&o<31536e3?parseInt(o/2592e3)+"个月前":parseInt(o/31536e3)+"年前":xE(n,t)}return r},colorGradient:SE.colorGradient,colorToRgba:SE.colorToRgba,guid:function(e=32,t=!0,n=null){let o="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),r=[];if(n=n||o.length,e)for(let i=0;i<e;i++)r[i]=o[0|Math.random()*n];else{let e;r[8]=r[13]=r[18]=r[23]="-",r[14]="4";for(let t=0;t<36;t++)r[t]||(e=0|16*Math.random(),r[t]=o[19==t?3&e|8:e])}return t?(r.shift(),"u"+r.join("")):r.join("")},color:{primary:"#2979ff",primaryDark:"#2b85e4",primaryDisabled:"#a0cfff",primaryLight:"#ecf5ff",bgColor:"#f3f4f6",info:"#909399",infoDark:"#82848a",infoDisabled:"#c8c9cc",infoLight:"#f4f4f5",warning:"#ff9900",warningDark:"#f29100",warningDisabled:"#fcbd71",warningLight:"#fdf6ec",error:"#fa3534",errorDark:"#dd6161",errorDisabled:"#fab6b6",errorLight:"#fef0f0",success:"#19be6b",successDark:"#18b566",successDisabled:"#71d5a1",successLight:"#dbf1e1",mainColor:"#303133",contentColor:"#606266",tipsColor:"#909399",lightColor:"#c0c4cc",borderColor:"#e4e7ed"},sys:function(){return Ov()},os:function(){return Ov().platform},type2icon:function(e="success",t=!1){-1==["primary","info","error","warning","success"].indexOf(e)&&(e="success");let n="";switch(e){case"primary":case"info":n="info-circle";break;case"error":n="close-circle";break;case"warning":n="error-circle";break;default:n="checkmark-circle"}return t&&(n+="-fill"),n},randomArray:function(e=[]){return e.sort((()=>Math.random()-.5))},wranning:function(e){},get:_E.get,post:_E.post,put:_E.put,delete:_E.delete,hexToRgb:SE.hexToRgb,rgbToHex:SE.rgbToHex,test:bE,random:function(e,t){if(e>=0&&t>0&&t>=e){let n=t-e+1;return Math.floor(Math.random()*n+e)}return 0},deepClone:mE,deepMerge:vE,getParent:function(e,t){let n=this.$parent;for(;n;){if(n.$options.name===e){let e={};if(Array.isArray(t))t.map((t=>{e[t]=n[t]?n[t]:""}));else for(let o in t)Array.isArray(t[o])?t[o].length?e[o]=t[o]:e[o]=n[o]:t[o].constructor===Object?Object.keys(t[o]).length?e[o]=t[o]:e[o]=n[o]:e[o]=t[o]||!1===t[o]?t[o]:n[o];return e}n=n.$parent}return{}},$parent:function(e=void 0){let t=this.$parent;for(;t;){if(!t.$options||t.$options.name===e)return t;t=t.$parent}return!1},addUnit:function(e="auto",t="rpx"){return e=String(e),bE.number(e)?`${e}${t}`:e},trim:function(e,t="both"){return"both"==t?e.replace(/^\s+|\s+$/g,""):"left"==t?e.replace(/^\s*/,""):"right"==t?e.replace(/(\s*$)/g,""):"all"==t?e.replace(/\s+/g,""):e},type:["primary","success","error","warning","info"],http:_E,toast:function(e,t=1500){Hy({title:e,icon:"none",duration:t})},config:IE,zIndex:{toast:10090,noNetwork:10080,popup:10075,mask:10070,navbar:980,topTips:975,sticky:970,indexListSticky:965},debounce:function(e,t=500,n=!1){if(null!==CE&&clearTimeout(CE),n){var o=!CE;CE=setTimeout((function(){CE=null}),t),o&&"function"==typeof e&&e()}else CE=setTimeout((function(){"function"==typeof e&&e()}),t)},throttle:function(e,t=500,n=!0,o="default"){EE[o]||(EE[o]=null),n?OE[o]||(OE[o]=!0,"function"==typeof e&&e(),EE[o]=setTimeout((()=>{OE[o]=!1}),t)):OE[o]||(OE[o]=!0,EE[o]=setTimeout((()=>{OE[o]=!1,"function"==typeof e&&e()}),t))}};uni.$u=AE;const PE={install:e=>{e.mixin(gE),e.config.globalProperties.$u=AE}},$E=Object.freeze(Object.defineProperty({__proto__:null,default:e=>{e.use(PE)}},Symbol.toStringTag,{value:"Module"})),LE=Object.freeze(Object.defineProperty({__proto__:null,default:async()=>{const e=new URL(location.href);if("47b1e3a9d33e6064e58cc4796c708447"==new URLSearchParams(e.search).get("vconsole")){return new(0,(await o((()=>import("./vconsole.min.61e7227c.js").then((e=>e.v))),["assets/vconsole.min.61e7227c.js","assets/_commonjsHelpers.02d3be64.js"])).default)}}},Symbol.toStringTag,{value:"Module"})),RE=Object.assign({"./modules/pinia.ts":pE,"./modules/uview.ts":$E,"./modules/vconsole.ts":LE}),jE={install:e=>{for(const t of Object.values(RE)){const n=t.default;M(n)&&n(e)}}};var BE=(e=>(e[e.MP_WEIXIN=1]="MP_WEIXIN",e[e.OA_WEIXIN=2]="OA_WEIXIN",e[e.H5=3]="H5",e[e.IOS=5]="IOS",e[e.ANDROID=6]="ANDROID",e))(BE||{}),DE=(e=>(e.LOGIN="YZMDL",e.BIND_MOBILE="BDSJHM",e.CHANGE_MOBILE="BGSJHM",e.FIND_PASSWORD="ZHDLMM",e))(DE||{}),NE=(e=>(e.NONE="",e.AVATAR="avatar",e.USERNAME="account",e.NICKNAME="nickname",e.SEX="sex",e))(NE||{}),FE=(e=>(e.SUCCESS="success",e.FAIL="fail",e.PENDING="pending",e))(FE||{}),VE=(e=>(e.LOADING="loading",e.NORMAL="normal",e.ERROR="error",e.EMPTY="empty",e))(VE||{});const qE=()=>/MicroMessenger/i.test(navigator.userAgent);const WE=({MP_WEIXIN:e,OA_WEIXIN:t,H5:n,IOS:o,ANDROID:r,OTHER:i})=>qE()?t():n(),HE=WE({MP_WEIXIN:()=>BE.MP_WEIXIN,OA_WEIXIN:()=>BE.OA_WEIXIN,H5:()=>BE.H5,IOS:()=>BE.IOS,ANDROID:()=>BE.ANDROID,OTHER:()=>null});var zE,UE={};function YE(e){return Ak.post({url:"/login/account",data:{...e,terminal:HE}})}function XE(e){return Ak.post({url:"/login/register",data:{...e,channel:HE}})}function GE(e){return Ak.post({url:"/login/oaLogin",data:e})}function JE(e){return Ak.post({url:"/login/oaAuthBind",data:e})}zE=window,{get exports(){return UE},set exports(e){UE=e}}.exports=function(e,t){if(!e.jWeixin){var n,o={config:"preVerifyJSAPI",onMenuShareTimeline:"menu:share:timeline",onMenuShareAppMessage:"menu:share:appmessage",onMenuShareQQ:"menu:share:qq",onMenuShareWeibo:"menu:share:weiboApp",onMenuShareQZone:"menu:share:QZone",previewImage:"imagePreview",getLocation:"geoLocation",openProductSpecificView:"openProductViewWithPid",addCard:"batchAddCard",openCard:"batchViewCard",chooseWXPay:"getBrandWCPayRequest",openEnterpriseRedPacket:"getRecevieBizHongBaoRequest",startSearchBeacons:"startMonitoringBeacons",stopSearchBeacons:"stopMonitoringBeacons",onSearchBeacons:"onBeaconsInRange",consumeAndShareCard:"consumedShareCard",openAddress:"editAddress"},r=function(){var e={};for(var t in o)e[o[t]]=t;return e}(),i=e.document,a=i.title,s=navigator.userAgent.toLowerCase(),l=navigator.platform.toLowerCase(),c=!(!l.match("mac")&&!l.match("win")),u=-1!=s.indexOf("wxdebugger"),d=-1!=s.indexOf("micromessenger"),f=-1!=s.indexOf("android"),h=-1!=s.indexOf("iphone")||-1!=s.indexOf("ipad"),p=(n=s.match(/micromessenger\/(\d+\.\d+\.\d+)/)||s.match(/micromessenger\/(\d+\.\d+)/))?n[1]:"",g={initStartTime:P(),initEndTime:0,preVerifyStartTime:0,preVerifyEndTime:0},m={version:1,appId:"",initTime:0,preVerifyTime:0,networkType:"",isPreVerifyOk:1,systemType:h?1:f?2:-1,clientVersion:p,url:encodeURIComponent(location.href)},v={},y={_completes:[]},b={state:0,data:{}};$((function(){g.initEndTime=P()}));var _=!1,w=[],x={config:function(t){A("config",v=t);var n=!1!==v.check;$((function(){if(n)S(o.config,{verifyJsApiList:I(v.jsApiList),verifyOpenTagList:I(v.openTagList)},function(){y._complete=function(e){g.preVerifyEndTime=P(),b.state=1,b.data=e},y.success=function(e){m.isPreVerifyOk=0},y.fail=function(e){y._fail?y._fail(e):b.state=-1};var e=y._completes;return e.push((function(){!function(){if(!(c||u||v.debug||p<"6.0.2"||m.systemType<0)){var e=new Image;m.appId=v.appId,m.initTime=g.initEndTime-g.initStartTime,m.preVerifyTime=g.preVerifyEndTime-g.preVerifyStartTime,x.getNetworkType({isInnerInvoke:!0,success:function(t){m.networkType=t.networkType;var n="https://open.weixin.qq.com/sdk/report?v="+m.version+"&o="+m.isPreVerifyOk+"&s="+m.systemType+"&c="+m.clientVersion+"&a="+m.appId+"&n="+m.networkType+"&i="+m.initTime+"&p="+m.preVerifyTime+"&u="+m.url;e.src=n}})}}()})),y.complete=function(t){for(var n=0,o=e.length;n<o;++n)e[n]();y._completes=[]},y}()),g.preVerifyStartTime=P();else{b.state=1;for(var e=y._completes,t=0,r=e.length;t<r;++t)e[t]();y._completes=[]}})),x.invoke||(x.invoke=function(t,n,o){e.WeixinJSBridge&&WeixinJSBridge.invoke(t,E(n),o)},x.on=function(t,n){e.WeixinJSBridge&&WeixinJSBridge.on(t,n)})},ready:function(e){0!=b.state?e():(y._completes.push(e),!d&&v.debug&&e())},error:function(e){p<"6.0.2"||(-1==b.state?e(b.data):y._fail=e)},checkJsApi:function(e){S("checkJsApi",{jsApiList:I(e.jsApiList)},(e._complete=function(e){if(f){var t=e.checkResult;t&&(e.checkResult=JSON.parse(t))}e=function(e){var t=e.checkResult;for(var n in t){var o=r[n];o&&(t[o]=t[n],delete t[n])}return e}(e)},e))},onMenuShareTimeline:function(e){C(o.onMenuShareTimeline,{complete:function(){S("shareTimeline",{title:e.title||a,desc:e.title||a,img_url:e.imgUrl||"",link:e.link||location.href,type:e.type||"link",data_url:e.dataUrl||""},e)}},e)},onMenuShareAppMessage:function(e){C(o.onMenuShareAppMessage,{complete:function(t){"favorite"===t.scene?S("sendAppMessage",{title:e.title||a,desc:e.desc||"",link:e.link||location.href,img_url:e.imgUrl||"",type:e.type||"link",data_url:e.dataUrl||""}):S("sendAppMessage",{title:e.title||a,desc:e.desc||"",link:e.link||location.href,img_url:e.imgUrl||"",type:e.type||"link",data_url:e.dataUrl||""},e)}},e)},onMenuShareQQ:function(e){C(o.onMenuShareQQ,{complete:function(){S("shareQQ",{title:e.title||a,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},onMenuShareWeibo:function(e){C(o.onMenuShareWeibo,{complete:function(){S("shareWeiboApp",{title:e.title||a,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},onMenuShareQZone:function(e){C(o.onMenuShareQZone,{complete:function(){S("shareQZone",{title:e.title||a,desc:e.desc||"",img_url:e.imgUrl||"",link:e.link||location.href},e)}},e)},updateTimelineShareData:function(e){S("updateTimelineShareData",{title:e.title,link:e.link,imgUrl:e.imgUrl},e)},updateAppMessageShareData:function(e){S("updateAppMessageShareData",{title:e.title,desc:e.desc,link:e.link,imgUrl:e.imgUrl},e)},startRecord:function(e){S("startRecord",{},e)},stopRecord:function(e){S("stopRecord",{},e)},onVoiceRecordEnd:function(e){C("onVoiceRecordEnd",e)},playVoice:function(e){S("playVoice",{localId:e.localId},e)},pauseVoice:function(e){S("pauseVoice",{localId:e.localId},e)},stopVoice:function(e){S("stopVoice",{localId:e.localId},e)},onVoicePlayEnd:function(e){C("onVoicePlayEnd",e)},uploadVoice:function(e){S("uploadVoice",{localId:e.localId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},downloadVoice:function(e){S("downloadVoice",{serverId:e.serverId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},translateVoice:function(e){S("translateVoice",{localId:e.localId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},chooseImage:function(e){S("chooseImage",{scene:"1|2",count:e.count||9,sizeType:e.sizeType||["original","compressed"],sourceType:e.sourceType||["album","camera"]},(e._complete=function(e){if(f){var t=e.localIds;try{t&&(e.localIds=JSON.parse(t))}catch(n){}}},e))},getLocation:function(e){},previewImage:function(e){S(o.previewImage,{current:e.current,urls:e.urls},e)},uploadImage:function(e){S("uploadImage",{localId:e.localId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},downloadImage:function(e){S("downloadImage",{serverId:e.serverId,isShowProgressTips:0==e.isShowProgressTips?0:1},e)},getLocalImgData:function(e){!1===_?(_=!0,S("getLocalImgData",{localId:e.localId},(e._complete=function(e){if(_=!1,0<w.length){var t=w.shift();wx.getLocalImgData(t)}},e))):w.push(e)},getNetworkType:function(e){S("getNetworkType",{},(e._complete=function(e){e=function(e){var t=e.errMsg;e.errMsg="getNetworkType:ok";var n=e.subtype;if(delete e.subtype,n)e.networkType=n;else{var o=t.indexOf(":"),r=t.substring(o+1);switch(r){case"wifi":case"edge":case"wwan":e.networkType=r;break;default:e.errMsg="getNetworkType:fail"}}return e}(e)},e))},openLocation:function(e){S("openLocation",{latitude:e.latitude,longitude:e.longitude,name:e.name||"",address:e.address||"",scale:e.scale||28,infoUrl:e.infoUrl||""},e)},getLocation:function(e){S(o.getLocation,{type:(e=e||{}).type||"wgs84"},(e._complete=function(e){delete e.type},e))},hideOptionMenu:function(e){S("hideOptionMenu",{},e)},showOptionMenu:function(e){S("showOptionMenu",{},e)},closeWindow:function(e){S("closeWindow",{},e=e||{})},hideMenuItems:function(e){S("hideMenuItems",{menuList:e.menuList},e)},showMenuItems:function(e){S("showMenuItems",{menuList:e.menuList},e)},hideAllNonBaseMenuItem:function(e){S("hideAllNonBaseMenuItem",{},e)},showAllNonBaseMenuItem:function(e){S("showAllNonBaseMenuItem",{},e)},scanQRCode:function(e){S("scanQRCode",{needResult:(e=e||{}).needResult||0,scanType:e.scanType||["qrCode","barCode"]},(e._complete=function(e){if(h){var t=e.resultStr;if(t){var n=JSON.parse(t);e.resultStr=n&&n.scan_code&&n.scan_code.scan_result}}},e))},openAddress:function(e){S(o.openAddress,{},(e._complete=function(e){var t;(t=e).postalCode=t.addressPostalCode,delete t.addressPostalCode,t.provinceName=t.proviceFirstStageName,delete t.proviceFirstStageName,t.cityName=t.addressCitySecondStageName,delete t.addressCitySecondStageName,t.countryName=t.addressCountiesThirdStageName,delete t.addressCountiesThirdStageName,t.detailInfo=t.addressDetailInfo,delete t.addressDetailInfo,e=t},e))},openProductSpecificView:function(e){S(o.openProductSpecificView,{pid:e.productId,view_type:e.viewType||0,ext_info:e.extInfo},e)},addCard:function(e){for(var t=e.cardList,n=[],r=0,i=t.length;r<i;++r){var a=t[r],s={card_id:a.cardId,card_ext:a.cardExt};n.push(s)}S(o.addCard,{card_list:n},(e._complete=function(e){var t=e.card_list;if(t){for(var n=0,o=(t=JSON.parse(t)).length;n<o;++n){var r=t[n];r.cardId=r.card_id,r.cardExt=r.card_ext,r.isSuccess=!!r.is_succ,delete r.card_id,delete r.card_ext,delete r.is_succ}e.cardList=t,delete e.card_list}},e))},chooseCard:function(e){S("chooseCard",{app_id:v.appId,location_id:e.shopId||"",sign_type:e.signType||"SHA1",card_id:e.cardId||"",card_type:e.cardType||"",card_sign:e.cardSign,time_stamp:e.timestamp+"",nonce_str:e.nonceStr},(e._complete=function(e){e.cardList=e.choose_card_info,delete e.choose_card_info},e))},openCard:function(e){for(var t=e.cardList,n=[],r=0,i=t.length;r<i;++r){var a=t[r],s={card_id:a.cardId,code:a.code};n.push(s)}S(o.openCard,{card_list:n},e)},consumeAndShareCard:function(e){S(o.consumeAndShareCard,{consumedCardId:e.cardId,consumedCode:e.code},e)},chooseWXPay:function(e){S(o.chooseWXPay,O(e),e)},openEnterpriseRedPacket:function(e){S(o.openEnterpriseRedPacket,O(e),e)},startSearchBeacons:function(e){S(o.startSearchBeacons,{ticket:e.ticket},e)},stopSearchBeacons:function(e){S(o.stopSearchBeacons,{},e)},onSearchBeacons:function(e){C(o.onSearchBeacons,e)},openEnterpriseChat:function(e){S("openEnterpriseChat",{useridlist:e.userIds,chatname:e.groupName},e)},launchMiniProgram:function(e){S("launchMiniProgram",{targetAppId:e.targetAppId,path:function(e){if("string"==typeof e&&0<e.length){var t=e.split("?")[0],n=e.split("?")[1];return t+=".html",void 0!==n?t+"?"+n:t}}(e.path),envVersion:e.envVersion},e)},openBusinessView:function(e){S("openBusinessView",{businessType:e.businessType,queryString:e.queryString||"",envVersion:e.envVersion},(e._complete=function(e){if(f){var t=e.extraData;if(t)try{e.extraData=JSON.parse(t)}catch(n){e.extraData={}}}},e))},miniProgram:{navigateBack:function(e){e=e||{},$((function(){S("invokeMiniProgramAPI",{name:"navigateBack",arg:{delta:e.delta||1}},e)}))},navigateTo:function(e){$((function(){S("invokeMiniProgramAPI",{name:"navigateTo",arg:{url:e.url}},e)}))},redirectTo:function(e){$((function(){S("invokeMiniProgramAPI",{name:"redirectTo",arg:{url:e.url}},e)}))},switchTab:function(e){$((function(){S("invokeMiniProgramAPI",{name:"switchTab",arg:{url:e.url}},e)}))},reLaunch:function(e){$((function(){S("invokeMiniProgramAPI",{name:"reLaunch",arg:{url:e.url}},e)}))},postMessage:function(e){$((function(){S("invokeMiniProgramAPI",{name:"postMessage",arg:e.data||{}},e)}))},getEnv:function(t){$((function(){t({miniprogram:"miniprogram"===e.__wxjs_environment})}))}}},T=1,k={};return i.addEventListener("error",(function(e){if(!f){var t=e.target,n=t.tagName,o=t.src;if(("IMG"==n||"VIDEO"==n||"AUDIO"==n||"SOURCE"==n)&&-1!=o.indexOf("wxlocalresource://")){e.preventDefault(),e.stopPropagation();var r=t["wx-id"];if(r||(r=T++,t["wx-id"]=r),k[r])return;k[r]=!0,wx.ready((function(){wx.getLocalImgData({localId:o,success:function(e){t.src=e.localData}})}))}}}),!0),i.addEventListener("load",(function(e){if(!f){var t=e.target,n=t.tagName;if(t.src,"IMG"==n||"VIDEO"==n||"AUDIO"==n||"SOURCE"==n){var o=t["wx-id"];o&&(k[o]=!1)}}}),!0),t&&(e.wx=e.jWeixin=x),x}function S(t,n,o){e.WeixinJSBridge?WeixinJSBridge.invoke(t,E(n),(function(e){M(t,e,o)})):A(t,o)}function C(t,n,o){e.WeixinJSBridge?WeixinJSBridge.on(t,(function(e){o&&o.trigger&&o.trigger(e),M(t,e,n)})):A(t,o||n)}function E(e){return(e=e||{}).appId=v.appId,e.verifyAppId=v.appId,e.verifySignType="sha1",e.verifyTimestamp=v.timestamp+"",e.verifyNonceStr=v.nonceStr,e.verifySignature=v.signature,e}function O(e){return{timeStamp:e.timestamp+"",nonceStr:e.nonceStr,package:e.package,paySign:e.paySign,signType:e.signType||"SHA1"}}function M(e,t,n){"openEnterpriseChat"!=e&&"openBusinessView"!==e||(t.errCode=t.err_code),delete t.err_code,delete t.err_desc,delete t.err_detail;var o=t.errMsg;o||(o=t.err_msg,delete t.err_msg,o=function(e,t){var n=e,o=r[n];o&&(n=o);var i="ok";if(t){var a=t.indexOf(":");"confirm"==(i=t.substring(a+1))&&(i="ok"),"failed"==i&&(i="fail"),-1!=i.indexOf("failed_")&&(i=i.substring(7)),-1!=i.indexOf("fail_")&&(i=i.substring(5)),"access denied"!=(i=(i=i.replace(/_/g," ")).toLowerCase())&&"no permission to execute"!=i||(i="permission denied"),"config"==n&&"function not exist"==i&&(i="ok"),""==i&&(i="fail")}return n+":"+i}(e,o),t.errMsg=o),(n=n||{})._complete&&(n._complete(t),delete n._complete),o=t.errMsg||"",v.debug&&!n.isInnerInvoke&&alert(JSON.stringify(t));var i=o.indexOf(":");switch(o.substring(i+1)){case"ok":n.success&&n.success(t);break;case"cancel":n.cancel&&n.cancel(t);break;default:n.fail&&n.fail(t)}n.complete&&n.complete(t)}function I(e){if(e){for(var t=0,n=e.length;t<n;++t){var r=e[t],i=o[r];i&&(e[t]=i)}return e}}function A(e,t){if(!(!v.debug||t&&t.isInnerInvoke)){var n=r[e];n&&(e=n),t&&t._complete&&delete t._complete,console.log('"'+e+'",',t||"")}}function P(){return(new Date).getTime()}function $(t){d&&(e.WeixinJSBridge?t():i.addEventListener&&i.addEventListener("WeixinJSBridgeReady",t,!1))}}(zE);const KE=(e,t=!1,n)=>new Promise(((o,r)=>{let i=Uf();n&&(i=Uf().in(n)),i[t?"selectAll":"select"](e).boundingClientRect((function(e){return t&&Array.isArray(e)&&e.length||!t&&e?o(e):void r("找不到元素")})).exec()}));function ZE(e,t="navigateTo"){if("mini_program"===e.type)return void function(e){const t=e.query;window.open(`weixin://dl/business/?appid=${null==t?void 0:t.appId}&path=${null==t?void 0:t.path}&env_version=${null==t?void 0:t.env_version}&query=${encodeURIComponent(null==t?void 0:t.query)}`)}(e);const n=(null==e?void 0:e.query)?`${e.path}?${tO(null==e?void 0:e.query)}`:e.path;("switchTab"==t||e.canTab)&&Ty({url:n}),"navigateTo"==t&&by({url:n}),"reLaunch"==t&&wy({url:n})}const QE=(e,t)=>{const n=[];for(let o=0;o<Math.ceil(e.length/t);o++){const r=o*t,i=r+t;n.push(e.slice(r,i))}return n},eO=e=>null==e&&void 0===e;function tO(e){let t="";for(const n of Object.keys(e)){const o=e[n],r=encodeURIComponent(n)+"=";if(!eO(o))if(console.log(encodeURIComponent(n),P(o)),P(o)){for(const e of Object.keys(o))if(!eO(o[e])){t+=encodeURIComponent(n+"["+e+"]")+"="+encodeURIComponent(o[e])+"&"}}else t+=r+encodeURIComponent(o)+"&"}return t.slice(0,-1)}const nO=(e,t="rpx")=>Object.is(Number(e),NaN)?e:`${e}${t}`;function oO({price:e,take:t="all",prec:n}){let[o,r=""]=(e+"").split(".");if(void 0!==n){for(let e=n-r.length;e>0;--e)r+="0";r=r.substr(0,n)}switch(t){case"int":return o;case"dec":return r;case"all":return o+"."+r}}function rO(...e){return function(){return new Promise(((t,n)=>{const o=e.values(),r=e=>{const i=o.next();i.done?t(e):Promise.resolve(i.value(e)).then(r).catch(n)};r()}))}}const iO=(e,t="yyyy-mm-dd",n=!1)=>{if(!e&&n)return"--";e||(e=Number(new Date)),10==e.toString().length&&(e*=1e3);const o=new Date(e);let r;const i={"y+":o.getFullYear().toString(),"m+":(o.getMonth()+1).toString(),"d+":o.getDate().toString(),"h+":o.getHours().toString(),"M+":o.getMinutes().toString(),"s+":o.getSeconds().toString()};for(const a in i)r=new RegExp("("+a+")").exec(t),r&&(t=t.replace(r[1],1==r[1].length?i[a]:i[a].padStart(r[1].length,"0")));return t},aO=e=>{if(e<60)return`${e}秒`;{const t=Math.floor(e/3600),n=e%3600,o=Math.floor(n/60),r=n%60;let i="";return t>0&&(i+=`${t}小时`),o>0&&(i+=`${o}分钟`),r>0&&(i+=`${r}秒`),i||"--"}},sO=e=>{const t=["#808080","#2979ff","#ff6b00","#FF0000","#00FFFF","#FFA500","#FF00FF","#FFFF00","#800000","#008000","#000080","#808000","#800080","#008080","#A52A2A","#FFC0CB","#D2B48C","#8B4513","#9370DB","#2E8B57"];if(void 0!==e){return t[e%t.length]}return t};var lO=(e=>(e.LOGIN="login",e.PC_LOGIN="pcLogin",e.BIND_WX="bindWx",e.BASE="base",e))(lO||{});const cO={_authData:{code:"",scene:""},setAuthData(e={}){this._authData=e},getAuthData(){return this._authData},getSignLink:()=>(void 0!==window.signLink&&""!==window.signLink||(window.signLink=location.href.split("#")[0]),function(){const e=navigator.userAgent;return e.indexOf("Android")>-1||e.indexOf("Adr")>-1}()?location.href.split("#")[0]:window.signLink),getUrl(e,t="snsapi_userinfo",n={}){const o=`${location.href}${location.search?"&":"?"}scene=${e||""}&${tO(n)}`;return new Promise(((e,n)=>{var r;(r={url:o,scope:t},Ak.get({url:"/login/codeUrl",data:r})).then((t=>{location.href=t.url,e()}),n)}))},config(){return new Promise(((e,t)=>{var n;(n={url:this.getSignLink()},Ak.get({url:"/wechat/jsConfig",data:n})).then((n=>{UE.config({...n,success:()=>{e("success")},fail:e=>{t("wx config is fail")}})}))}))},miniProgram:UE.miniProgram,ready:()=>new Promise(((e,t)=>{UE.ready((()=>{e()})),UE.error((()=>{t()}))})),pay(e){return new Promise(((t,n)=>{this.ready().then((()=>{UE.chooseWXPay({timestamp:e.timeStamp,nonceStr:e.nonceStr,package:e.package,signType:e.signType,paySign:e.paySign,success:e=>{"chooseWXPay:ok"===e.errMsg?t(e):n(e.errMsg)},cancel:e=>{n(e)},fail:e=>{n(e)}})})).catch((e=>{n(e)}))}))},async share(e){return new Promise(((t,n)=>{this.ready().then((()=>{const{title:o,link:r,imgUrl:i,desc:a}=e,s=["updateTimelineShareData","updateAppMessageShareData"];for(const e of s)UE[e]({title:o,link:r,imgUrl:i,desc:a,success(){t()},fail(){n()}})})).catch(n)}))},getAddress(){return new Promise(((e,t)=>{this.ready().then((()=>{UE.openAddress({success:t=>{e(t)},fail:e=>{t(e)}})}))}))},getLocation(){return new Promise(((e,t)=>{this.ready().then((()=>{UE.getLocation({type:"gcj02",success:t=>{e(t)},fail:e=>{t(e)}})}))}))},hideMenuItems(e){return new Promise(((t,n)=>{this.ready().then((()=>{UE.hideMenuItems({menuList:e,success:e=>{t(e)},fail:e=>{n(e)}})}))}))},showMenuItems(e){return new Promise(((t,n)=>{this.ready().then((()=>{UE.showMenuItems({menuList:e,success:e=>{t(e)},fail:e=>{n(e)}})}))}))}},uO=function(e){const t={beforeGuards:GS(),afterGuards:GS()},n=Pn(_C),o=AC(e),r="h5"===e.platform?eE:dE;function i(e,t="navigateTo",n){return new Promise(((o,i)=>{let s={};var l;null!=(l=e)&&"object"==typeof l&&(s=e),r.navigate(a,e,t,n).then((e=>{o(e)})).catch((e=>{var t,n;null===(t=s.fail)||void 0===t||t.call(s,e),null===(n=s.complete)||void 0===n||n.call(s,e),i(e)}))}))}const a={level:0,lock:!1,currentRoute:n,guards:t,options:e,vueRouter:null,routeMatcher:o,parseQuery:e.parseQuery||CC,stringifyQuery:e.stringifyQuery||OC,jump:function(e,t){return new Promise(((n,o)=>{r.jump(a,e,t).then(n).catch(o)}))},navigateTo:e=>i(e,"navigateTo"),switchTab:e=>i(e,"switchTab"),redirectTo:e=>i(e,"redirectTo"),reLaunch:e=>i(e,"reLaunch"),navigateBack:(e={delta:1})=>i(e,"navigateBack"),navigate:i,resolve:function(e,t="navigateTo"){return r.resolve(a,e,t)},forceGuardEach:function(){return new Promise(((e,t)=>{r.forceGuardEach(a).then(e).catch(t)}))},beforeEach(e){t.beforeGuards.add(e)},afterEach(e){t.afterGuards.add(e)},install(e){const t={};for(const r in _C)t[r]=Gi((()=>n.value[r]));e.config.globalProperties.$uniRouter=a,Object.defineProperty(e.config.globalProperties,"$uniRoute",{enumerable:!0,get:()=>Rn(n)}),e.provide(wC,a),e.provide(xC,mn(t));const o=e.mount;e.mount=function(...t){return function(e){vC.forEach((t=>{uni[t]=function(n){return e[t](n)}}))}(a),r.mount(e,a),WC(e,a),console.log("%c uni-router %c v1.2.7 ","padding: 2px 1px; border-radius: 3px 0 0 3px; color: #fff; background: #606060; font-weight: bold;","padding: 2px 1px; border-radius: 0 3px 3px 0; color: #fff; background: #42c02e; font-weight: bold;"),o(...t)}}};return a}({routes:[{path:"/pages/index/index",aliasPath:"/"},{path:"/pages/news/news"},{path:"/pages/user/user"},{path:"/pages/login/login",meta:{white:!0}},{path:"/pages/register/register",meta:{white:!0}},{path:"/pages/forget_pwd/forget_pwd",meta:{white:!0}},{path:"/pages/customer_service/customer_service",meta:{white:!0}},{path:"/pages/news_detail/news_detail"},{path:"/pages/user_set/user_set",meta:{auth:!0}},{path:"/pages/collection/collection",meta:{auth:!0}},{path:"/pages/as_us/as_us"},{path:"/pages/agreement/agreement"},{path:"/pages/change_password/change_password",meta:{auth:!0}},{path:"/pages/user_data/user_data",meta:{auth:!0}},{path:"/pages/search/search"},{path:"/pages/webview/webview"},{path:"/pages/bind_mobile/bind_mobile"},{path:"/pages/empty/empty"},{path:"/pages/payment_result/payment_result",meta:{auth:!0}},{path:"/uni_modules/vk-uview-ui/components/u-avatar-cropper/u-avatar-cropper"},{path:"/packages/pages/ticket/device/index",meta:{auth:!0}},{path:"/packages/pages/ticket/device/detail",meta:{}},{path:"/packages/pages/message/index",meta:{auth:!0}},{path:"/packages/pages/ticket/order/create",meta:{auth:!0}},{path:"/packages/pages/ticket/order/list",meta:{auth:!0}},{path:"/packages/pages/ticket/order/staff_list",meta:{auth:!0}},{path:"/packages/pages/ticket/order/staff_detail",meta:{auth:!0}},{path:"/packages/pages/ticket/order/detail",meta:{auth:!0}},{path:"/packages/pages/ticket/hall",meta:{auth:!0}},{path:"/packages/pages/ticket/hall_detail",meta:{auth:!0}},{path:"/packages/pages/ticket/index",meta:{auth:!0}},{path:"/packages/pages/ticket/user",meta:{auth:!0}},{path:"/packages/pages/ticket/aingdesk-chat",meta:{auth:!0}},{path:"/packages/pages/ticket/order/template",meta:{auth:!0}},{path:"/packages/pages/web-view/index",meta:{auth:!0}},{path:"/packages/pages/404/404",name:"404",meta:{white:!0}},{path:"/packages/pages/user_wallet/user_wallet",meta:{auth:!0}},{path:"/packages/pages/recharge/recharge",meta:{auth:!0}},{path:"/packages/pages/recharge_record/recharge_record",meta:{auth:!0}},{path:"/packages/pages/user/data_analysis",meta:{auth:!0}},{path:"*",redirect:()=>({name:"404"})}],debug:!1,platform:"h5",h5:{}});let dO=!0;uO.beforeEach((async(e,t)=>{if(dO){Mk().isLogin||e.meta.white||_k.set(bk,e.fullPath),dO=!1}})),uO.afterEach(((e,t)=>{Mk().isLogin||e.meta.white||_k.set(bk,e.fullPath)})),uO.beforeEach((async(e,t)=>{if(!Mk().isLogin&&e.meta.auth)return"/pages/login/login"})),uO.beforeEach((async(e,t)=>{const{code:n,state:o,scene:r}=e.query;if(n&&o&&r)return cO.setAuthData({code:n,scene:r}),delete e.query.code,delete e.query.state,{path:e.path,force:!0,navType:"reLaunch",query:e.query}})),uO.afterEach(((e,t)=>{setTimeout((async()=>{HE!=BE.OA_WEIXIN||e.meta.webview||await cO.config()}))}));const fO={computed:{$theme(){const e=XS(),t=Dk();return{primaryColor:e.primaryColor,pageStyle:e.vars,navColor:e.navColor,navBgColor:e.navBgColor,title:t.getWebsiteConfig.shop_name}}}};(function(){const e=Ya(fE);return function(e){e.mixin(fO)}(e),e.use(jE),e.use(uO),{app:e}})().app.use(Gm).mount("#app");export{KE as $,Co as A,QE as B,of as C,cb as D,Cr as E,ci as F,XS as G,mn as H,Uw as I,Hw as J,Mk as K,cx as L,kn as M,$y as N,$v as O,jv as P,Ta as Q,Nk as R,Xg as S,lb as T,by as U,To as V,lr as W,Ni as X,Df as Y,xo as Z,eo as _,xr as a,Uv as a$,Uf as a0,Hg as a1,Pn as a2,Av as a3,Ci as a4,Zg as a5,qw as a6,Fk as a7,qE as a8,cO as a9,Lg as aA,Sg as aB,NE as aC,zw as aD,xk as aE,kk as aF,Tk as aG,Gp as aH,Wu as aI,yk as aJ,vr as aK,Wa as aL,Vk as aM,Nv as aN,Ak as aO,pv as aP,Rv as aQ,Bv as aR,nm as aS,VE as aT,Ov as aU,$m as aV,If as aW,Fv as aX,Pf as aY,Af as aZ,sb as a_,lO as aa,Uy as ab,Yy as ac,kC as ad,$k as ae,DE as af,_k as ag,bk as ah,wy as ai,GE as aj,YE as ak,XE as al,vy as am,Ck as an,JE as ao,Iv as ap,hg as aq,eg as ar,Lk as as,Sk as at,nO as au,lf as av,fr as aw,cf as ax,Rk as ay,Ju as az,_i as b,cy as b0,uf as b1,up as b2,Ab as b3,_r as b4,Hy as b5,Uu as b6,iO as b7,aO as b8,sO as b9,fy as bA,my as bB,Dv as bC,nh as bD,Bk as bE,hp as bF,fp as bG,Gg as bH,Ek as bI,oO as bJ,WE as bK,FE as bL,In as bM,rO as bN,BE as bO,HE as bP,Ok as bQ,ub as ba,ob as bb,jg as bc,Bg as bd,qg as be,Ty as bf,_y as bg,sr as bh,Yw as bi,Nb as bj,jk as bk,Zf as bl,eh as bm,yC as bn,mb as bo,Li as bp,lx as bq,Kf as br,gv as bs,Pv as bt,ov as bu,ss as bv,Qf as bw,dy as bx,af as by,Hv as bz,Gi as c,Vo as d,Ei as e,Ii as f,bi as g,Sr as h,kg as i,u as j,qa as k,Mi as l,Ip as m,i as n,gi as o,om as p,Ug as q,Fw as r,ko as s,h as t,Dk as u,TC as v,mo as w,Rn as x,ZE as y,An as z};
