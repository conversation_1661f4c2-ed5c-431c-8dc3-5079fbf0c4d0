import{K as t,o as e,b as s,w as a,aU as i,am as l,b7 as o,b8 as c,b9 as n,D as u,a0 as r,ba as d,N as h,bb as f,U as m,p,f as _,e as k,g as y,h as g,F as T,l as b,t as C,n as v,Q as w,a1 as S,r as D,a as $,a5 as x,b4 as V,az as P,m as I,aS as R,bc as M,j as L,k as A,bd as B,b6 as U}from"./index-1f99df69.js";import{_ as j}from"./u-icon.6d560ed4.js";import{T as N,c as z,r as F,_ as W}from"./TicketStatusSelector.c5403fbf.js";import{_ as H}from"./u-popup.64944c14.js";import{_ as X}from"./uni-easyinput.61f3681a.js";import{b as Y,k as E,l as Q,f as O,n as q,m as J,o as K,u as Z,p as G,q as tt,t as et,h as st,i as at,v as it,r as lt,w as ot}from"./ticket.f414548f.js";import{c as ct}from"./customFn.bcd5b981.js";import{u as nt}from"./useDictOptions.a4153ed6.js";import{_ as ut}from"./index.vue_vue_type_script_setup_true_lang.40aaf37a.js";import{A as rt}from"./single.8ed39644.js";import{M as dt}from"./MessageDetailPopupWrapper.deac022e.js";import{_ as ht}from"./empty.dfd7c9cf.js";import{_ as ft}from"./_plugin-vue_export-helper.1b428a4d.js";import"./uni-data-checkbox.1ac05949.js";import"./uni-cloud.es.050f64ca.js";import"./uni-icons.8ecc47ca.js";import"./uni-search-bar.f1ab1fca.js";import"./MessageDetailPopup.62ca7e36.js";import"./uni-popup.b7176a8c.js";import"./message.8e95ac30.js";const mt=t(),pt={components:{DictValue:ut,AdminSelectorSingle:rt,MessageDetailPopupWrapper:dt,TicketStatusSelector:N},data(){const t=i();return{cancelReasons:z,ratingTags:F,dictData:{},status_id:0,activeTab:"all",statusTabs:[{value:"all",label:"全部",id:0,count:0}],scrollLeft:0,touchStartX:0,touchStartTime:0,ticketList:[],counts:{all:0,pending:0,processing:0,resolved:0,closed:0},page:1,pageSize:5,loading:!1,finished:!1,currentTicketId:"",cancelPopupVisible:!1,currentReason:0,showCustomReasonInput:!1,customReason:"",completePopupVisible:!1,completeText:"",setMoneyPopupVisible:!1,setMoney:"",updateMoney:"",oldMoeny:"",updateAmountPopupVisible:!1,changeDispatchPopupVisible:!1,showSelectedAdminSingle:!1,defaultSelectedAdmin:{id:0,name:""},isApp:"android"===t.platform||"ios"===t.platform,isH5:"h5"===t.platform,isWechat:"mp-weixin"===t.platform,createBtnRight:30,createBtnBottom:230,backToTopBtnRight:30,backToTopBtnBottom:130,isDragging:!1,startX:0,startY:0,startRight:0,startBottom:0,windowWidth:t.windowWidth,windowHeight:t.windowHeight,showBackToTop:!1,loadTicketsTimer:null,initStatusList:[],popStatusTitle:"选择状态节点",popStatusType:"status",showStatusSelector:!1,selectorStatusList:[],selectedStatus:{id:0,status_name:"请选择",sort:0},showMorePopup:!1}},computed:{},onLoad(t){setTimeout((()=>{mt.userInfo.tenant_admin_id||(this.$u.toast("您没有权限访问当前页面"),l({delta:1}))}),1e3),t.status&&(this.status_id=t.status),this.initDictData(),this.loadTickets(),this.loadStatusList()},onPageScroll(t){this.showBackToTop=t.scrollTop>300},onPullDownRefresh(){this.refreshList()},onReachBottom(){this.finished||this.loading||(this.page++,this.loadTickets())},methods:{timeFormat:o,formatServiceTime:c,getColorByIndex:n,backToTop(){u({scrollTop:0,duration:300})},handleScroll(t){this.scrollLeft=t.detail.scrollLeft},handleTouchStart(t){this.isDragging=!0;const e=t.touches&&t.touches[0]?t.touches[0]:null;e&&(this.startX=e.clientX,this.startY=e.clientY);const s=t.currentTarget.classList[0];"floating-create-btn"===s?(this.startRight=this.createBtnRight,this.startBottom=this.createBtnBottom):"floating-back-to-top-btn"===s&&(this.startRight=this.backToTopBtnRight,this.startBottom=this.backToTopBtnBottom)},handleTouchMove(t){if(!this.isDragging)return;const e=t.touches&&t.touches[0]?t.touches[0]:null;if(!e)return void console.warn("事件对象的 touches 属性不存在或长度为 0:",t);const s=e.clientX,a=e.clientY,i=this.startX-s,l=this.startY-a,o=t.currentTarget.classList[0],c=(t,e,s,a)=>{t=Math.max(20,Math.min(t,this.windowWidth-80)),e=Math.max(20,Math.min(e,this.windowHeight-80)),this[s]=t,this[a]=e};if("floating-create-btn"===o){c(this.startRight+i/2,this.startBottom+l/2,"createBtnRight","createBtnBottom")}else if("floating-back-to-top-btn"===o){c(this.startRight+i/2,this.startBottom+l/2,"backToTopBtnRight","backToTopBtnBottom")}},handleTouchEnd(){this.isDragging=!1},switchTab(t,e){this.activeTab!==t&&(this.activeTab=t,this.status_id=e,this.refreshList(),this.$nextTick((()=>{this.autoScrollToActiveTab()})))},autoScrollToActiveTab(){const t=r().in(this);t.selectAll(".client-tab-item").boundingClientRect(),t.select(".client-tab-container").boundingClientRect(),t.exec((t=>{const e=t[0],s=t[1],a=e[this.statusTabs.findIndex((t=>t.value===this.activeTab))];if(a){let t=this.scrollLeft;a.right>s.right?t+=a.right-s.right:a.left<s.left&&(t-=s.left-a.left),this.scrollLeft=t}}))},refreshList(){this.page=1,this.finished=!1,this.ticketList=[],this.loadTickets()},async initDictData(){try{const{dictData:t}=nt("urgency_degree,maintenance_status,source_type,is_read,allocation_status,payment_status,user_confirmed_amount,audit_status,close_type,close_status,service_period");this.dictData=t}catch(t){this.$u.toast(t||"加载字典数据失败")}},async loadStatusList(){try{const t=await Y();console.log("statusList:",t),this.initStatusList=t}catch(t){this.$u.toast(t||"加载状态列表失败")}},async loadTickets(){this.loadTicketsTimer&&clearTimeout(this.loadTicketsTimer),this.loadTicketsTimer=setTimeout((async()=>{var t,e;if(!this.loading&&!this.finished){this.loading=!0;try{console.log("this.status_id:",this.status_id);const s=this.status_id?this.status_id:(null==(t=this.statusTabs.find((t=>t.value===this.activeTab)))?void 0:t.id)||"";console.log("status_id:",s);const a=await E({page:this.page,pageSize:this.pageSize,status_id:s,isOwn:!1});if(console.log("Ticket list response:",a),1===this.page?(this.statusTabs=a.statusCounts,this.ticketList=a.lists||[]):this.ticketList=[...this.ticketList,...a.lists||[]],this.finished=!a.lists||a.lists.length<this.pageSize,this.status_id){const t=(null==(e=this.statusTabs.find((t=>t.id==this.status_id)))?void 0:e.value)||"all";t!==this.activeTab&&(this.activeTab=t,this.$nextTick((()=>{this.autoScrollToActiveTab()})))}}catch(s){this.$u.toast(s)}finally{this.loading=!1,d()}}}),0)},callStaff(t){ct(t)},showChangeDispatchDialog(t){this.currentTicketId=t;const e=this.ticketList.find((e=>e.id===t));if(e){e.actions.status.is_initial?(this.showSelectedAdminSingle=!1,this.changeDispatchPopupVisible=!0):this.changeToDispatch(1)}else this.$u.toast("参数有误，请联系客服")},changeToDispatch(t){this.changeDispatchPopupVisible=!1,0===t?h({title:"提示",content:"确定要释放该工单到抢单大厅吗？",confirmText:"确定",cancelText:"取消",cancelColor:this.$theme.minorColor,confirmColor:this.$theme.primaryColor,success:t=>{t.confirm&&this.confirmChangeToDispatch(0,0)}}):(this.defaultSelectedAdmin={id:0,name:""},this.showSelectedAdminSingle=!0,console.log("点击改派指定人后，showSelectedAdminSingle 的值:",this.showSelectedAdminSingle))},handleConfirmAdminSingle(t){if(this.showSelectedAdminSingle=!1,console.log("子组件选中的管理员:",t),t.id){let e=0;const s=t.dept_id;s.length>0&&(e=s[0]),this.confirmChangeToDispatch(t.id,e)}},async confirmChangeToDispatch(t,e){if(t!=mt.userInfo.tenant_admin_id)try{await Q({id:this.currentTicketId,staff_id:t,dept_id:e}),this.ticketList.map((t=>{t.id===this.currentTicketId&&(t.is_participate=0)})),this.$u.toast("操作成功")}catch(s){this.$u.toast(s)}else this.$u.toast("不能将工单改派给自己")},showChangeUrgencyDialog(t){this.currentTicketId=t;let e=this.dictData.urgency_degree,s=e.map((t=>t.name));console.log(s),f({itemList:s,success:t=>{if(-1!==t.tapIndex){let s=e[t.tapIndex].value;console.log("用户点击的选项的id:",s),this.changeUrgency(s)}}})},async changeUrgency(t){try{await O({id:this.currentTicketId,urgency:t,mode:2}),this.$u.toast("操作成功"),this.ticketList.map((e=>{e.id===this.currentTicketId&&(e.urgency=t)}))}catch(e){this.$u.toast(e||"变更失败，请重试")}},showNotifyPayDialog(t,e){this.currentTicketId=t,h({title:"提示",content:`提醒客户支付费用￥${e}`,confirmText:"确定",cancelText:"取消",cancelColor:this.$theme.minorColor,confirmColor:this.$theme.primaryColor,success:t=>{t.confirm&&this.confirmNotifyPayTicket()}})},async confirmNotifyPayTicket(){try{await q({id:this.currentTicketId}),this.$u.toast("提醒成功")}catch(t){this.$u.toast(t)}},showConfirmPayDialog(t,e){this.currentTicketId=t,h({title:"提示",content:`确认该工单客户已支付费用￥${e}`,confirmText:"确定",cancelText:"取消",cancelColor:this.$theme.minorColor,confirmColor:this.$theme.primaryColor,success:t=>{t.confirm&&this.confirmConfirmPayTicket()}})},async confirmConfirmPayTicket(){try{await J({id:this.currentTicketId}),this.ticketList.map((t=>{t.id===this.currentTicketId&&(t.payment_status=2,t.client_confirmed_amount=1,t.actions.status.is_final&&(t.close_status=1,t.complete_time||(t.complete_time=Math.floor((new Date).getTime()/1e3))))})),this.$u.toast("操作成功")}catch(t){this.$u.toast(t)}},showNotifyConfirmAmountDialog(t,e){this.currentTicketId=t,h({title:"提示",content:`提醒客户确认费用￥${e}`,confirmText:"确定",cancelText:"取消",cancelColor:this.$theme.minorColor,confirmColor:this.$theme.primaryColor,success:t=>{t.confirm&&this.confirmNotifyConfirmAmountTicket()}})},async confirmNotifyConfirmAmountTicket(){try{await K({id:this.currentTicketId}),this.$u.toast("提醒成功")}catch(t){this.$u.toast(t)}},showUpdateAmountDialog(t,e){this.currentTicketId=t,this.updateMoney="",this.oldMoeny=e,this.updateAmountPopupVisible=!0},async confirmUpdateAmountTicket(){if(this.updateMoney)if(isNaN(this.updateMoney)||this.updateMoney<0)this.$u.toast("请输入正确的金额");else if(isNaN(this.updateMoney)||this.updateMoney<0)this.$u.toast("请输入正确的金额");else if(this.updateMoney!=this.oldMoeny){if(0==this.updateMoney)return this.updateAmountPopupVisible=!1,void this.showSetFreeDialog(this.currentTicketId,this.oldMoeny);try{await Z({id:this.currentTicketId,pay_amount:this.updateMoney}),this.ticketList.map((t=>{t.id===this.currentTicketId&&(t.payment_amount=this.updateMoney,t.payment_status=1,t.client_confirmed_amount=0)})),this.$u.toast("设置付费金额成功"),this.updateAmountPopupVisible=!1,this.updateMoney="",this.oldMoeny=""}catch(t){this.$u.toast(t)}}else this.$u.toast("修改金额与原金额相同");else this.$u.toast("请输入付费金额")},showSetFreeDialog(t,e){this.currentTicketId=t,h({title:"提示",content:`原需支付￥${e}，确定设置为免费吗？`,confirmText:"确定",cancelText:"取消",cancelColor:this.$theme.minorColor,confirmColor:this.$theme.primaryColor,success:t=>{t.confirm&&this.confirmSetFreeTicket()}})},async confirmSetFreeTicket(){try{await G({id:this.currentTicketId}),this.ticketList.map((t=>{t.id===this.currentTicketId&&(t.payment_status=0,t.client_confirmed_amount=0,t.payment_amount=0,t.actions.status.is_final&&(t.close_status=1,t.complete_time=Math.floor((new Date).getTime()/1e3)))})),this.$u.toast("设置免费成功")}catch(t){this.$u.toast(t)}},showSetPayDialog(t){this.currentTicketId=t,this.setMoney="",this.setMoneyPopupVisible=!0},async confirmSetPayTicket(){if(this.setMoney)if(console.log("confirmSetPayTicket called with setMoney:",this.setMoney),isNaN(this.setMoney)||this.setMoney<=0)this.$u.toast("请输入正确的金额");else try{await tt({id:this.currentTicketId,pay_amount:this.setMoney}),this.ticketList.map((t=>{t.id===this.currentTicketId&&(t.payment_amount=this.setMoney,t.payment_status=1,t.client_confirmed_amount=0)})),this.$u.toast("设置付费金额成功"),this.setMoneyPopupVisible=!1,this.setMoney=""}catch(t){this.$u.toast(t)}else this.$u.toast("请输入付费金额")},showCompleteDialog(t){this.currentTicketId=t,this.completeText="",this.completePopupVisible=!0},async confirmComplete(){try{await et({id:this.currentTicketId,processing_method:this.completeText}),this.ticketList.map((t=>{t.id===this.currentTicketId&&(t.ticket_status_id=t.actions.next_status_id,2==t.payment_status&&1==t.client_confirmed_amount&&t.payment_amount>0&&(t.close_status=1),0==t.payment_status&&(t.close_status=1),t.complete_time=Math.floor((new Date).getTime()/1e3),t.actions.status.is_final=1,t.actions.next_status={},t.actions.next_status_id=0)})),this.$u.toast("工单已完成"),this.completePopupVisible=!1}catch(t){this.$u.toast(t)}},showRestoreDialog(t){this.currentTicketId=t,h({title:"提示",content:"确定要撤回取消该工单吗？",confirmText:"确定",cancelText:"取消",cancelColor:this.$theme.minorColor,confirmColor:this.$theme.primaryColor,success:t=>{t.confirm&&this.confirmRestoreTicket()}})},async confirmRestoreTicket(){try{await st({id:this.currentTicketId,type:1}),this.$u.toast("操作成功"),this.ticketList.map((t=>{t.id===this.currentTicketId&&(t.cancel_status=0,t.close_status=0)}))}catch(t){this.$u.toast(t||"操作失败，请重试")}},showCancelDialog(t){console.log("showCancelDialog called with id:",t),this.currentTicketId=t,this.cancelPopupVisible=!0,this.currentReason=0},cancelReasonRadioChange(t){let e;this.isApp||this.isWechat?e=t.detail.value:this.isH5&&(e=t.target.value),console.log("cancelReasonRadioChange called with selectedValue:",e);for(let s=0;s<this.cancelReasons.length;s++)if(this.cancelReasons[s]===e){this.currentReason=s;break}this.showCustomReasonInput="其他原因"===e,this.showCustomReasonInput||(this.customReason=""),console.log("currentReason after change:",this.currentReason)},handleCustomReasonInput(){},async confirmCancelTicket(){let t;if("其他原因"===this.cancelReasons[this.currentReason]){if(!this.customReason.trim())return void this.$u.toast("请输入具体取消原因");t=this.customReason}else t=this.cancelReasons[this.currentReason];if(t)try{await at({id:this.currentTicketId,reason:t,close_status:2}),this.$u.toast("工单已取消"),this.cancelPopupVisible=!1,this.ticketList.map((t=>{t.id===this.currentTicketId&&(t.cancel_status=2,t.close_status=2)}))}catch(e){this.$u.toast(e)}else this.$u.toast("取消原因错误")},showChangeStatusDialog(t){this.currentTicketId=t,this.showChangeStatusSelector()},showChangeStatusSelector(){var t;const e=this.ticketList.find((t=>t.id===this.currentTicketId)).ticket_status_id,s=(null==(t=this.initStatusList.find((t=>t.id===e)))?void 0:t.sort)||0;console.log("currentStatusSort:",s),this.selectorStatusList=this.initStatusList.filter((t=>t.sort>=s)),console.log("selectorStatusList:",this.selectorStatusList),this.popStatusTitle="请选择变更到的状态",this.popStatusType="change",this.showStatusSelector=!0},async confirmChangeStatus(){if(this.selectedStatus.id)try{await it({id:this.currentTicketId,to_status_id:this.selectedStatus.id,type:1}),this.$u.toast("变更成功"),this.ticketList.map((t=>{t.id===this.currentTicketId&&(t.ticket_status_id=this.selectedStatus.id,t.status_name=this.selectedStatus.status_name,t.status_color=this.selectedStatus.status_color,1==this.selectedStatus.is_final&&(t.complete_time=Math.floor((new Date).getTime()/1e3),t.payment_amount>0&&2==t.payment_status&&1==t.client_confirmed_amount&&(t.close_status=1),0==t.payment_status&&(t.close_status=1)))}))}catch(t){this.$u.toast(t)}else this.$u.toast("请选择变更到的状态")},showRestartDialog(t){this.currentTicketId=t,h({title:"提示",content:"您确定重启该工单？",confirmText:"确定",cancelText:"取消",cancelColor:this.$theme.minorColor,confirmColor:this.$theme.primaryColor,success:t=>{t.confirm&&this.showRestartStatusSelector()}})},showRestartStatusSelector(){this.ticketList.find((t=>t.id===this.currentTicketId)).ticket_status_id,this.selectorStatusList=this.initStatusList,console.log("selectorStatusList:",this.selectorStatusList),this.popStatusTitle="请选择重启到的状态",this.popStatusType="restart",this.showStatusSelector=!0},async confirmRestartTicket(){if(this.selectedStatus.id)try{await lt({id:this.currentTicketId,to_status_id:this.selectedStatus.id,type:1}),this.$u.toast("重启成功"),this.ticketList.map((t=>{t.id===this.currentTicketId&&(t.processing_total_duration=0,t.complete_time="",t.close_status=0,t.ticket_status_id=this.selectedStatus.id,t.status_name=this.selectedStatus.status_name,t.status_color=this.selectedStatus.status_color,t.restart_num=t.restart_num+1)}))}catch(t){this.$u.toast(t)}else this.$u.toast("请选择重启到的状态")},handleConfirmStatusSelector(t){console.log("确认选择的状态：",t),this.selectedStatus=t,"restart"===this.popStatusType?(this.showStatusSelector=!1,this.confirmRestartTicket()):"change"===this.popStatusType&&(this.showStatusSelector=!1,this.confirmChangeStatus())},showCanQuitDialog(t){this.currentTicketId=t,h({title:"提示",content:"您确定退出参与该工单？",confirmText:"确定",cancelText:"取消",cancelColor:this.$theme.minorColor,confirmColor:this.$theme.primaryColor,success:t=>{t.confirm&&this.confirmQuitTicket()}})},async confirmQuitTicket(){try{await ot({id:this.currentTicketId}).then((t=>{console.log("退出参与工单成功:",t)}));let t=null,e=null;this.ticketList.map((s=>{s.id===this.currentTicketId&&(e=s,t=this.ticketList.findIndex((t=>t.id===this.currentTicketId)))})),this.ticketList.splice(t,1),this.statusTabs.forEach((t=>{t.id!==e.ticket_status_id&&0!==t.id||t.count--})),this.$u.toast("操作成功")}catch(t){this.$u.toast(t)}},goToDetail(t){m({url:`/packages/pages/ticket/order/staff_detail?id=${t}`})},goToCreate(){m({url:"/packages/pages/ticket/order/create"})}}},_t=()=>{w((t=>({"02921e07":t.$theme.primaryColor,"05a7ac81":t.$theme.navBgColor,"01ad0286":t.$theme.navColor,"5893f498":t.$theme.minorColor})))},kt=pt.setup;pt.setup=kt?(t,e)=>(_t(),kt(t,e)):_t;const yt=ft(pt,[["render",function(t,i,l,o,c,n){const u=p,r=S,d=D($("u-icon"),j),h=x,f=V("dict-value"),m=D($("u-rate"),W),w=P,N=I,z=V("AdminSelectorSingle"),F=V("TicketStatusSelector"),Y=R,E=D($("u-popup"),H),Q=B,O=U,q=M,J=D($("uni-easyinput"),X),K=V("MessageDetailPopupWrapper");return e(),s(u,{class:"client-ticket-list"},{default:a((()=>[_(" Header with statusTabs "),k(u,{class:"client-header"},{default:a((()=>[_(" 可滚动的 tab 容器 "),k(u,{class:"scrollable-tab-container"},{default:a((()=>[k(r,{class:"client-tab-container","scroll-x":"","show-scrollbar":"false","scroll-left":c.scrollLeft,onScroll:n.handleScroll,role:"tablist","aria-label":"工单状态标签列表"},{default:a((()=>[(e(!0),y(T,null,g(c.statusTabs,((t,i)=>(e(),s(u,{key:t.value,class:L(["client-tab-item",{"client-active":c.activeTab===t.value}]),onClick:e=>n.switchTab(t.value,t.id),role:"tab","aria-selected":c.activeTab===t.value,tabindex:"0"},{default:a((()=>[b(C(t.label)+"("+C(t.count||0)+") ",1)])),_:2},1032,["class","onClick","aria-selected"])))),128))])),_:1},8,["scroll-left","onScroll"])])),_:1}),_(" 新增更多按钮 "),k(u,{class:"more-button",onClick:i[0]||(i[0]=t=>c.showMorePopup=!0)},{default:a((()=>[k(d,{name:"list-dot",color:"#ffffff",size:"36"})])),_:1})])),_:1}),_(" Ticket list "),k(u,{class:"client-list-container"},{default:a((()=>[(e(!0),y(T,null,g(c.ticketList,((i,l)=>(e(),s(u,{class:"client-ticket-item",key:l,onClick:t=>n.goToDetail(i.id)},{default:a((()=>[_(" 工单编号 创建时间 "),k(u,{class:"client-ticket-code-time"},{default:a((()=>[k(u,{class:"code"},{default:a((()=>[k(h,null,{default:a((()=>[b(C(i.ticket_code),1)])),_:2},1024)])),_:2},1024),k(u,{class:"time"},{default:a((()=>[k(h,null,{default:a((()=>[b(C(i.create_time),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),_(" Ticket title and status "),k(u,{class:"client-ticket-header"},{default:a((()=>[k(u,{class:"client-ticket-title"},{default:a((()=>[_(" <text class=\"client-status-dot\" :style=\"'background-color: '+ item.status_color +';'\"></text> "),i.is_participate?(e(),s(h,{key:0,class:"client-is-participate",style:v("color: "+t.$theme.primaryColor+";")},{default:a((()=>[b("负责")])),_:1},8,["style"])):(e(),s(h,{key:1,class:"client-is-participate"},{default:a((()=>[b("参与")])),_:1})),_(" 工单标题，添加 class 控制样式 "),k(h,{class:"client-ticket-title-text"},{default:a((()=>[b(C(i.ticket_title),1)])),_:2},1024)])),_:2},1024),k(u,{class:"client-ticket-status",style:v("background-color: "+i.status_color+";")},{default:a((()=>[b(C(i.status_name),1)])),_:2},1032,["style"])])),_:2},1024),_(" 服务类型和预约时间 "),k(u,{class:"client-service-info"},{default:a((()=>[k(u,{class:"client-service-type-row"},{default:a((()=>[_(" 工单紧急程度 "),k(u,{class:"client-urgency-type",style:v("background-color: "+n.getColorByIndex(i.urgency)+";")},{default:a((()=>[k(f,{options:c.dictData.urgency_degree,value:i.urgency},null,8,["options","value"])])),_:2},1032,["style"]),_(" 工单类型 "),k(u,{class:"client-service-type"},{default:a((()=>[b(C(i.type_name),1)])),_:2},1024),_(" 工单金额及状态 "),k(u,{class:"client-price-type"},{default:a((()=>[k(u,{class:"client-price-status"},{default:a((()=>[k(f,{options:c.dictData.payment_status,value:i.payment_status},null,8,["options","value"])])),_:2},1024),i.payment_status>0?(e(),s(h,{key:0,class:"client-price-tag"},{default:a((()=>[b("¥"+C(i.payment_amount),1)])),_:2},1024)):_("v-if",!0)])),_:2},1024)])),_:2},1024),_(" 工单描述 "),i.ticket_description?(e(),s(u,{key:0,class:"client-service-desc-row"},{default:a((()=>[k(h,{class:"client-service-desc-text"},{default:a((()=>[b(C(i.ticket_description),1)])),_:2},1024)])),_:2},1024)):_("v-if",!0),i.processing_total_duration?(e(),s(u,{key:1,class:"client-time-row"},{default:a((()=>[k(u,{class:"client-time-label"},{default:a((()=>[b("累计服务用时:")])),_:1}),k(u,{class:"client-time-value expected-sec"},{default:a((()=>[k(u,{class:"expected-date"},{default:a((()=>[b(C(n.formatServiceTime(i.processing_total_duration)),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)):_("v-if",!0),k(u,{class:"client-time-row"},{default:a((()=>[k(u,{class:"client-time-label"},{default:a((()=>[b("预约时间：")])),_:1}),k(u,{class:"client-time-value expected-sec"},{default:a((()=>[_(" 预约日期 "),k(u,{class:"expected-date"},{default:a((()=>[b(C(n.timeFormat(i.expected_date)||"待定"),1)])),_:2},1024),_(" 预约时间段 "),k(f,{options:c.dictData.service_period,value:i.expected_time_period},null,8,["options","value"])])),_:2},1024)])),_:2},1024)])),_:2},1024),_(" 客户信息 名称 电话 邮箱 单位  "),k(u,{class:"staff-client-info"},{default:a((()=>[k(u,{class:"staff-client-row"},{default:a((()=>[k(h,{class:"staff-client-name"},{default:a((()=>[b("👤"+C(i.user_name),1)])),_:2},1024),k(u,{class:"staff-client-phone",onClick:A((t=>n.callStaff(i.user_phone)),["stop"])},{default:a((()=>[k(h,{class:"staff-phone-icon"},{default:a((()=>[b("📞")])),_:1}),b(" "+C(i.user_desensitize_phone),1)])),_:2},1032,["onClick"])])),_:2},1024),i.user_email?(e(),s(u,{key:0,class:"staff-client-row"},{default:a((()=>[k(u,{class:"staff-client-email"},{default:a((()=>[k(h,{class:"staff-email-icon"},{default:a((()=>[b("📧")])),_:1}),b(" "+C(i.user_email),1)])),_:2},1024)])),_:2},1024)):_("v-if",!0),k(u,{class:"staff-client-row"},{default:a((()=>[i.user_company_id?(e(),s(u,{key:0,class:"staff-client-company"},{default:a((()=>[k(h,{class:"staff-company-icon"},{default:a((()=>[b("🏢")])),_:1}),b(" "+C(i.company_name),1)])),_:2},1024)):_("v-if",!0),_(" 地图导航按钮  "),_(' <view class="staff-client-map" v-if="item.latitude&&item.longitude">\r\n                            <view class="staff-map-btn" style="color: #2979ff;" @click.stop="navigateToLocation(item.address)">\r\n                                <uni-icons type="paperplane-filled" color="#ff9500" size="20"></uni-icons>\r\n                                导航\r\n                            </view>\r\n                        </view> ')])),_:2},1024)])),_:2},1024),_(" 完成时间信息 "),i.complete_time>0?(e(),s(u,{key:0,class:"client-complete-info"},{default:a((()=>[k(h,{class:"client-complete-label"},{default:a((()=>[b("完成时间:")])),_:1}),k(h,{class:"client-complete-time"},{default:a((()=>[b(C(n.timeFormat(i.complete_time)),1)])),_:2},1024)])),_:2},1024)):_("v-if",!0),_(" Location info "),i.address?(e(),s(u,{key:1,class:"client-location-info"},{default:a((()=>[k(h,{class:"client-location-icon"},{default:a((()=>[b("📍")])),_:1}),k(h,{class:"client-location-text"},{default:a((()=>[b(C(i.address),1)])),_:2},1024)])),_:2},1024)):_("v-if",!0),_(" 产品设备信息 "),i.product_id>0?(e(),s(u,{key:2,class:"client-staff-info"},{default:a((()=>[_(" 修改类名添加宫格样式 "),k(u,{class:"staff-product-grid grid-layout"},{default:a((()=>[k(u,{class:"staff-detail-item"},{default:a((()=>[k(h,{class:"staff-detail-label"},{default:a((()=>[b("产品设备:")])),_:1}),k(u,{class:"staff-detail-value"},{default:a((()=>[b(C(i.product_name||"未知"),1)])),_:2},1024)])),_:2},1024),i.product_code?(e(),s(u,{key:0,class:"staff-detail-item"},{default:a((()=>[k(h,{class:"staff-detail-label"},{default:a((()=>[b("编号:")])),_:1}),k(u,{class:"staff-detail-value"},{default:a((()=>[b(C(i.product_code),1)])),_:2},1024)])),_:2},1024)):_("v-if",!0),i.product_brand?(e(),s(u,{key:1,class:"staff-detail-item"},{default:a((()=>[k(h,{class:"staff-detail-label"},{default:a((()=>[b("品牌:")])),_:1}),k(u,{class:"staff-detail-value"},{default:a((()=>[b(C(i.product_brand),1)])),_:2},1024)])),_:2},1024)):_("v-if",!0),i.product_model?(e(),s(u,{key:2,class:"staff-detail-item"},{default:a((()=>[k(h,{class:"staff-detail-label"},{default:a((()=>[b("型号:")])),_:1}),k(u,{class:"staff-detail-value"},{default:a((()=>[b(C(i.product_model),1)])),_:2},1024)])),_:2},1024)):_("v-if",!0),k(u,{class:"staff-detail-item"},{default:a((()=>[k(h,{class:"staff-detail-label"},{default:a((()=>[b("购买日期:")])),_:1}),k(u,{class:"staff-detail-value"},{default:a((()=>[b(C(n.timeFormat(i.product_maintenance_end_time,"yyyy-mm-dd",!0)),1)])),_:2},1024)])),_:2},1024),k(u,{class:"staff-detail-item"},{default:a((()=>[k(h,{class:"staff-detail-label"},{default:a((()=>[b("维保到期:")])),_:1}),k(u,{class:"staff-detail-value"},{default:a((()=>[b(C(i.product_maintenance_end_time?n.timeFormat(i.product_maintenance_end_time):"长期"),1)])),_:2},1024)])),_:2},1024),k(u,{class:"staff-detail-item"},{default:a((()=>[k(h,{class:"staff-detail-label"},{default:a((()=>[b("维保状态:")])),_:1}),k(u,{class:"staff-detail-value"},{default:a((()=>[k(f,{options:c.dictData.maintenance_status,value:i.product_maintenance_state},null,8,["options","value"])])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)):_("v-if",!0),_(" 工单状态栏 "),k(u,{class:"client-ticket-footer"},{default:a((()=>[_(" 工单动作 ticket_action "),i.ticket_action?(e(),s(u,{key:0,class:"foot-ticket-status"},{default:a((()=>[b(C(i.ticket_action),1)])),_:2},1024)):_("v-if",!0),_(" 审核状态 audit_status"),k(u,{class:"foot-ticket-status"},{default:a((()=>[k(f,{options:c.dictData.audit_status,value:i.audit_status},null,8,["options","value"])])),_:2},1024),_(" 分配状态 allocation_status"),k(u,{class:"foot-ticket-status"},{default:a((()=>[k(f,{options:c.dictData.allocation_status,value:i.allocation_status},null,8,["options","value"])])),_:2},1024),_(" 费用确认 user_confirmed_amount "),i.payment_status>0?(e(),s(u,{key:1,class:"foot-ticket-status"},{default:a((()=>[k(f,{options:c.dictData.user_confirmed_amount,value:i.client_confirmed_amount},null,8,["options","value"])])),_:2},1024)):_("v-if",!0),_(" 关闭 close_status "),i.close_status>0?(e(),s(u,{key:2,class:"foot-ticket-status"},{default:a((()=>[i.cancel_status?(e(),s(h,{key:0},{default:a((()=>[b("已取消")])),_:1})):(e(),s(h,{key:1},{default:a((()=>[b("已关闭")])),_:1}))])),_:2},1024)):_("v-if",!0)])),_:2},1024),_(" Ticket serve rate "),k(u,{class:"client-ticket-serve"},{default:a((()=>{var t;return[_(" 已评价标签 "),(null==(t=i.actions.rated)?void 0:t.overall_satisfaction)?(e(),s(u,{key:0,class:"client-rated-tag"},{default:a((()=>[k(u,{class:"client-rated-stars"},{default:a((()=>[b(" 评价均值 "),k(m,{count:5,modelValue:i.actions.rated.average,"onUpdate:modelValue":t=>i.actions.rated.average=t,readonly:"",disabled:"",size:"20","active-color":"#fabd1d","inactive-color":"#dbdbdb","inactive-icon":"star-fill"},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024),k(h,{style:{color:"#fabd1d"}},{default:a((()=>[b(C(i.actions.rated.average)+"分",1)])),_:2},1024)])),_:2},1024)):_("v-if",!0)]})),_:2},1024),_(" 操作按钮靠右显示 金额设置 金额变更 催确认 催支付 确认支付 接单按钮 取消按钮 完成 关闭 改派 留言回复 "),i.is_participate?(e(),s(u,{key:3,class:"client-action-row"},{default:a((()=>[_(" 变更紧急 "),_(' <view class="client-action-btn" v-if="item.close_status == 0">\r\n                        <button @click.stop="showChangeUrgencyDialog(item.id)" >\r\n                            变更优先级\r\n                        </button>\r\n                    </view> '),_(" 修改工单状态 "),0==i.close_status&&0==i.actions.status.is_final?(e(),s(u,{key:0,class:"client-action-btn"},{default:a((()=>[k(w,{onClick:A((t=>n.showChangeStatusDialog(i.id)),["stop"])},{default:a((()=>[b(" 变更状态 ")])),_:2},1032,["onClick"])])),_:2},1024)):_("v-if",!0),_(" 重启工单 "),i.close_status>0&&0==i.cancel_status&&i.actions.config.restart_ticket&&i.actions.config.assignee_restart?(e(),s(u,{key:1,class:"client-action-btn"},{default:a((()=>[k(w,{onClick:A((t=>n.showRestartDialog(i.id)),["stop"])},{default:a((()=>[b(" 重启工单 ")])),_:2},1032,["onClick"])])),_:2},1024)):_("v-if",!0),_(" 确认支付 canConfirmPay "),0==i.close_status&&1==i.payment_status&&1==i.client_confirmed_amount&&i.payment_amount>0?(e(),s(u,{key:2,class:"client-action-btn"},{default:a((()=>[k(w,{onClick:A((t=>n.showConfirmPayDialog(i.id,i.payment_amount)),["stop"])},{default:a((()=>[b(" 确认支付 ")])),_:2},1032,["onClick"])])),_:2},1024)):_("v-if",!0),_(" 通知支付 canNotifyPay "),0==i.close_status&&1==i.payment_status&&1==i.client_confirmed_amount&&i.payment_amount>0?(e(),s(u,{key:3,class:"client-action-btn"},{default:a((()=>[k(w,{onClick:A((t=>n.showNotifyPayDialog(i.id,i.payment_amount)),["stop"])},{default:a((()=>[b(" 提醒支付 ")])),_:2},1032,["onClick"])])),_:2},1024)):_("v-if",!0),_(" 提醒确认费用 canNotifyConfirmAmount "),_(' <view class="client-action-btn"\r\n                        v-if="item.close_status==0&&item.payment_status==1 && item.client_confirmed_amount==0 && item.payment_amount>0">\r\n                        <button @click.stop="showNotifyConfirmAmountDialog(item.id,item.payment_amount)" >\r\n                            提醒确认费用\r\n                        </button>\r\n                     </view> '),_(" 修改费用 canUpdateAmount "),_(' <view class="client-action-btn" v-if="item.payment_status != 0 && item.close_status == 0">\r\n                        <button @click.stop="showUpdateAmountDialog(item.id,item.payment_amount)" >\r\n                            修改费用\r\n                        </button>\r\n                     </view> '),_(" 变更付费状态 canSetPay "),0==i.close_status&&0==i.payment_status?(e(),s(u,{key:4,class:"client-action-btn"},{default:a((()=>[_(" 不需付费 可改为 需付费并设置付费金额 "),k(w,{onClick:A((t=>n.showSetPayDialog(i.id)),["stop"])},{default:a((()=>[b(" 设置金额 ")])),_:2},1032,["onClick"])])),_:2},1024)):_("v-if",!0),_(" 改派 未关闭 "),0==i.close_status?(e(),s(u,{key:5,class:"client-action-btn"},{default:a((()=>[k(w,{onClick:A((t=>n.showChangeDispatchDialog(i.id)),["stop"])},{default:a((()=>[b(" 改派 ")])),_:2},1032,["onClick"])])),_:2},1024)):_("v-if",!0),_(" 完成 未关闭 有下个状态节点并为终态 "),0==i.close_status&&i.actions.next_status&&1==i.actions.next_status.is_final?(e(),s(u,{key:6,class:"client-action-btn"},{default:a((()=>[k(w,{onClick:A((t=>n.showCompleteDialog(i.id)),["stop"])},{default:a((()=>[b(" 完成 ")])),_:2},1032,["onClick"])])),_:2},1024)):_("v-if",!0),_(" 取消按钮 "),0==i.close_status&&0==i.cancel_status&&0==i.actions.status.is_final&&!i.complete_time&&i.actions.config.assignee_close&&0==i.actions.next_status.is_final?(e(),s(u,{key:7,class:"client-action-btn"},{default:a((()=>[k(w,{onClick:A((t=>n.showCancelDialog(i.id)),["stop"])},{default:a((()=>[b(" 取消 ")])),_:2},1032,["onClick"])])),_:2},1024)):_("v-if",!0),_(" 恢复工单按钮 "),i.close_status>0&&i.cancel_status>0&&0==i.actions.status.is_final&&!i.complete_time&&i.actions.config.assignee_close&&i.actions.config.restore_ticket?(e(),s(u,{key:8,class:"client-action-btn"},{default:a((()=>[k(w,{onClick:A((t=>n.showRestoreDialog(i.id)),["stop"])},{default:a((()=>[b(" 撤回取消 ")])),_:2},1032,["onClick"])])),_:2},1024)):_("v-if",!0)])),_:2},1024)):(e(),s(u,{key:4,class:"client-action-row"},{default:a((()=>[_(" 重启工单 "),i.close_status>0&&0==i.cancel_status&&i.actions.config.restart_ticket&&i.actions.config.assignee_restart?(e(),s(u,{key:0,class:"client-action-btn"},{default:a((()=>[k(w,{onClick:A((t=>n.showRestartDialog(i.id)),["stop"])},{default:a((()=>[b(" 重启工单 ")])),_:2},1032,["onClick"])])),_:2},1024)):_("v-if",!0),_(" 退出参与 "),0==i.cancel_status?(e(),s(u,{key:1,class:"client-action-btn"},{default:a((()=>[k(w,{onClick:A((t=>n.showCanQuitDialog(i.id)),["stop"])},{default:a((()=>[b(" 退出参与 ")])),_:2},1032,["onClick"])])),_:2},1024)):_("v-if",!0)])),_:2},1024))])),_:2},1032,["onClick"])))),128)),_(" Empty state "),0!=c.ticketList.length||c.loading?_("v-if",!0):(e(),s(u,{key:0,class:"client-empty-state"},{default:a((()=>[k(N,{src:ht,mode:"aspectFit"}),k(u,null,{default:a((()=>[_(" 修改部分 "),c.status_id?(e(),s(h,{key:0},{default:a((()=>{var t;return[b(" 暂无"+C((null==(t=c.statusTabs.find((t=>t.id===c.status_id)))?void 0:t.label)||"")+"记录 ",1)]})),_:1})):(e(),s(h,{key:1},{default:a((()=>[b("您还没有工单记录")])),_:1})),_(' <button class="client-create-empty-btn" @click="goToCreate">立即创建工单</button> ')])),_:1})])),_:1}))])),_:1}),_(" 悬浮按钮 "),k(u,null,{default:a((()=>[_(" 提交工单悬浮按钮 "),_(' <view \r\n                class="floating-create-btn" \r\n                @click="goToCreate"\r\n                @touchstart="handleTouchStart"\r\n                @touchmove="handleTouchMove"\r\n                @touchend="handleTouchEnd"\r\n                :style="{ right: createBtnRight + \'rpx\', bottom: createBtnBottom + \'rpx\' }"\r\n            >\r\n                <u-icon name="plus" size="30"/>\r\n            </view> '),_(" 返回顶部悬浮按钮 "),c.showBackToTop?(e(),s(u,{key:0,class:"floating-back-to-top-btn",onClick:n.backToTop,onTouchstart:n.handleTouchStart,onTouchmove:n.handleTouchMove,onTouchend:n.handleTouchEnd,style:v({right:c.backToTopBtnRight+"rpx",bottom:c.backToTopBtnBottom+"rpx"})},{default:a((()=>[k(d,{name:"arrow-up",size:"30",color:"#fff"})])),_:1},8,["onClick","onTouchstart","onTouchmove","onTouchend","style"])):_("v-if",!0)])),_:1}),_(" 组件 "),k(u,null,{default:a((()=>[_(" 选择管理员组件 "),k(z,{title:"选择指派人",show:c.showSelectedAdminSingle,defaultSelectedAdmin:c.defaultSelectedAdmin,onConfirm:n.handleConfirmAdminSingle},null,8,["show","defaultSelectedAdmin","onConfirm"]),_(" 状态节点组件 "),k(F,{title:c.popStatusTitle,show:c.showStatusSelector,statusList:c.selectorStatusList,onConfirm:n.handleConfirmStatusSelector,onClosePopup:i[1]||(i[1]=t=>c.showStatusSelector=!1)},null,8,["title","show","statusList","onConfirm"])])),_:1}),_(" 完成工单弹窗 "),k(E,{modelValue:c.completePopupVisible,"onUpdate:modelValue":i[5]||(i[5]=t=>c.completePopupVisible=t),mode:"center","border-radius":"16",width:"650rpx","close-on-click-overlay":!1},{default:a((()=>[k(u,{class:"client-rating-popup"},{default:a((()=>[k(u,{class:"client-rating-header"},{default:a((()=>[k(h,{class:"client-rating-title"},{default:a((()=>[b("完成工单")])),_:1}),k(h,{class:"client-rating-close",onClick:i[2]||(i[2]=t=>c.completePopupVisible=!1)},{default:a((()=>[b("×")])),_:1})])),_:1}),k(u,{class:"client-rating-content"},{default:a((()=>[k(u,{style:{color:"#ff1a00"}},{default:a((()=>[b(" 是否确定完成该工单？ ")])),_:1}),k(u,{class:"mt-[30rpx]"},{default:a((()=>[k(h,{class:"client-rating-label mb-[15rpx]"},{default:a((()=>[b("分享处理方法步骤")])),_:1}),k(Y,{class:"client-rating-suggestion",modelValue:c.completeText,"onUpdate:modelValue":i[3]||(i[3]=t=>c.completeText=t),placeholder:"您的分享将帮助团队提升服务质量"},null,8,["modelValue"])])),_:1})])),_:1}),k(u,{class:"client-rating-footer"},{default:a((()=>[k(w,{class:"client-rating-cancel",onClick:i[4]||(i[4]=t=>c.completePopupVisible=!1)},{default:a((()=>[b("取消")])),_:1}),k(w,{class:"client-rating-submit",onClick:n.confirmComplete},{default:a((()=>[b("确定完成")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["modelValue"]),_(" 取消工单弹窗 "),k(E,{modelValue:c.cancelPopupVisible,"onUpdate:modelValue":i[9]||(i[9]=t=>c.cancelPopupVisible=t),mode:"center","border-radius":"16",width:"650rpx","close-on-click-overlay":!1},{default:a((()=>[k(u,{class:"client-cancel-popup"},{default:a((()=>[k(u,{class:"client-cancel-header"},{default:a((()=>[k(h,{class:"client-cancel-title"},{default:a((()=>[b("取消工单")])),_:1}),k(h,{class:"client-cancel-close",onClick:i[6]||(i[6]=t=>c.cancelPopupVisible=!1)},{default:a((()=>[b("×")])),_:1})])),_:1}),k(u,{class:"client-cancel-content"},{default:a((()=>[k(u,{class:"client-cancel-tips"},{default:a((()=>[b("确定要取消该工单吗？取消后无法恢复")])),_:1}),k(u,{class:"client-cancel-reason-select"},{default:a((()=>[k(h,{class:"client-reason-label"},{default:a((()=>[b("请选择取消原因：")])),_:1}),k(u,{class:"client-reason-options"},{default:a((()=>[k(q,{onChange:n.cancelReasonRadioChange},{default:a((()=>[(e(!0),y(T,null,g(c.cancelReasons,((i,l)=>(e(),s(O,{class:"uni-list-cell uni-list-cell-pd",key:i,style:{display:"inline-flex","align-items":"center","margin-right":"20rpx"}},{default:a((()=>[k(u,null,{default:a((()=>[k(Q,{value:i,color:t.$theme.primaryColor,checked:l===c.currentReason},null,8,["value","color","checked"])])),_:2},1024),k(u,null,{default:a((()=>[b(C(i),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1},8,["onChange"]),_(" 当选择其他原因时显示输入框 "),c.showCustomReasonInput?(e(),s(u,{key:0},{default:a((()=>[k(Y,{class:"client-custom-reason-input",modelValue:c.customReason,"onUpdate:modelValue":i[7]||(i[7]=t=>c.customReason=t),placeholder:"请输入具体取消原因",onInput:n.handleCustomReasonInput},null,8,["modelValue","onInput"])])),_:1})):_("v-if",!0)])),_:1})])),_:1})])),_:1}),k(u,{class:"client-cancel-footer"},{default:a((()=>[k(w,{class:"client-cancel-btn-secondary",onClick:i[8]||(i[8]=t=>c.cancelPopupVisible=!1)},{default:a((()=>[b("返回")])),_:1}),k(w,{class:"client-cancel-btn-primary",onClick:n.confirmCancelTicket},{default:a((()=>[b("确认取消")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["modelValue"]),_(" 设置付费金额弹窗 "),k(E,{modelValue:c.setMoneyPopupVisible,"onUpdate:modelValue":i[13]||(i[13]=t=>c.setMoneyPopupVisible=t),mode:"center","border-radius":"16",width:"650rpx","close-on-click-overlay":!1},{default:a((()=>[k(u,{class:"client-cancel-popup"},{default:a((()=>[k(u,{class:"client-cancel-header"},{default:a((()=>[k(h,{class:"client-cancel-title"},{default:a((()=>[b("设置付费金额")])),_:1}),k(h,{class:"client-cancel-close",onClick:i[10]||(i[10]=t=>c.setMoneyPopupVisible=!1)},{default:a((()=>[b("×")])),_:1})])),_:1}),k(u,{class:"client-cancel-content"},{default:a((()=>[k(u,{class:"client-cancel-tips"},{default:a((()=>[b("请输入工单需要支付的金额")])),_:1}),k(u,{class:"client-cancel-reason-select"},{default:a((()=>[_(' <text class="client-reason-label">请输入金额：</text> '),k(u,{class:"client-reason-options"},{default:a((()=>[k(J,{type:"number",primaryColor:t.$theme.primaryColor,modelValue:c.setMoney,"onUpdate:modelValue":i[11]||(i[11]=t=>c.setMoney=t),placeholder:"请输入金额"},null,8,["primaryColor","modelValue"])])),_:1})])),_:1})])),_:1}),k(u,{class:"client-cancel-footer"},{default:a((()=>[k(w,{class:"client-cancel-btn-secondary",onClick:i[12]||(i[12]=t=>c.setMoneyPopupVisible=!1)},{default:a((()=>[b("返回")])),_:1}),k(w,{class:"client-cancel-btn-primary",onClick:n.confirmSetPayTicket},{default:a((()=>[b("确认设置")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["modelValue"]),_(" 修改工单金额弹窗 "),k(E,{modelValue:c.updateAmountPopupVisible,"onUpdate:modelValue":i[17]||(i[17]=t=>c.updateAmountPopupVisible=t),mode:"center","border-radius":"16",width:"650rpx","close-on-click-overlay":!1},{default:a((()=>[k(u,{class:"client-cancel-popup"},{default:a((()=>[k(u,{class:"client-cancel-header"},{default:a((()=>[k(h,{class:"client-cancel-title"},{default:a((()=>[b("修改工单费用")])),_:1}),k(h,{class:"client-cancel-close",onClick:i[14]||(i[14]=t=>c.updateAmountPopupVisible=!1)},{default:a((()=>[b("×")])),_:1})])),_:1}),k(u,{class:"client-cancel-content"},{default:a((()=>[k(u,{class:"client-cancel-tips"},{default:a((()=>[b("当前工单需付费金额"),k(h,{style:{color:"#ff1a00"}},{default:a((()=>[b("￥"+C(c.oldMoeny),1)])),_:1})])),_:1}),k(u,{class:"client-cancel-tips"},{default:a((()=>[b("请输入修改后的费用")])),_:1}),k(u,{class:"client-cancel-reason-select"},{default:a((()=>[k(u,{class:"client-reason-options"},{default:a((()=>[k(J,{primaryColor:t.$theme.primaryColor,type:"number",modelValue:c.updateMoney,"onUpdate:modelValue":i[15]||(i[15]=t=>c.updateMoney=t),placeholder:"请输入金额"},null,8,["primaryColor","modelValue"])])),_:1})])),_:1})])),_:1}),k(u,{class:"client-cancel-footer"},{default:a((()=>[k(w,{class:"client-cancel-btn-secondary",onClick:i[16]||(i[16]=t=>c.updateAmountPopupVisible=!1)},{default:a((()=>[b("返回")])),_:1}),k(w,{class:"client-cancel-btn-primary",onClick:n.confirmUpdateAmountTicket},{default:a((()=>[b("确认修改")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["modelValue"]),_(" 改派工单弹窗 显示两个按钮 改派指定人 释放到抢单大厅 "),k(E,{modelValue:c.changeDispatchPopupVisible,"onUpdate:modelValue":i[21]||(i[21]=t=>c.changeDispatchPopupVisible=t),mode:"center","border-radius":"16",width:"650rpx","close-on-click-overlay":!1},{default:a((()=>[k(u,{class:"client-cancel-popup"},{default:a((()=>[k(u,{class:"client-cancel-header"},{default:a((()=>[k(h,{class:"client-cancel-title"},{default:a((()=>[b("请选择工单改派方式")])),_:1}),k(h,{class:"client-cancel-close",onClick:i[18]||(i[18]=t=>c.changeDispatchPopupVisible=!1)},{default:a((()=>[b("×")])),_:1})])),_:1}),k(u,{class:"client-cancel-content"},{default:a((()=>[_(" 改派指定人按钮 "),k(w,{class:"client-cancel-btn-primary",onClick:i[19]||(i[19]=t=>n.changeToDispatch(1)),style:{"margin-bottom":"20rpx"}},{default:a((()=>[b(" 改派指定人 ")])),_:1}),_(" 释放到抢单大厅按钮 "),k(w,{class:"client-cancel-btn-secondary",onClick:i[20]||(i[20]=t=>n.changeToDispatch(0))},{default:a((()=>[b(" 释放到抢单大厅 ")])),_:1})])),_:1})])),_:1})])),_:1},8,["modelValue"]),_(" 新增更多选项弹窗 "),k(E,{modelValue:c.showMorePopup,"onUpdate:modelValue":i[22]||(i[22]=t=>c.showMorePopup=t),mode:"bottom","border-radius":"16","close-on-click-overlay":!0,"close-icon":"close-circle","close-icon-size":"36",closeable:!0},{default:a((()=>[k(u,{class:"more-options"},{default:a((()=>[(e(!0),y(T,null,g(c.statusTabs,((t,i)=>(e(),s(u,{key:t.value,class:L(["more-option-item",{selected:c.activeTab===t.value}]),onClick:e=>{n.switchTab(t.value,t.id),c.showMorePopup=!1}},{default:a((()=>[b(C(t.label)+"("+C(t.count||0)+") ",1)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1},8,["modelValue"]),_(" 使用新的公共消息详情弹窗组件 "),k(K,{ref:"messageDetailPopupRef"},null,512)])),_:1})}],["__scopeId","data-v-de9da5d1"]]);export{yt as default};
